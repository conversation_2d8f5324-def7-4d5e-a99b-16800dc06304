{"name": "@tsparticles/updater-wobble", "version": "3.8.1", "description": "tsParticles particles wobble updater", "homepage": "https://particles.js.org", "repository": {"type": "git", "url": "git+https://github.com/tsparticles/tsparticles.git", "directory": "updaters/wobble"}, "keywords": ["front-end", "frontend", "tsparticles", "particles.js", "<PERSON><PERSON>s", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles-bg", "particles-bg-vue", "particles-ts", "particles.ts", "react-particles-js", "react-particles.js", "react-particles", "react", "reactjs", "vue-particles", "ngx-particles", "angular-particles", "particleground", "vue", "v<PERSON><PERSON><PERSON>", "preact", "preactjs", "j<PERSON>y", "<PERSON><PERSON>s", "angular", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "confetti", "canvas", "fireworks", "fireworks-js", "confetti-js", "confettijs", "<PERSON>js", "canvas-confetti", "tsparticles-plugin", "@tsparticles/updater"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tsparticles/tsparticles/issues"}, "sideEffects": false, "jsdelivr": "tsparticles.updater.wobble.min.js", "unpkg": "tsparticles.updater.wobble.min.js", "browser": "browser/index.js", "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts", "browser": "./browser/index.js", "import": "./esm/index.js", "require": "./cjs/index.js", "umd": "./umd/index.js", "default": "./cjs/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@tsparticles/engine": "3.8.1"}, "publishConfig": {"access": "public"}}