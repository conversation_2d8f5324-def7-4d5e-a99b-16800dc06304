'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { GlitchTextEffectProps } from '../../../types/ui';
import { brandColors, brandTypography } from '../../../styles/brand';

/**
 * GlitchTextEffect Component
 * 
 * Creates glitch text effects for modern, edgy branding elements.
 * Features customizable intensity, colors, and animation triggers.
 * 
 * @example
 * ```tsx
 * <GlitchTextEffect
 *   text="APOTHECARY FARMS"
 *   intensity="high"
 *   trigger="hover"
 *   colors={['#2FB886', '#ff0080', '#00ff80']}
 *   fontSize="4rem"
 * />
 * ```
 */
const GlitchTextEffect: React.FC<GlitchTextEffectProps> = ({
  text,
  intensity = 'medium',
  trigger = 'hover',
  colors = [brandColors.apothecary, '#ff0080', '#00ff80'],
  fontSize = '2rem',
  fontWeight = 700,
  className = '',
  style,
}) => {
  const [isGlitching, setIsGlitching] = useState(trigger === 'continuous');
  const [glitchText, setGlitchText] = useState(text);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: trigger === 'scroll',
  });

  // Glitch characters for text corruption
  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';
  const numbers = '0123456789';
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

  // Get intensity settings
  const getIntensitySettings = () => {
    switch (intensity) {
      case 'low':
        return {
          glitchDuration: 100,
          glitchInterval: 2000,
          corruptionChance: 0.1,
          maxCorruptedChars: 2,
        };
      case 'medium':
        return {
          glitchDuration: 200,
          glitchInterval: 1500,
          corruptionChance: 0.2,
          maxCorruptedChars: 4,
        };
      case 'high':
        return {
          glitchDuration: 300,
          glitchInterval: 1000,
          corruptionChance: 0.3,
          maxCorruptedChars: 6,
        };
      default:
        return {
          glitchDuration: 200,
          glitchInterval: 1500,
          corruptionChance: 0.2,
          maxCorruptedChars: 4,
        };
    }
  };

  const settings = getIntensitySettings();

  // Generate corrupted text
  const generateGlitchedText = () => {
    const chars = text.split('');
    const corruptedIndices = new Set<number>();
    
    // Randomly select characters to corrupt
    const numToCorrupt = Math.min(
      Math.floor(Math.random() * settings.maxCorruptedChars) + 1,
      chars.length
    );
    
    while (corruptedIndices.size < numToCorrupt) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      if (chars[randomIndex] !== ' ') {
        corruptedIndices.add(randomIndex);
      }
    }
    
    // Replace selected characters with glitch characters
    corruptedIndices.forEach(index => {
      const charSets = [glitchChars, numbers, letters];
      const randomSet = charSets[Math.floor(Math.random() * charSets.length)];
      chars[index] = randomSet[Math.floor(Math.random() * randomSet.length)];
    });
    
    return chars.join('');
  };

  // Start glitch effect
  const startGlitch = () => {
    if (intervalRef.current) return;
    
    setIsGlitching(true);
    
    intervalRef.current = setInterval(() => {
      setGlitchText(generateGlitchedText());
      
      // Reset to original text after glitch duration
      timeoutRef.current = setTimeout(() => {
        setGlitchText(text);
      }, settings.glitchDuration);
    }, settings.glitchInterval);
  };

  // Stop glitch effect
  const stopGlitch = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsGlitching(false);
    setGlitchText(text);
  };

  // Handle trigger effects
  useEffect(() => {
    if (trigger === 'continuous') {
      startGlitch();
    } else if (trigger === 'scroll' && inView) {
      startGlitch();
      // Stop after a few cycles
      setTimeout(stopGlitch, 5000);
    }

    return () => {
      stopGlitch();
    };
  }, [trigger, inView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopGlitch();
    };
  }, []);

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      startGlitch();
    }
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      stopGlitch();
    }
  };

  // Generate CSS for glitch effect
  const glitchStyles = {
    position: 'relative' as const,
    fontSize,
    fontWeight,
    fontFamily: brandTypography.fonts.sans,
    color: colors[0],
    cursor: trigger === 'hover' ? 'pointer' : 'default',
    ...style,
  };

  const beforeAfterStyles = {
    content: `"${glitchText}"`,
    position: 'absolute' as const,
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'transparent',
  };

  return (
    <motion.div
      ref={ref}
      className={`glitch-text-effect ${className}`}
      style={glitchStyles}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Main text */}
      <span className="relative z-10">{glitchText}</span>
      
      {/* Glitch layers */}
      {isGlitching && (
        <>
          {/* Red glitch layer */}
          <motion.span
            className="absolute top-0 left-0 z-0"
            style={{
              color: colors[1] || '#ff0080',
              clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)',
            }}
            animate={{
              x: [-2, 2, -1, 1, 0],
              y: [0, -1, 1, 0, -1],
            }}
            transition={{
              duration: 0.1,
              repeat: Infinity,
              repeatType: 'mirror',
            }}
          >
            {glitchText}
          </motion.span>
          
          {/* Blue glitch layer */}
          <motion.span
            className="absolute top-0 left-0 z-0"
            style={{
              color: colors[2] || '#00ff80',
              clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)',
            }}
            animate={{
              x: [1, -2, 2, -1, 0],
              y: [1, 0, -1, 1, 0],
            }}
            transition={{
              duration: 0.1,
              repeat: Infinity,
              repeatType: 'mirror',
              delay: 0.05,
            }}
          >
            {glitchText}
          </motion.span>
          
          {/* Scan lines */}
          <motion.div
            className="absolute inset-0 z-20 pointer-events-none"
            style={{
              background: `repeating-linear-gradient(
                0deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.03) 2px,
                rgba(255, 255, 255, 0.03) 4px
              )`,
            }}
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 0.1,
              repeat: Infinity,
              repeatType: 'mirror',
            }}
          />
          
          {/* Random noise overlay */}
          <motion.div
            className="absolute inset-0 z-10 pointer-events-none"
            style={{
              background: `radial-gradient(circle, transparent 50%, rgba(255, 255, 255, 0.1) 50%)`,
              backgroundSize: '4px 4px',
            }}
            animate={{
              backgroundPosition: ['0px 0px', '4px 4px', '0px 4px', '4px 0px'],
              opacity: [0, 0.3, 0, 0.2, 0],
            }}
            transition={{
              duration: 0.2,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
        </>
      )}
      
      {/* Glow effect */}
      {isGlitching && (
        <motion.div
          className="absolute inset-0 z-0"
          style={{
            color: colors[0],
            filter: 'blur(4px)',
            opacity: 0.5,
          }}
          animate={{
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 0.2,
            repeat: Infinity,
            repeatType: 'mirror',
          }}
        >
          {glitchText}
        </motion.div>
      )}
    </motion.div>
  );
};

export default GlitchTextEffect;
