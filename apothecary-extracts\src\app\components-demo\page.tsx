'use client';

import { useState } from 'react';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';

// Import all our new components
import TextWithGlitchEffect from '@/components/ui/TextWithGlitchEffect';
import AnnouncementBar from '@/components/ui/AnnouncementBar';
import { ExampleFeatureIcons } from '@/components/ui/FeatureIcons';
import { ExampleTabbedGrid } from '@/components/ui/Tabs';
import { ExampleGridCards } from '@/components/ui/GridCards';
import { ExampleSplitMediaText } from '@/components/ui/SplitMediaText';
import { ExampleFAQAccordion } from '@/components/ui/FAQAccordion';
import { ExampleCardSlider } from '@/components/ui/CardSlider';
import FilterableStrainCards from '@/components/ui/FilterableStrainCards';
import { ExampleMasonryGrid } from '@/components/ui/MasonryGrid';
import { ExampleVerticalTimeline } from '@/components/ui/VerticalTimeline';
import { ExampleHeroVideoLoop } from '@/components/ui/HeroVideoLoop';
import { ParallaxSection } from '@/components/ui/ParallaxBanner';
import { sampleStrains } from '@/data/sampleStrains';

export default function ComponentsDemo() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);

  return (
    <>
      {!isAgeVerified && (
        <AgeVerification onVerified={() => setIsAgeVerified(true)} />
      )}

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          {/* Announcement Bar */}
          <AnnouncementBar
            message="🎉 New UI Components Demo! Explore our latest interactive elements and animations."
            ctaText="Learn More"
            ctaLink="#components"
            type="promotion"
            dismissible={true}
          />

          <Navigation />

          <main>
            {/* Hero Video Loop Section */}
            <section id="hero" className="mb-16">
              <ExampleHeroVideoLoop />
            </section>

            {/* Components Showcase */}
            <section id="components" className="py-16">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                  <h1 className="text-4xl sm:text-5xl font-serif font-bold text-foreground mb-4">
                    <TextWithGlitchEffect 
                      text="UI Components Demo"
                      glitchIntensity="medium"
                      triggerOnHover={true}
                      className="text-primary-600"
                    />
                  </h1>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    Explore our comprehensive collection of interactive React components designed for modern cannabis businesses.
                  </p>
                </div>

                {/* Feature Icons */}
                <div className="mb-20">
                  <h2 className="text-3xl font-serif font-bold text-center mb-8">Feature Icons</h2>
                  <ExampleFeatureIcons />
                </div>

                {/* Card Slider */}
                <div className="mb-20">
                  <ExampleCardSlider />
                </div>

                {/* Tabbed Grid */}
                <div className="mb-20">
                  <ExampleTabbedGrid />
                </div>

                {/* Grid Cards */}
                <div className="mb-20">
                  <ExampleGridCards />
                </div>

                {/* Filterable Strain Cards */}
                <div className="mb-20">
                  <h2 className="text-3xl font-serif font-bold text-center mb-8">Filterable Strain Cards</h2>
                  <FilterableStrainCards
                    strains={sampleStrains}
                    showFilters={true}
                    defaultFilter="all"
                    sortBy="name"
                  />
                </div>

                {/* Split Media Text */}
                <div className="mb-20">
                  <ExampleSplitMediaText />
                </div>

                {/* Masonry Grid */}
                <div className="mb-20">
                  <ExampleMasonryGrid />
                </div>

                {/* FAQ Accordion */}
                <div className="mb-20">
                  <ExampleFAQAccordion />
                </div>

                {/* Timeline */}
                <div className="mb-20">
                  <ExampleVerticalTimeline />
                </div>
              </div>
            </section>

            {/* Parallax Section */}
            <ParallaxSection />

            {/* Component Features Overview */}
            <section className="py-16 bg-cream-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-serif font-bold text-foreground mb-4">
                    Component Features
                  </h2>
                  <p className="text-lg text-gray-600">
                    Built with modern technologies and best practices
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Framer Motion</h3>
                    <p className="text-gray-600">Smooth animations and micro-interactions powered by Framer Motion.</p>
                  </div>

                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-sage-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">TypeScript</h3>
                    <p className="text-gray-600">Fully typed components with excellent developer experience.</p>
                  </div>

                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-gold-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Responsive</h3>
                    <p className="text-gray-600">Mobile-first design that works perfectly on all devices.</p>
                  </div>

                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Customizable</h3>
                    <p className="text-gray-600">Highly configurable with props for different use cases.</p>
                  </div>

                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-sage-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Accessible</h3>
                    <p className="text-gray-600">Built with accessibility in mind, following WCAG guidelines.</p>
                  </div>

                  <div className="bg-cream-50 p-6 rounded-xl shadow-soft">
                    <div className="w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-gold-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Performance</h3>
                    <p className="text-gray-600">Optimized for performance with lazy loading and efficient animations.</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Call to Action */}
            <section className="py-16 bg-primary-600 text-cream-50">
              <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
                <h2 className="text-3xl font-serif font-bold mb-4">
                  Ready to Implement These Components?
                </h2>
                <p className="text-xl text-cream-200 mb-8">
                  All components are production-ready and can be easily integrated into your cannabis business website.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="bg-gold-500 text-primary-800 px-8 py-3 rounded-lg font-semibold hover:bg-gold-400 transition-colors duration-200">
                    View Documentation
                  </button>
                  <button className="border-2 border-cream-50 text-cream-50 px-8 py-3 rounded-lg font-semibold hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200">
                    Download Components
                  </button>
                </div>
              </div>
            </section>
          </main>
        </div>
      )}
    </>
  );
}
