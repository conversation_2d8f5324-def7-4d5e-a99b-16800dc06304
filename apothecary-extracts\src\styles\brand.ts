/**
 * Apothecary Farms Brand Utility System
 * Centralized brand tokens for colors, gradients, shadows, and animations
 */

// Brand Colors
export const brandColors = {
  // Primary Green Palette
  primary: {
    50: '#f0f9f4',
    100: '#dcf2e4',
    200: '#bce5cd',
    300: '#8dd1a8',
    400: '#57b67c',
    500: '#359a5a', // Main brand green
    600: '#267d47',
    700: '#1f633a',
    800: '#1b4332',
    900: '#163a2b',
  },
  
  // Apothecary Green (from memory - the mint green mentioned)
  apothecary: '#2FB886',
  
  // Cream/Neutral Palette
  cream: {
    50: '#fefefe',
    100: '#f8f6f0',
    200: '#f4f1e8',
    300: '#ede8db',
    400: '#e4dcc8',
    500: '#d8cdb0',
  },
  
  // Gold/Amber Palette (for extracts/rosin)
  gold: {
    50: '#fefcf7',
    100: '#fdf8ed',
    200: '#f9eed5',
    300: '#f4e4bc',
    400: '#edd5a3',
    500: '#d4a574',
  },
  
  // Charcoal/<PERSON> Palette
  charcoal: {
    50: '#f8f9fa',
    100: '#e9ecef',
    200: '#dee2e6',
    300: '#ced4da',
    400: '#adb5bd',
    500: '#6c757d',
    600: '#495057',
    700: '#343a40',
    800: '#2d3436',
    900: '#212529',
  },
  
  // Sage Green Palette
  sage: {
    50: '#f7f9f8',
    100: '#eef2f0',
    200: '#dde5e1',
    300: '#c4d2ca',
    400: '#a5b8ad',
    500: '#95a99c',
  },
} as const;

// Terpene-Inspired Color Palettes
export const terpeneColors = {
  // Pineapple Express - Orange to Yellow
  pineapple: {
    from: '#ff8c00',
    to: '#ffd700',
    gradient: 'linear-gradient(135deg, #ff8c00 0%, #ffd700 100%)',
  },
  
  // GMO - Blue Frost
  gmo: {
    from: '#4a90e2',
    to: '#87ceeb',
    gradient: 'linear-gradient(135deg, #4a90e2 0%, #87ceeb 100%)',
  },
  
  // Rosin - Gold to Amber
  rosin: {
    from: '#d4a574',
    to: '#ff8c00',
    gradient: 'linear-gradient(135deg, #d4a574 0%, #ff8c00 100%)',
  },
  
  // Flower - Green to Yellow
  flower: {
    from: '#359a5a',
    to: '#ffd700',
    gradient: 'linear-gradient(135deg, #359a5a 0%, #ffd700 100%)',
  },
  
  // Extract - Amber to Purple
  extract: {
    from: '#ff8c00',
    to: '#9370db',
    gradient: 'linear-gradient(135deg, #ff8c00 0%, #9370db 100%)',
  },
  
  // Particle Palette - Soft colors for backgrounds
  particles: {
    yellow: '#fff9c4',
    lime: '#d4edda',
    lavender: '#e2d5f1',
  },
} as const;

// Brand Gradients
export const brandGradients = {
  // Main brand gradients
  cannabis: 'linear-gradient(135deg, #1b4332 0%, #267d47 50%, #95a99c 100%)',
  gold: 'linear-gradient(135deg, #d4a574 0%, #edd5a3 100%)',
  sage: 'linear-gradient(135deg, #95a99c 0%, #c4d2ca 100%)',
  
  // Context-based gradients
  hero: 'linear-gradient(135deg, #1b4332 0%, #163a2b 100%)',
  card: 'linear-gradient(135deg, #f8f6f0 0%, #ede8db 100%)',
  
  // Animated gradients for text
  dynamicText: {
    pineapple: 'linear-gradient(45deg, #ff8c00, #ffd700, #ff8c00)',
    gmo: 'linear-gradient(45deg, #4a90e2, #87ceeb, #4a90e2)',
    rosin: 'linear-gradient(45deg, #d4a574, #ff8c00, #d4a574)',
  },
} as const;

// Brand Shadows
export const brandShadows = {
  // Soft shadows
  soft: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  
  // Brand-specific glows
  apothecaryGlow: '0 0 20px rgba(47, 184, 134, 0.3)',
  goldGlow: '0 0 20px rgba(212, 165, 116, 0.3)',
  
  // Dynamic shadows for tilt effects
  tiltShadow: (direction: 'left' | 'right' | 'up' | 'down') => {
    const shadows = {
      left: '-5px 5px 15px rgba(0, 0, 0, 0.2)',
      right: '5px 5px 15px rgba(0, 0, 0, 0.2)',
      up: '0 -5px 15px rgba(0, 0, 0, 0.2)',
      down: '0 5px 15px rgba(0, 0, 0, 0.2)',
    };
    return shadows[direction];
  },
} as const;

// Animation Presets
export const brandAnimations = {
  // Easing functions (Framer Motion format)
  easing: {
    smooth: [0.4, 0, 0.2, 1],
    bounce: [0.68, -0.55, 0.265, 1.55],
    elastic: [0.175, 0.885, 0.32, 1.275],
  },

  // CSS easing functions (for CSS transitions)
  cssEasing: {
    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
  
  // Duration presets
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
    verySlow: 800,
  },
  
  // Common animation variants for Framer Motion
  fadeInUp: {
    initial: { opacity: 0, y: 40 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5, ease: 'easeOut' },
  },
  
  scaleOnHover: {
    whileHover: { scale: 1.07, brightness: 1.1 },
    transition: { duration: 0.2 },
  },
  
  tiltEffect: {
    perspective: 1000,
    rotateRange: 10,
  },
  
  typewriter: {
    charDelay: 50,
    cursorBlink: 1000,
  },
} as const;

// Typography Scale
export const brandTypography = {
  fonts: {
    sans: 'var(--font-inter), Inter, system-ui, sans-serif',
    serif: 'var(--font-playfair), Playfair Display, Georgia, serif',
  },
  
  sizes: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
    '5xl': '3rem',
    '6xl': '3.75rem',
  },
  
  weights: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
} as const;

// Spacing Scale
export const brandSpacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '3rem',
  '3xl': '4rem',
  '4xl': '6rem',
  '5xl': '8rem',
} as const;

// Utility Functions
export const brandUtils = {
  // Get color with opacity
  withOpacity: (color: string, opacity: number) => {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    return color;
  },
  
  // Generate random terpene gradient
  randomTerpeneGradient: () => {
    const terpenes = Object.keys(terpeneColors);
    const randomTerpene = terpenes[Math.floor(Math.random() * terpenes.length)];
    return terpeneColors[randomTerpene as keyof typeof terpeneColors].gradient;
  },
  
  // Get appropriate text color for background
  getTextColor: (backgroundColor: string) => {
    // Simple light/dark detection - in production, use a proper contrast calculation
    const darkColors = ['#1b4332', '#163a2b', '#267d47', '#2d3436', '#212529'];
    return darkColors.includes(backgroundColor) ? brandColors.cream[50] : brandColors.charcoal[800];
  },
} as const;

export default {
  colors: brandColors,
  terpenes: terpeneColors,
  gradients: brandGradients,
  shadows: brandShadows,
  animations: brandAnimations,
  typography: brandTypography,
  spacing: brandSpacing,
  utils: brandUtils,
};
