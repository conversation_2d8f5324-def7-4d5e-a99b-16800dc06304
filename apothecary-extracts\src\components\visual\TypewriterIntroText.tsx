'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TypewriterIntroTextProps } from '../../types/visual';
import { brandColors, brandTypography, brandAnimations } from '../../styles/brand';

/**
 * TypewriterIntroText Component
 * 
 * Adds nostalgic, cinematic intros to landing pages.
 * Features letter-by-letter typing with blinking cursor.
 * 
 * @example
 * ```tsx
 * <TypewriterIntroText 
 *   text={["Welcome to", "Apothecary Farms", "Premium Cannabis Extracts"]}
 *   charDelay={50}
 *   cursor="✨"
 *   loop={false}
 * />
 * ```
 */
const TypewriterIntroText: React.FC<TypewriterIntroTextProps> = ({
  text,
  className = '',
  style,
  charDelay = 50,
  wordDelay = 1000,
  cursor = '|',
  cursorBlinkSpeed = 1000,
  loop = false,
  startDelay = 0,
  fontFamily = 'serif',
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [showCursor, setShowCursor] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatches
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Convert text to array if it's a string
  const textArray = Array.isArray(text) ? text : [text];

  // Start typing animation
  useEffect(() => {
    if (!isClient) return; // Only run on client side

    const startTyping = setTimeout(() => {
      setIsTyping(true);
    }, startDelay);

    return () => clearTimeout(startTyping);
  }, [isClient, startDelay]);

  // Typing animation logic
  useEffect(() => {
    if (!isTyping) return;

    const currentText = textArray[currentTextIndex];
    
    if (currentIndex < currentText.length) {
      // Type next character
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + currentText[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, charDelay);

      return () => clearTimeout(timeout);
    } else {
      // Finished typing current text
      if (currentTextIndex < textArray.length - 1) {
        // Move to next text after word delay
        const timeout = setTimeout(() => {
          setDisplayText('');
          setCurrentIndex(0);
          setCurrentTextIndex(prev => prev + 1);
        }, wordDelay);

        return () => clearTimeout(timeout);
      } else if (loop) {
        // Restart from beginning if looping
        const timeout = setTimeout(() => {
          setDisplayText('');
          setCurrentIndex(0);
          setCurrentTextIndex(0);
        }, wordDelay);

        return () => clearTimeout(timeout);
      } else {
        // Stop typing
        setIsTyping(false);
      }
    }
  }, [currentIndex, currentTextIndex, isTyping, textArray, charDelay, wordDelay, loop]);

  // Cursor blinking animation
  useEffect(() => {
    if (!isClient) return; // Only run on client side

    const interval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, cursorBlinkSpeed);

    return () => clearInterval(interval);
  }, [isClient, cursorBlinkSpeed]);

  // Get font family
  const getFontFamily = () => {
    return fontFamily === 'serif' ? brandTypography.fonts.serif : brandTypography.fonts.sans;
  };

  // Don't render anything on server side to prevent hydration mismatch
  if (!isClient) {
    return (
      <div
        className={`typewriter-intro-text ${className}`}
        style={{
          fontFamily: getFontFamily(),
          ...style,
        }}
      >
        {/* Show static text on server side */}
        <span>{textArray[0]}</span>
      </div>
    );
  }

  return (
    <div
      className={`typewriter-intro-text ${className}`}
      style={{
        fontFamily: getFontFamily(),
        ...style,
      }}
    >
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {displayText}
      </motion.span>

      <AnimatePresence>
        {(isTyping || showCursor) && (
          <motion.span
            className="typewriter-cursor"
            style={{
              color: brandColors.apothecary,
              marginLeft: '2px',
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: showCursor ? 1 : 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          >
            {cursor}
          </motion.span>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TypewriterIntroText;
