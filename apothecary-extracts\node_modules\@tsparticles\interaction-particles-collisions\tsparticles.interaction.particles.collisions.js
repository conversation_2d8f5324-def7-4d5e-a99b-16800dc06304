/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Absorb.js":
/*!********************************!*\
  !*** ./dist/browser/Absorb.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   absorb: () => (/* binding */ absorb)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst half = 0.5,\n  absorbFactor = 10,\n  minAbsorbFactor = 0;\nfunction updateAbsorb(p1, r1, p2, r2, delta, pixelRatio) {\n  const factor = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(p1.options.collisions.absorb.speed * delta.factor / absorbFactor, minAbsorbFactor, r2);\n  p1.size.value += factor * half;\n  p2.size.value -= factor;\n  if (r2 <= pixelRatio) {\n    p2.size.value = 0;\n    p2.destroy();\n  }\n}\nfunction absorb(p1, p2, delta, pixelRatio) {\n  const r1 = p1.getRadius(),\n    r2 = p2.getRadius();\n  if (r1 === undefined && r2 !== undefined) {\n    p1.destroy();\n  } else if (r1 !== undefined && r2 === undefined) {\n    p2.destroy();\n  } else if (r1 !== undefined && r2 !== undefined) {\n    if (r1 >= r2) {\n      updateAbsorb(p1, r1, p2, r2, delta, pixelRatio);\n    } else {\n      updateAbsorb(p2, r2, p1, r1, delta, pixelRatio);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/Absorb.js?");

/***/ }),

/***/ "./dist/browser/Bounce.js":
/*!********************************!*\
  !*** ./dist/browser/Bounce.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bounce: () => (/* binding */ bounce)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst fixBounceSpeed = p => {\n  if (p.collisionMaxSpeed === undefined) {\n    p.collisionMaxSpeed = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(p.options.collisions.maxSpeed);\n  }\n  if (p.velocity.length > p.collisionMaxSpeed) {\n    p.velocity.length = p.collisionMaxSpeed;\n  }\n};\nfunction bounce(p1, p2) {\n  (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.circleBounce)((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.circleBounceDataFromParticle)(p1), (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.circleBounceDataFromParticle)(p2));\n  fixBounceSpeed(p1);\n  fixBounceSpeed(p2);\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/Bounce.js?");

/***/ }),

/***/ "./dist/browser/Collider.js":
/*!**********************************!*\
  !*** ./dist/browser/Collider.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collider: () => (/* binding */ Collider)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ResolveCollision_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResolveCollision.js */ \"./dist/browser/ResolveCollision.js\");\n\n\nconst double = 2;\nclass Collider extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {}\n  interact(p1, delta) {\n    if (p1.destroyed || p1.spawning) {\n      return;\n    }\n    const container = this.container,\n      pos1 = p1.getPosition(),\n      radius1 = p1.getRadius(),\n      query = container.particles.quadTree.queryCircle(pos1, radius1 * double);\n    for (const p2 of query) {\n      if (p1 === p2 || !p2.options.collisions.enable || p1.options.collisions.mode !== p2.options.collisions.mode || p2.destroyed || p2.spawning) {\n        continue;\n      }\n      const pos2 = p2.getPosition(),\n        radius2 = p2.getRadius();\n      if (Math.abs(Math.round(pos1.z) - Math.round(pos2.z)) > radius1 + radius2) {\n        continue;\n      }\n      const dist = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(pos1, pos2),\n        distP = radius1 + radius2;\n      if (dist > distP) {\n        continue;\n      }\n      (0,_ResolveCollision_js__WEBPACK_IMPORTED_MODULE_1__.resolveCollision)(p1, p2, delta, container.retina.pixelRatio);\n    }\n  }\n  isEnabled(particle) {\n    return particle.options.collisions.enable;\n  }\n  reset() {}\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/Collider.js?");

/***/ }),

/***/ "./dist/browser/Destroy.js":
/*!*********************************!*\
  !*** ./dist/browser/Destroy.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   destroy: () => (/* binding */ destroy)\n/* harmony export */ });\n/* harmony import */ var _Bounce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bounce.js */ \"./dist/browser/Bounce.js\");\n\nfunction destroy(p1, p2) {\n  if (!p1.unbreakable && !p2.unbreakable) {\n    (0,_Bounce_js__WEBPACK_IMPORTED_MODULE_0__.bounce)(p1, p2);\n  }\n  if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n    p1.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n    p2.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n    const deleteP = p1.getRadius() >= p2.getRadius() ? p2 : p1;\n    deleteP.destroy();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/Destroy.js?");

/***/ }),

/***/ "./dist/browser/ResolveCollision.js":
/*!******************************************!*\
  !*** ./dist/browser/ResolveCollision.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveCollision: () => (/* binding */ resolveCollision)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Absorb_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Absorb.js */ \"./dist/browser/Absorb.js\");\n/* harmony import */ var _Bounce_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bounce.js */ \"./dist/browser/Bounce.js\");\n/* harmony import */ var _Destroy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Destroy.js */ \"./dist/browser/Destroy.js\");\n\n\n\n\nfunction resolveCollision(p1, p2, delta, pixelRatio) {\n  switch (p1.options.collisions.mode) {\n    case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.CollisionMode.absorb:\n      {\n        (0,_Absorb_js__WEBPACK_IMPORTED_MODULE_1__.absorb)(p1, p2, delta, pixelRatio);\n        break;\n      }\n    case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.CollisionMode.bounce:\n      {\n        (0,_Bounce_js__WEBPACK_IMPORTED_MODULE_2__.bounce)(p1, p2);\n        break;\n      }\n    case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.CollisionMode.destroy:\n      {\n        (0,_Destroy_js__WEBPACK_IMPORTED_MODULE_3__.destroy)(p1, p2);\n        break;\n      }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/ResolveCollision.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadParticlesCollisionsInteraction: () => (/* binding */ loadParticlesCollisionsInteraction)\n/* harmony export */ });\n/* harmony import */ var _Collider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Collider.js */ \"./dist/browser/Collider.js\");\n\nasync function loadParticlesCollisionsInteraction(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addInteractor(\"particlesCollisions\", container => {\n    return Promise.resolve(new _Collider_js__WEBPACK_IMPORTED_MODULE_0__.Collider(container));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-particles-collisions/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});