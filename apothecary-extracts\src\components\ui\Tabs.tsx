'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, ReactNode } from 'react';

interface Tab {
  id: string;
  label: string;
  content: ReactNode;
  icon?: ReactNode;
  badge?: string | number;
}

interface TabsProps {
  tabs: Tab[];
  defaultTab?: string;
  variant?: 'default' | 'soft' | 'pills' | 'underline';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  onTabChange?: (tabId: string) => void;
}

export default function Tabs({
  tabs,
  defaultTab,
  variant = 'default',
  orientation = 'horizontal',
  className = '',
  onTabChange
}: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const variantStyles = {
    default: {
      container: 'border-b border-cream-300',
      tab: 'px-4 py-2 font-medium text-sm border-b-2 border-transparent hover:text-primary-600 hover:border-primary-300 transition-colors duration-200',
      activeTab: 'text-primary-600 border-primary-600',
      content: 'py-6'
    },
    soft: {
      container: 'bg-cream-100 rounded-lg p-1',
      tab: 'px-4 py-2 font-medium text-sm rounded-md hover:bg-cream-200 transition-colors duration-200',
      activeTab: 'bg-cream-50 text-primary-600 shadow-soft',
      content: 'py-6'
    },
    pills: {
      container: 'space-x-2',
      tab: 'px-4 py-2 font-medium text-sm rounded-full border border-cream-300 hover:border-primary-300 hover:text-primary-600 transition-colors duration-200',
      activeTab: 'bg-primary-600 text-cream-50 border-primary-600',
      content: 'py-6'
    },
    underline: {
      container: 'space-x-6',
      tab: 'px-0 py-2 font-medium text-sm relative hover:text-primary-600 transition-colors duration-200',
      activeTab: 'text-primary-600',
      content: 'py-6'
    }
  };

  const styles = variantStyles[variant];
  const isVertical = orientation === 'vertical';

  const tabVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  const contentVariants = {
    hidden: { opacity: 0, x: isVertical ? 20 : 0, y: isVertical ? 0 : 10 },
    visible: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, x: isVertical ? -20 : 0, y: isVertical ? 0 : -10 }
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className={`${isVertical ? 'flex gap-6' : ''} ${className}`}>
      {/* Tab Navigation */}
      <div className={`${isVertical ? 'flex-shrink-0 w-64' : ''}`}>
        <nav className={`${isVertical ? 'flex flex-col space-y-2' : `flex ${styles.container}`}`}>
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              variants={tabVariants}
              initial="hidden"
              animate="visible"
              transition={{ duration: 0.2 }}
              onClick={() => handleTabChange(tab.id)}
              className={`
                ${styles.tab}
                ${activeTab === tab.id ? styles.activeTab : ''}
                ${isVertical ? 'text-left justify-start' : ''}
                flex items-center gap-2 relative
              `}
            >
              {/* Icon */}
              {tab.icon && (
                <span className="flex-shrink-0">
                  {tab.icon}
                </span>
              )}

              {/* Label */}
              <span>{tab.label}</span>

              {/* Badge */}
              {tab.badge && (
                <span className="ml-auto bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full">
                  {tab.badge}
                </span>
              )}

              {/* Underline variant indicator */}
              {variant === 'underline' && activeTab === tab.id && (
                <motion.div
                  layoutId="activeTabIndicator"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600"
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                />
              )}

              {/* Soft variant background */}
              {variant === 'soft' && activeTab === tab.id && (
                <motion.div
                  layoutId="activeTabBackground"
                  className="absolute inset-0 bg-cream-50 rounded-md shadow-soft"
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                  style={{ zIndex: -1 }}
                />
              )}
            </motion.button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className={`${isVertical ? 'flex-1' : ''} ${styles.content}`}>
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            variants={contentVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {activeTabContent}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

// Example usage with GridCards
export function ExampleTabbedGrid() {
  const tabs = [
    {
      id: 'flower',
      label: 'Flower',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v11h14V7l-7-5z" />
        </svg>
      ),
      badge: '12',
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Grid content would go here */}
          <div className="bg-cream-50 p-6 rounded-lg">Flower products...</div>
        </div>
      )
    },
    {
      id: 'concentrates',
      label: 'Concentrates',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v11h14V7l-7-5z" />
        </svg>
      ),
      badge: '8',
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-cream-50 p-6 rounded-lg">Concentrate products...</div>
        </div>
      )
    },
    {
      id: 'edibles',
      label: 'Edibles',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L3 7v11h14V7l-7-5z" />
        </svg>
      ),
      badge: '15',
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-cream-50 p-6 rounded-lg">Edible products...</div>
        </div>
      )
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-3xl font-serif font-bold text-center mb-8">Product Categories</h2>
      <Tabs tabs={tabs} variant="soft" />
    </div>
  );
}
