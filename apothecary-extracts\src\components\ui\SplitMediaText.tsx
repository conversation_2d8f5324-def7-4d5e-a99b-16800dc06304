'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import Image from 'next/image';

interface SplitMediaTextProps {
  title: string;
  subtitle?: string;
  content: ReactNode;
  media: {
    type: 'image' | 'video';
    src: string;
    alt?: string;
    poster?: string; // For video
  };
  layout?: 'default' | 'reversed';
  mediaPosition?: 'left' | 'right';
  backgroundColor?: string;
  textAlign?: 'left' | 'center' | 'right';
  cta?: {
    text: string;
    href?: string;
    action?: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  className?: string;
}

export default function SplitMediaText({
  title,
  subtitle,
  content,
  media,
  layout = 'default',
  mediaPosition = 'right',
  backgroundColor = 'bg-cream-50',
  textAlign = 'left',
  cta,
  className = ''
}: SplitMediaTextProps) {
  const isReversed = layout === 'reversed' || mediaPosition === 'left';

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const textVariants = {
    hidden: {
      opacity: 0,
      x: isReversed ? 50 : -50,
      y: 20
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const mediaVariants = {
    hidden: {
      opacity: 0,
      x: isReversed ? -50 : 50,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const ctaVariants = {
    primary: 'bg-primary-600 text-cream-50 hover:bg-primary-700',
    secondary: 'bg-gold-500 text-primary-800 hover:bg-gold-400',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-cream-50'
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  return (
    <motion.section
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      className={`py-16 lg:py-24 ${backgroundColor} ${className}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center ${isReversed ? 'lg:grid-flow-col-dense' : ''}`}>
          
          {/* Text Content */}
          <motion.div
            variants={textVariants}
            className={`${isReversed ? 'lg:col-start-2' : ''} ${textAlignClasses[textAlign]}`}
          >
            {/* Subtitle */}
            {subtitle && (
              <motion.p
                className="text-primary-600 font-medium text-sm uppercase tracking-wide mb-4"
                variants={{
                  hidden: { opacity: 0, y: 10 },
                  visible: { opacity: 1, y: 0 }
                }}
              >
                {subtitle}
              </motion.p>
            )}

            {/* Title */}
            <motion.h2
              className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-foreground mb-6 leading-tight"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              }}
            >
              {title}
            </motion.h2>

            {/* Content */}
            <motion.div
              className="text-gray-600 text-lg leading-relaxed mb-8"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              }}
            >
              {content}
            </motion.div>

            {/* CTA Button */}
            {cta && (
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: { opacity: 1, y: 0 }
                }}
              >
                {cta.href ? (
                  <a
                    href={cta.href}
                    className={`inline-flex items-center px-8 py-4 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${ctaVariants[cta.variant || 'primary']}`}
                  >
                    {cta.text}
                    <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                ) : (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={cta.action}
                    className={`inline-flex items-center px-8 py-4 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${ctaVariants[cta.variant || 'primary']}`}
                  >
                    {cta.text}
                    <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </motion.button>
                )}
              </motion.div>
            )}
          </motion.div>

          {/* Media Content */}
          <motion.div
            variants={mediaVariants}
            className={`${isReversed ? 'lg:col-start-1' : ''} relative`}
          >
            <div className="relative overflow-hidden rounded-2xl shadow-large">
              {media.type === 'image' ? (
                <div className="relative aspect-[4/3] w-full">
                  <Image
                    src={media.src}
                    alt={media.alt || title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
              ) : (
                <div className="relative aspect-[16/9] w-full">
                  <video
                    src={media.src}
                    poster={media.poster}
                    controls
                    className="w-full h-full object-cover"
                    preload="metadata"
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
              )}

              {/* Decorative overlay */}
              <div className="absolute inset-0 bg-gradient-to-tr from-primary-600/10 to-transparent pointer-events-none" />
            </div>

            {/* Floating decoration */}
            <motion.div
              className="absolute -top-6 -right-6 w-24 h-24 bg-gold-200 rounded-full opacity-20"
              animate={{
                y: [0, -10, 0],
                rotate: [0, 5, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
            />
            <motion.div
              className="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-200 rounded-full opacity-30"
              animate={{
                y: [0, 10, 0],
                rotate: [0, -5, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: 1
              }}
            />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}

// Example usage
export function ExampleSplitMediaText() {
  return (
    <SplitMediaText
      subtitle="Our Story"
      title="Crafting Excellence Since 2015"
      content={
        <div>
          <p className="mb-4">
            At Apothecary Extracts, we've been pioneering premium cannabis cultivation and extraction 
            techniques for nearly a decade. Our commitment to quality, innovation, and customer 
            satisfaction has made us Colorado's trusted source for exceptional cannabis products.
          </p>
          <p>
            From our state-of-the-art facilities to our expert team of cultivators and extractors, 
            every step of our process is designed to deliver the purest, most potent products possible.
          </p>
        </div>
      }
      media={{
        type: 'image',
        src: '/assets/about/facility.jpg',
        alt: 'Apothecary Extracts facility'
      }}
      layout="reversed"
      cta={{
        text: 'Learn More About Us',
        href: '/about',
        variant: 'primary'
      }}
    />
  );
}
