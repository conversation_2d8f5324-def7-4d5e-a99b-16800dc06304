'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { MorphingBlobHeroProps } from '../../types/visual';
import { terpeneColors, brandAnimations } from '../../styles/brand';

/**
 * MorphingBlobHero Component
 * 
 * Adds fluid, organic motion behind content in hero sections.
 * Features smooth SVG blob morphing with terpene-themed gradients.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <MorphingBlobHero 
 *     gradientTheme="rosin"
 *     size={400}
 *     position="center"
 *     animate={true}
 *   />
 *   <h1 className="relative z-10">Hero Content</h1>
 * </div>
 * ```
 */
const MorphingBlobHero: React.FC<MorphingBlobHeroProps> = ({
  className = '',
  style,
  shapeCount = 4,
  morphDuration = 3000,
  morphDelay = 500,
  size = 300,
  gradientTheme = 'flower',
  customGradient,
  animate = true,
  blurIntensity = 20,
  position = 'center',
}) => {
  const [currentShapeIndex, setCurrentShapeIndex] = useState(0);

  // Predefined blob shapes for morphing
  const blobShapes = [
    // Shape 1 - Organic blob
    "M60,-60C80,-40,100,-20,100,0C100,20,80,40,60,60C40,80,20,100,0,100C-20,100,-40,80,-60,60C-80,40,-100,20,-100,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-100C20,-100,40,-80,60,-60Z",
    
    // Shape 2 - Rounded square-ish
    "M50,-50C70,-50,90,-30,90,-10C90,10,70,30,50,50C30,70,10,90,-10,90C-30,90,-50,70,-50,50C-70,30,-90,10,-90,-10C-90,-30,-70,-50,-50,-50C-30,-70,-10,-90,10,-90C30,-90,50,-70,50,-50Z",
    
    // Shape 3 - Flowing organic
    "M70,-30C90,-10,90,30,70,50C50,70,10,70,-10,50C-30,30,-30,-10,-10,-30C10,-50,50,-50,70,-30Z",
    
    // Shape 4 - Irregular blob
    "M40,-60C60,-40,80,-20,80,20C80,60,40,80,0,80C-40,80,-80,60,-80,20C-80,-20,-60,-40,-40,-60C-20,-80,20,-80,40,-60Z",
  ];

  // Get gradient based on theme
  const getGradient = () => {
    if (customGradient) return customGradient;
    
    const terpene = terpeneColors[gradientTheme];
    if (!terpene) return terpeneColors.flower.gradient;
    
    return terpene.gradient;
  };

  // Get position styles
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      zIndex: 0,
    };

    switch (position) {
      case 'center':
        return {
          ...baseStyles,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        };
      case 'top-left':
        return {
          ...baseStyles,
          top: '10%',
          left: '10%',
        };
      case 'top-right':
        return {
          ...baseStyles,
          top: '10%',
          right: '10%',
        };
      case 'bottom-left':
        return {
          ...baseStyles,
          bottom: '10%',
          left: '10%',
        };
      case 'bottom-right':
        return {
          ...baseStyles,
          bottom: '10%',
          right: '10%',
        };
      default:
        return {
          ...baseStyles,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        };
    }
  };

  // Morphing animation
  useEffect(() => {
    if (!animate) return;

    const interval = setInterval(() => {
      setCurrentShapeIndex(prev => (prev + 1) % shapeCount);
    }, morphDuration + morphDelay);

    return () => clearInterval(interval);
  }, [animate, morphDuration, morphDelay, shapeCount]);

  const gradientId = `blob-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <motion.div
      className={`morphing-blob-hero ${className}`}
      style={{
        ...getPositionStyles(),
        filter: `blur(${blurIntensity}px)`,
        ...style,
      }}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, ease: brandAnimations.easing.smooth }}
    >
      <svg
        width={size}
        height={size}
        viewBox="-100 -100 200 200"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={terpeneColors[gradientTheme]?.from || '#359a5a'} />
            <stop offset="100%" stopColor={terpeneColors[gradientTheme]?.to || '#ffd700'} />
          </linearGradient>
        </defs>
        
        <motion.path
          d={blobShapes[currentShapeIndex]}
          fill={`url(#${gradientId})`}
          animate={{
            d: blobShapes[currentShapeIndex],
          }}
          transition={{
            duration: morphDuration / 1000,
            ease: brandAnimations.easing.smooth,
          }}
        />
      </svg>
    </motion.div>
  );
};

export default MorphingBlobHero;
