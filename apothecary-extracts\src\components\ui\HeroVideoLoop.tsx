'use client';

import { motion } from 'framer-motion';
import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';

interface CTAButton {
  text: string;
  href?: string;
  action?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
}

interface HeroVideoLoopProps {
  headline: string;
  subheadline?: string;
  videoSrc: string;
  posterImage?: string;
  ctas?: CTAButton[];
  overlayOpacity?: number;
  textPosition?: 'center' | 'left' | 'right';
  showScrollIndicator?: boolean;
  autoPlay?: boolean;
  className?: string;
}

export default function HeroVideoLoop({
  headline,
  subheadline,
  videoSrc,
  posterImage,
  ctas = [],
  overlayOpacity = 0.4,
  textPosition = 'center',
  showScrollIndicator = true,
  autoPlay = true,
  className = ''
}: HeroVideoLoopProps) {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (video && autoPlay) {
      video.play().catch(() => {
        // Auto-play failed, user interaction required
        setIsPlaying(false);
      });
    }
  }, [autoPlay]);

  const handleVideoLoad = () => {
    setIsVideoLoaded(true);
  };

  const handlePlayPause = () => {
    const video = videoRef.current;
    if (video) {
      if (video.paused) {
        video.play();
        setIsPlaying(true);
      } else {
        video.pause();
        setIsPlaying(false);
      }
    }
  };

  const ctaVariants = {
    primary: 'bg-gold-500 text-primary-800 hover:bg-gold-400 focus:ring-gold-400',
    secondary: 'bg-primary-600 text-cream-50 hover:bg-primary-700 focus:ring-primary-500',
    outline: 'border-2 border-cream-50 text-cream-50 hover:bg-cream-50 hover:text-primary-800 focus:ring-cream-50'
  };

  const textPositionClasses = {
    center: 'text-center items-center',
    left: 'text-left items-start',
    right: 'text-right items-end'
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  };

  const scrollIndicatorVariants = {
    animate: {
      y: [0, 10, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  return (
    <section className={`relative h-screen overflow-hidden bg-primary-800 ${className}`}>
      {/* Video Background */}
      <div className="absolute inset-0">
        <video
          ref={videoRef}
          autoPlay={autoPlay}
          muted
          loop
          playsInline
          poster={posterImage}
          onLoadedData={handleVideoLoad}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          className={`w-full h-full object-cover transition-opacity duration-1000 ${
            isVideoLoaded ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <source src={videoSrc} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Video Overlay */}
        <div 
          className="absolute inset-0 bg-black transition-opacity duration-300"
          style={{ opacity: overlayOpacity }}
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30" />
      </div>

      {/* Content */}
      <div className={`relative z-10 h-full flex flex-col justify-center px-6 sm:px-8 lg:px-12 ${textPositionClasses[textPosition]}`}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl"
        >
          {/* Headline */}
          <motion.h1
            variants={itemVariants}
            className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-cream-50 leading-tight mb-6"
          >
            {headline.split(' ').map((word, index) => (
              <motion.span
                key={index}
                variants={{
                  hidden: { opacity: 0, y: 50 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: {
                      duration: 0.6,
                      delay: index * 0.1,
                      ease: 'easeOut'
                    }
                  }
                }}
                className="inline-block mr-3"
              >
                {word}
              </motion.span>
            ))}
          </motion.h1>

          {/* Subheadline */}
          {subheadline && (
            <motion.p
              variants={itemVariants}
              className="text-xl sm:text-2xl md:text-3xl text-cream-200 mb-8 leading-relaxed max-w-3xl"
            >
              {subheadline}
            </motion.p>
          )}

          {/* CTA Buttons */}
          {ctas.length > 0 && (
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-4 justify-center sm:justify-start"
            >
              {ctas.map((cta, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {cta.href ? (
                    <Link
                      href={cta.href}
                      className={`inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${
                        ctaVariants[cta.variant || 'primary']
                      }`}
                    >
                      {cta.text}
                      <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  ) : (
                    <button
                      onClick={cta.action}
                      className={`inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${
                        ctaVariants[cta.variant || 'primary']
                      }`}
                    >
                      {cta.text}
                      <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  )}
                </motion.div>
              ))}
            </motion.div>
          )}
        </motion.div>
      </div>

      {/* Video Controls */}
      <div className="absolute bottom-6 left-6 z-20">
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={handlePlayPause}
          className="bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200"
          aria-label={isPlaying ? 'Pause video' : 'Play video'}
        >
          {isPlaying ? (
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-6 h-6 ml-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          )}
        </motion.button>
      </div>

      {/* Scroll Indicator */}
      {showScrollIndicator && (
        <motion.div
          variants={scrollIndicatorVariants}
          animate="animate"
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        >
          <div className="flex flex-col items-center text-cream-50">
            <span className="text-sm font-medium mb-2">Scroll</span>
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </motion.div>
      )}

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0 z-20">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z" 
            fill="var(--background)"
          />
        </svg>
      </div>
    </section>
  );
}

// Example usage
export function ExampleHeroVideoLoop() {
  const ctas: CTAButton[] = [
    {
      text: 'Browse Products',
      href: '/products',
      variant: 'primary'
    },
    {
      text: 'Find Your Store',
      href: '/locations',
      variant: 'outline'
    },
    {
      text: 'Explore Extracts',
      href: '/products?category=extracts',
      variant: 'secondary'
    }
  ];

  return (
    <HeroVideoLoop
      headline="Craft Cannabis. Next Level."
      subheadline="Award-winning extracts. Rare strains. Daily deals. Let's vibe."
      videoSrc="/assets/hero/loop-vapor.mp4"
      posterImage="/assets/hero/hero-poster.jpg"
      ctas={ctas}
      overlayOpacity={0.3}
      textPosition="center"
      showScrollIndicator={true}
    />
  );
}
