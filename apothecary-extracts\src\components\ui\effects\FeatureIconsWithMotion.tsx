'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { FeatureIconsWithMotionProps, FeatureItem } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * FeatureIconsWithMotion Component
 * 
 * Build feature highlight sections with animated icons using Framer Motion.
 * Features customizable layouts, animation triggers, and cannabis industry icons.
 * 
 * @example
 * ```tsx
 * <FeatureIconsWithMotion
 *   features={features}
 *   layout="grid"
 *   animationTrigger="scroll"
 *   columns={3}
 *   iconSize="lg"
 * />
 * ```
 */
const FeatureIconsWithMotion: React.FC<FeatureIconsWithMotionProps> = ({
  features,
  layout = 'grid',
  animationTrigger = 'scroll',
  columns = 3,
  iconSize = 'md',
  className = '',
  style,
}) => {
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: animationTrigger === 'scroll',
  });

  // Get icon size classes
  const getIconSizeClasses = () => {
    switch (iconSize) {
      case 'sm':
        return { container: 'w-12 h-12', icon: 'w-6 h-6' };
      case 'md':
        return { container: 'w-16 h-16', icon: 'w-8 h-8' };
      case 'lg':
        return { container: 'w-20 h-20', icon: 'w-10 h-10' };
      case 'xl':
        return { container: 'w-24 h-24', icon: 'w-12 h-12' };
      default:
        return { container: 'w-16 h-16', icon: 'w-8 h-8' };
    }
  };

  const iconSizeClasses = getIconSizeClasses();

  // Get layout classes
  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap justify-center gap-8';
      case 'vertical':
        return 'space-y-8';
      case 'grid':
      default:
        return `grid gap-8 ${
          columns === 1 ? 'grid-cols-1' :
          columns === 2 ? 'grid-cols-1 md:grid-cols-2' :
          columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
          columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :
          'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
        }`;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.8
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: brandAnimations.easing.smooth
      }
    }
  };

  const iconVariants = {
    hidden: { 
      scale: 0,
      rotate: -180
    },
    visible: { 
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.6,
        ease: brandAnimations.easing.bounce,
        delay: 0.2
      }
    },
    hover: {
      scale: 1.1,
      rotate: 5,
      transition: {
        duration: 0.2,
        ease: brandAnimations.easing.smooth
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={
        animationTrigger === 'scroll' 
          ? (inView ? 'visible' : 'hidden')
          : animationTrigger === 'load' 
          ? 'visible' 
          : 'hidden'
      }
      className={`feature-icons-with-motion ${className}`}
      style={style}
    >
      <div className={getLayoutClasses()}>
        {features.map((feature, index) => (
          <FeatureCard
            key={feature.id}
            feature={feature}
            index={index}
            iconSizeClasses={iconSizeClasses}
            itemVariants={itemVariants}
            iconVariants={iconVariants}
            animationTrigger={animationTrigger}
            layout={layout}
          />
        ))}
      </div>
    </motion.div>
  );
};

// Individual Feature Card Component
interface FeatureCardProps {
  feature: FeatureItem;
  index: number;
  iconSizeClasses: { container: string; icon: string };
  itemVariants: any;
  iconVariants: any;
  animationTrigger: 'scroll' | 'hover' | 'load';
  layout: 'grid' | 'horizontal' | 'vertical';
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  feature,
  index,
  iconSizeClasses,
  itemVariants,
  iconVariants,
  animationTrigger,
  layout,
}) => {
  const [cardRef, cardInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const getFeatureColor = () => {
    if (feature.color) return feature.color;
    
    // Default colors based on index
    const colors = [
      brandColors.primary[500],
      brandColors.apothecary,
      brandColors.gold[500],
      brandColors.sage[500],
      brandColors.charcoal[600],
    ];
    return colors[index % colors.length];
  };

  const featureColor = getFeatureColor();

  return (
    <motion.div
      ref={cardRef}
      variants={itemVariants}
      className={`feature-card text-center ${
        layout === 'horizontal' ? 'flex-shrink-0' : ''
      }`}
      whileHover={animationTrigger === 'hover' ? { y: -8 } : undefined}
    >
      {/* Icon Container */}
      <motion.div
        variants={iconVariants}
        whileHover="hover"
        className={`${iconSizeClasses.container} mx-auto mb-4 rounded-xl flex items-center justify-center relative overflow-hidden`}
        style={{ 
          backgroundColor: `${featureColor}15`,
          boxShadow: brandShadows.soft 
        }}
      >
        {/* Background Glow */}
        <motion.div
          className="absolute inset-0 rounded-xl"
          style={{ backgroundColor: featureColor }}
          initial={{ opacity: 0.1 }}
          whileHover={{ opacity: 0.2 }}
          transition={{ duration: 0.2 }}
        />
        
        {/* Icon */}
        <motion.div
          className={`${iconSizeClasses.icon} relative z-10`}
          style={{ color: featureColor }}
          whileHover={{ 
            filter: 'brightness(1.2)',
            transition: { duration: 0.2 }
          }}
        >
          {feature.icon}
        </motion.div>

        {/* Pulse Effect */}
        <motion.div
          className="absolute inset-0 rounded-xl border-2"
          style={{ borderColor: featureColor }}
          initial={{ scale: 1, opacity: 0 }}
          animate={
            animationTrigger === 'scroll' && cardInView
              ? {
                  scale: [1, 1.2, 1],
                  opacity: [0, 0.6, 0],
                }
              : {}
          }
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: index * 0.2,
          }}
        />
      </motion.div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={
          animationTrigger === 'scroll' && cardInView
            ? { opacity: 1, y: 0 }
            : animationTrigger === 'load'
            ? { opacity: 1, y: 0 }
            : { opacity: 0, y: 20 }
        }
        transition={{
          duration: 0.5,
          delay: 0.3 + index * 0.1,
          ease: brandAnimations.easing.smooth,
        }}
      >
        <h3 
          className="text-lg font-semibold mb-2"
          style={{ color: featureColor }}
        >
          {feature.title}
        </h3>
        <p className="text-charcoal-600 leading-relaxed">
          {feature.description}
        </p>
      </motion.div>

      {/* Decorative Elements */}
      <motion.div
        className="absolute -top-2 -right-2 w-4 h-4 rounded-full"
        style={{ backgroundColor: featureColor }}
        initial={{ scale: 0, opacity: 0 }}
        animate={
          animationTrigger === 'scroll' && cardInView
            ? { scale: 1, opacity: 0.3 }
            : animationTrigger === 'load'
            ? { scale: 1, opacity: 0.3 }
            : { scale: 0, opacity: 0 }
        }
        transition={{
          duration: 0.4,
          delay: 0.6 + index * 0.1,
          ease: brandAnimations.easing.bounce,
        }}
      />
    </motion.div>
  );
};

export default FeatureIconsWithMotion;
