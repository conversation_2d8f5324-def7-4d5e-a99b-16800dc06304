"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[622],{1225:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(5155),t=a(6874),l=a.n(t);let i=[{name:"Colorado Springs - Garden of the Gods",address:"414 Garden of the Gods Road, Colorado Springs, CO 80907",phone:"(*************",hours:{weekdays:"9:00 AM - 9:00 PM",saturday:"9:00 AM - 9:00 PM",sunday:"10:00 AM - 8:00 PM"},services:["Medical","Recreational","Curbside Pickup"],isNew:!0},{name:"Colorado Springs - Downtown",address:"123 Main Street, Colorado Springs, CO 80903",phone:"(*************",hours:{weekdays:"8:00 AM - 10:00 PM",saturday:"8:00 AM - 10:00 PM",sunday:"9:00 AM - 9:00 PM"},services:["Medical","Recreational","Delivery","Consultation"],isNew:!1}];function n(){return(0,r.jsx)("section",{className:"py-20 bg-cream-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Visit Our Dispensaries"}),(0,r.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed",children:"Find us at convenient locations throughout Colorado Springs. Each dispensary offers the full Apothecary Extracts experience."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:i.map(e=>(0,r.jsxs)("div",{className:"bg-cream-100 rounded-xl p-8 shadow-soft hover:shadow-medium transition-shadow duration-300 relative overflow-hidden",children:[e.isNew&&(0,r.jsx)("div",{className:"absolute top-4 right-4",children:(0,r.jsx)("span",{className:"bg-gold-500 text-primary-800 px-3 py-1 rounded-full text-sm font-semibold",children:"New Location"})}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-primary-800 mb-2",children:e.name}),(0,r.jsxs)("p",{className:"text-charcoal-600 mb-4 flex items-start",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-primary-600 mr-2 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"})}),e.address]}),(0,r.jsxs)("p",{className:"text-charcoal-600 mb-4 flex items-center",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-primary-600 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"})}),e.phone]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-primary-800 mb-3",children:"Store Hours"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-charcoal-600",children:"Monday - Friday:"}),(0,r.jsx)("span",{className:"text-charcoal-800 font-medium",children:e.hours.weekdays})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-charcoal-600",children:"Saturday:"}),(0,r.jsx)("span",{className:"text-charcoal-800 font-medium",children:e.hours.saturday})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-charcoal-600",children:"Sunday:"}),(0,r.jsx)("span",{className:"text-charcoal-800 font-medium",children:e.hours.sunday})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-primary-800 mb-3",children:"Services"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.services.map(e=>(0,r.jsx)("span",{className:"px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full",children:e},e))})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsx)(l(),{href:"/locations/".concat(e.name.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"")),className:"flex-1 bg-primary-800 text-cream-50 py-3 px-4 rounded-lg text-center font-medium hover:bg-primary-700 transition-colors duration-200",children:"View Details"}),(0,r.jsx)("a",{href:"https://maps.google.com/?q=".concat(encodeURIComponent(e.address)),target:"_blank",rel:"noopener noreferrer",className:"flex-1 bg-transparent text-primary-800 py-3 px-4 rounded-lg text-center font-medium border border-primary-800 hover:bg-primary-50 transition-colors duration-200",children:"Get Directions"})]})]},e.name))}),(0,r.jsxs)("div",{className:"bg-gold-100 rounded-xl p-8 text-center",children:[(0,r.jsx)("h3",{className:"text-2xl font-serif font-bold text-primary-800 mb-4",children:"Can't Make It to Our Store?"}),(0,r.jsx)("p",{className:"text-charcoal-700 mb-6 max-w-2xl mx-auto",children:"We offer curbside pickup and delivery services (where legally permitted) to make your cannabis shopping experience as convenient as possible."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(l(),{href:"/delivery",className:"inline-flex items-center px-6 py-3 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200",children:[(0,r.jsxs)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"}),(0,r.jsx)("path",{d:"M5 6h5v2H5zm11.5 9c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"})]}),"Delivery Info"]}),(0,r.jsxs)(l(),{href:"/curbside",className:"inline-flex items-center px-6 py-3 bg-transparent text-primary-800 font-semibold rounded-lg border border-primary-800 hover:bg-primary-50 transition-colors duration-200",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"})}),"Curbside Pickup"]})]})]})]})})}},2056:(e,s,a)=>{a.d(s,{A:()=>t});var r=a(5155);function t(){return(0,r.jsx)("div",{className:"bg-gold-100 border border-gold-400 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gold-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gold-800 mb-2",children:"Important Legal Information"}),(0,r.jsxs)("div",{className:"text-sm text-gold-700 space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Colorado State Compliance:"})," This establishment is licensed by the State of Colorado to sell cannabis products to adults 21 years of age and older. License #: [LICENSE_NUMBER]"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Health & Safety:"})," Cannabis products have not been evaluated by the FDA and are not intended to diagnose, treat, cure, or prevent any disease. Keep out of reach of children and pets."]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Consumption Guidelines:"})," Do not operate vehicles or machinery after use. Effects may be delayed with edible products. Start with low doses and wait before consuming more."]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Legal Restrictions:"})," Cannabis products may not be transported across state lines. Consumption is prohibited in public places and federal properties."]})]})]})]})})}}}]);