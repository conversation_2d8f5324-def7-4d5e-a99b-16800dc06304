{"name": "@tsparticles/slim", "version": "3.8.1", "description": "Easily create highly customizable particle animations and use them as animated backgrounds for your website. Ready to use components available also for React, Vue.js (2.x and 3.x), Angular, Svelte, jQuery, Preact, Riot.js, Inferno.", "homepage": "https://particles.js.org", "repository": {"type": "git", "url": "git+https://github.com/tsparticles/tsparticles.git", "directory": "bundles/slim"}, "keywords": ["front-end", "frontend", "tsparticles", "particles.js", "<PERSON><PERSON>s", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles-bg", "particles-bg-vue", "particles-ts", "particles.ts", "react-particles-js", "react-particles.js", "react-particles", "react", "reactjs", "vue-particles", "ngx-particles", "angular-particles", "particleground", "vue", "v<PERSON><PERSON><PERSON>", "preact", "preactjs", "j<PERSON>y", "<PERSON><PERSON>s", "angular", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "confetti", "canvas", "fireworks", "fireworks-js", "confetti-js", "confettijs", "<PERSON>js", "canvas-confetti"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tsparticles/tsparticles/issues"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "sideEffects": false, "jsdelivr": "tsparticles.slim.bundle.min.js", "unpkg": "tsparticles.slim.bundle.min.js", "browser": "browser/index.js", "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts", "browser": "./browser/index.js", "import": "./esm/index.js", "require": "./cjs/index.js", "umd": "./umd/index.js", "default": "./cjs/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@tsparticles/basic": "3.8.1", "@tsparticles/engine": "3.8.1", "@tsparticles/interaction-external-attract": "3.8.1", "@tsparticles/interaction-external-bounce": "3.8.1", "@tsparticles/interaction-external-bubble": "3.8.1", "@tsparticles/interaction-external-connect": "3.8.1", "@tsparticles/interaction-external-grab": "3.8.1", "@tsparticles/interaction-external-pause": "3.8.1", "@tsparticles/interaction-external-push": "3.8.1", "@tsparticles/interaction-external-remove": "3.8.1", "@tsparticles/interaction-external-repulse": "3.8.1", "@tsparticles/interaction-external-slow": "3.8.1", "@tsparticles/interaction-particles-attract": "3.8.1", "@tsparticles/interaction-particles-collisions": "3.8.1", "@tsparticles/interaction-particles-links": "3.8.1", "@tsparticles/move-parallax": "3.8.1", "@tsparticles/plugin-easing-quad": "3.8.1", "@tsparticles/shape-emoji": "3.8.1", "@tsparticles/shape-image": "3.8.1", "@tsparticles/shape-line": "3.8.1", "@tsparticles/shape-polygon": "3.8.1", "@tsparticles/shape-square": "3.8.1", "@tsparticles/shape-star": "3.8.1", "@tsparticles/updater-life": "3.8.1", "@tsparticles/updater-rotate": "3.8.1", "@tsparticles/updater-stroke-color": "3.8.1"}, "publishConfig": {"access": "public"}}