| Module Type        | React/Next Component Example                           |
| ------------------ | ------------------------------------------------------ |
| `hero_fullscreen`  | `<HeroVideoLoop />` with CTA buttons                   |
| `animated_cards`   | `<CardSlider autoScroll />` with emojis/icons          |
| `tabbed_grid`      | `<Tabs variant="soft" />` + `<GridCards />`            |
| `product_showcase` | `<MasonryGrid />` or `<Carousel with Thumbnails />`    |
| `parallax_section` | `<ParallaxBanner />` or `<ScrollFadeIn />`             |
| `strain_cards`     | `<FilterableStrainCards />` with hover states          |
| `timeline`         | `<VerticalTimeline animate />`                         |
| `icon_list`        | `<FeatureIcons />` with framer-motion                  |
| `accordion_list`   | `<FAQAccordion />` styled with theme colors            |
| `cta_banner`       | `<AnnouncementBar />` with link to newsletter or drops |
| `glitch_text`      | `<TextWithGlitchEffect />` or animated typewriter      |
| `split_text_media` | `<SplitMediaText layout="reversed" />`                 |
