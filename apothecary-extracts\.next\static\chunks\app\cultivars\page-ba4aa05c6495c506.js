(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[56],{3240:(e,a,i)=>{Promise.resolve().then(i.bind(i,5650))},5650:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>h});var t=i(5155),s=i(2115),r=i(1934),n=i(8126),l=i.n(n),c=i(5506),o=i(3900),d=i(2056),m=i(2257);let x=[{type:"Sativa",description:"Energizing and uplifting effects, ideal for daytime use",color:"from-gold-500 to-gold-600",icon:"☀️"},{type:"Indica",description:"Relaxing and sedating effects, perfect for evening use",color:"from-primary-600 to-primary-700",icon:"\uD83C\uDF19"},{type:"Hybrid",description:"Balanced effects combining the best of both worlds",color:"from-sage-400 to-sage-500",icon:"⚖️"}],p=[{name:"<PERSON>ya Cake",type:"Hybrid",thc:"22-26%",cbd:"<1%",effects:["Relaxed","Euphoric","Creative"],flavors:["Tropical","Sweet","Creamy"],description:"A delightful hybrid that combines tropical sweetness with relaxing effects. Perfect for creative endeavors and social situations.",image:"/api/placeholder/300/200",genetics:"Papaya \xd7 Wedding Cake"},{name:"GMO Crasher",type:"Indica",thc:"24-28%",cbd:"<1%",effects:["Sedating","Relaxed","Sleepy"],flavors:["Garlic","Diesel","Earthy"],description:"A potent indica-dominant strain known for its unique savory profile and powerful relaxing effects. Ideal for evening use.",image:"/api/placeholder/300/200",genetics:"GMO \xd7 Wedding Crasher"},{name:"Pineapple Habanero",type:"Sativa",thc:"20-24%",cbd:"<1%",effects:["Energetic","Creative","Focused"],flavors:["Pineapple","Spicy","Citrus"],description:"An invigorating sativa with a unique sweet and spicy flavor profile. Excellent for daytime productivity and creative projects.",image:"/api/placeholder/300/200",genetics:"Pineapple Express \xd7 Habanero Haze"},{name:"Purple Sunset",type:"Hybrid",thc:"18-22%",cbd:"1-2%",effects:["Balanced","Calm","Happy"],flavors:["Berry","Grape","Floral"],description:"A beautifully balanced hybrid with stunning purple hues and a complex berry flavor profile. Perfect for any time of day.",image:"/api/placeholder/300/200",genetics:"Purple Punch \xd7 Sunset Sherbet"},{name:"Colorado Kush",type:"Indica",thc:"19-23%",cbd:"<1%",effects:["Relaxed","Sleepy","Pain Relief"],flavors:["Pine","Earthy","Woody"],description:"A classic Colorado strain with traditional kush characteristics. Known for its therapeutic benefits and classic cannabis flavor.",image:"/api/placeholder/300/200",genetics:"Local Colorado Genetics"},{name:"Citrus Burst",type:"Sativa",thc:"21-25%",cbd:"<1%",effects:["Uplifting","Energetic","Social"],flavors:["Citrus","Lemon","Orange"],description:"A bright and energizing sativa with an explosive citrus flavor. Perfect for social gatherings and outdoor activities.",image:"/api/placeholder/300/200",genetics:"Tangie \xd7 Super Lemon Haze"}];function h(){let[e,a]=(0,s.useState)(!1),[i,n]=(0,s.useState)("All"),{cultivars:h}=m.CQ.pages,f="All"===i?p:p.filter(e=>e.type===i);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(l(),{children:[(0,t.jsx)("title",{children:h.seo.title}),(0,t.jsx)("meta",{name:"description",content:h.seo.description}),(0,t.jsx)("meta",{property:"og:title",content:h.seo.title}),(0,t.jsx)("meta",{property:"og:description",content:h.seo.description}),(0,t.jsx)("meta",{name:"age-restriction",content:"21+"})]}),!e&&(0,t.jsx)(o.A,{onVerified:()=>a(!0)}),e&&(0,t.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,t.jsx)(c.A,{}),(0,t.jsxs)("main",{children:[(0,t.jsxs)("section",{className:"relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden py-24 lg:py-32",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,t.jsx)("div",{className:"absolute inset-0 bg-primary-900"})}),(0,t.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,t.jsx)(r.P.h1,{className:"text-4xl md:text-6xl font-serif font-bold leading-tight mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:h.hero.headline}),(0,t.jsx)(r.P.p,{className:"text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:h.hero.subheadline})]})]}),(0,t.jsx)("section",{className:"py-8 bg-cream-100",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)(d.A,{})})}),(0,t.jsx)("section",{className:"py-16 bg-cream-100",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.P.div,{className:"text-center mb-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-primary-800 mb-4",children:"Cannabis Categories"}),(0,t.jsx)("p",{className:"text-lg text-charcoal-600 max-w-3xl mx-auto",children:"Understanding the differences between Sativa, Indica, and Hybrid strains helps you choose the perfect cannabis for your needs."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:x.map((e,a)=>(0,t.jsxs)(r.P.div,{className:"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.3,delay:.1*a},viewport:{once:!0},children:[(0,t.jsx)("div",{className:"h-20 bg-gradient-to-br ".concat(e.color," flex items-center justify-center"),children:(0,t.jsxs)("div",{className:"text-center text-cream-50",children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,t.jsx)("div",{className:"font-semibold",children:e.type})]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("p",{className:"text-charcoal-600 text-sm leading-relaxed",children:e.description})})]},e.type))}),(0,t.jsx)(r.P.div,{className:"flex flex-wrap justify-center gap-4 mb-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:["All","Sativa","Indica","Hybrid"].map(e=>(0,t.jsx)("button",{onClick:()=>n(e),className:"px-6 py-3 rounded-lg font-medium transition-colors duration-200 ".concat(i===e?"bg-primary-800 text-cream-50":"bg-cream-50 text-primary-800 hover:bg-primary-100"),children:e},e))})]})}),(0,t.jsx)("section",{className:"py-20 bg-cream-200",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,t.jsx)("h2",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Featured Strains"}),(0,t.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed",children:"Discover our current selection of premium cannabis strains, each with unique characteristics and carefully documented genetics."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:f.map((e,a)=>(0,t.jsxs)(r.P.div,{className:"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{scale:1.02,y:-4},transition:{duration:.3,delay:.1*a},viewport:{once:!0},children:[(0,t.jsxs)("div",{className:"h-48 bg-gradient-to-br from-primary-200 to-primary-300 relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),(0,t.jsx)("div",{className:"absolute top-4 left-4",children:(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("Sativa"===e.type?"bg-gold-500 text-cream-50":"Indica"===e.type?"bg-primary-700 text-cream-50":"bg-sage-500 text-cream-50"),children:e.type})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-charcoal-500 mb-3",children:e.genetics}),(0,t.jsx)("p",{className:"text-charcoal-600 text-sm mb-4 leading-relaxed",children:e.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-primary-800",children:"THC:"}),(0,t.jsx)("span",{className:"ml-1 text-charcoal-600",children:e.thc})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-primary-800",children:"CBD:"}),(0,t.jsx)("span",{className:"ml-1 text-charcoal-600",children:e.cbd})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-primary-800 mb-2",children:"Effects:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.effects.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-primary-800 mb-2",children:"Flavors:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.flavors.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-gold-100 text-gold-700 text-xs rounded-full",children:e},e))})]})]})]},e.name))})]})}),(0,t.jsx)("section",{className:"py-20 bg-primary-800",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,t.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Find Your Perfect Strain"}),(0,t.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"Visit our dispensary to explore these strains in person and get expert recommendations from our knowledgeable budtenders."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)("a",{href:"/locations",className:"inline-flex items-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200",children:"Visit Our Dispensary"}),(0,t.jsx)("a",{href:"/products",className:"inline-flex items-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200",children:"Browse All Products"})]})]})})})]}),(0,t.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-8",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,t.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase"})})})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[874,934,459,441,684,358],()=>a(3240)),_N_E=e.O()}]);