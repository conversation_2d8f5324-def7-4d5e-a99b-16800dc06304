'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FAQAccordionProps, FAQItem } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * FAQAccordion Component
 * 
 * Create an FAQ accordion with smooth animations and cannabis industry specific content.
 * Features search functionality, categories, and smooth expand/collapse animations.
 * 
 * @example
 * ```tsx
 * <FAQAccordion
 *   items={faqItems}
 *   allowMultiple={false}
 *   defaultOpen={['faq-1']}
 *   showCategories={true}
 *   enableSearch={true}
 * />
 * ```
 */
const FAQAccordion: React.FC<FAQAccordionProps> = ({
  items,
  allowMultiple = false,
  defaultOpen = [],
  showCategories = false,
  enableSearch = false,
  className = '',
  style,
}) => {
  const [openItems, setOpenItems] = useState<string[]>(defaultOpen);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(items.map(item => item.category).filter(Boolean)));
    return cats as string[];
  }, [items]);

  // Filter items based on search and category
  const filteredItems = useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory) {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    return filtered;
  }, [items, searchTerm, selectedCategory]);

  // Toggle item open/closed
  const toggleItem = (itemId: string) => {
    setOpenItems(prev => {
      if (allowMultiple) {
        return prev.includes(itemId)
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId];
      } else {
        return prev.includes(itemId) ? [] : [itemId];
      }
    });
  };

  // Clear search and filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: brandAnimations.easing.smooth
      }
    }
  };

  return (
    <div className={`faq-accordion ${className}`} style={style}>
      {/* Search and Filter Controls */}
      {(enableSearch || showCategories) && (
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          {enableSearch && (
            <div className="relative max-w-md">
              <input
                type="text"
                placeholder="Search FAQs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <svg className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          )}

          {/* Category Filter */}
          {showCategories && categories.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory('')}
                className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${
                  selectedCategory === ''
                    ? 'bg-primary-600 text-cream-50 border-primary-600'
                    : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
                }`}
              >
                All Categories
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 capitalize ${
                    selectedCategory === category
                      ? 'bg-primary-600 text-cream-50 border-primary-600'
                      : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          )}

          {/* Active Filters */}
          {(searchTerm || selectedCategory) && (
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-2">
                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700">
                    Search: "{searchTerm}"
                  </span>
                )}
                {selectedCategory && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700 capitalize">
                    Category: {selectedCategory}
                  </span>
                )}
              </div>
              <button
                onClick={clearFilters}
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Clear Filters
              </button>
            </div>
          )}

          {/* Results Count */}
          <p className="text-sm text-gray-600">
            Showing {filteredItems.length} of {items.length} questions
          </p>
        </div>
      )}

      {/* FAQ Items */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: '-50px' }}
        className="space-y-4"
      >
        <AnimatePresence>
          {filteredItems.map((item) => (
            <FAQItemComponent
              key={item.id}
              item={item}
              isOpen={openItems.includes(item.id)}
              onToggle={() => toggleItem(item.id)}
              itemVariants={itemVariants}
            />
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Empty State */}
      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
};

// Individual FAQ Item Component
interface FAQItemComponentProps {
  item: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
  itemVariants: any;
}

const FAQItemComponent: React.FC<FAQItemComponentProps> = ({
  item,
  isOpen,
  onToggle,
  itemVariants,
}) => {
  return (
    <motion.div
      variants={itemVariants}
      layout
      className="faq-item bg-cream-50 rounded-lg overflow-hidden"
      style={{ boxShadow: brandShadows.soft }}
    >
      {/* Question */}
      <motion.button
        onClick={onToggle}
        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-cream-100 transition-colors duration-200"
        whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}
        whileTap={{ scale: 0.99 }}
      >
        <span className="text-lg font-semibold text-charcoal-800 pr-4">
          {item.question}
        </span>
        
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2, ease: brandAnimations.easing.smooth }}
          className="flex-shrink-0"
        >
          <svg 
            className="w-5 h-5 text-primary-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M19 9l-7 7-7-7" 
            />
          </svg>
        </motion.div>
      </motion.button>

      {/* Answer */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: brandAnimations.easing.smooth 
            }}
            className="overflow-hidden"
          >
            <div className="px-6 pb-4">
              <div className="border-t border-cream-200 pt-4">
                <p className="text-charcoal-600 leading-relaxed">
                  {item.answer}
                </p>
                
                {/* Category Badge */}
                {item.category && (
                  <div className="mt-3">
                    <span className="inline-block px-3 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full capitalize">
                      {item.category}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default FAQAccordion;
