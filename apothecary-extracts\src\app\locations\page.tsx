'use client';

import Navigation from '@/components/Navigation';
import LocationInfo from '@/components/LocationInfo';
import ComplianceDisclaimer from '@/components/ComplianceDisclaimer';

export default function LocationsPage() {
  return (
    <div className="min-h-screen bg-cream-100">
      <Navigation />
      
      <main>
        {/* Header */}
        <section className="bg-gradient-to-br from-primary-800 to-primary-600 text-cream-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl sm:text-5xl font-serif font-bold mb-4">
                Our Locations
              </h1>
              <p className="text-xl text-cream-200 max-w-3xl mx-auto">
                Visit us at our convenient Colorado Springs locations for the full Apothecary Extracts experience.
              </p>
            </div>
          </div>
        </section>

        {/* Compliance */}
        <section className="py-8 bg-cream-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ComplianceDisclaimer />
          </div>
        </section>

        {/* Locations */}
        <LocationInfo />

        {/* Additional Info */}
        <section className="py-16 bg-cream-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-serif font-bold text-primary-800 mb-6">
                  What to Expect
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1">
                      <svg className="w-3 h-3 text-cream-50" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-primary-800 mb-1">Expert Consultation</h3>
                      <p className="text-charcoal-600">Our knowledgeable budtenders will help you find the perfect products for your needs.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1">
                      <svg className="w-3 h-3 text-cream-50" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-primary-800 mb-1">Premium Selection</h3>
                      <p className="text-charcoal-600">Browse our extensive collection of lab-tested, high-quality cannabis products.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1">
                      <svg className="w-3 h-3 text-cream-50" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-primary-800 mb-1">Safe Environment</h3>
                      <p className="text-charcoal-600">Clean, secure, and welcoming dispensary environment with strict safety protocols.</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h2 className="text-3xl font-serif font-bold text-primary-800 mb-6">
                  Visit Requirements
                </h2>
                <div className="bg-gold-100 rounded-lg p-6">
                  <ul className="space-y-3 text-charcoal-700">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-gold-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Must be 21+ years old
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-gold-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Valid government-issued ID required
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-gold-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Cash, debit, or CanPay accepted
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-gold-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Medical patients welcome
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-charcoal-800 text-cream-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="text-2xl font-serif font-bold text-cream-50 mb-4">
              Apothecary Extracts
            </div>
            <p className="text-cream-300 mb-4">
              Colorado&apos;s premier cannabis dispensary
            </p>
            <p className="text-cream-400 text-sm">
              © 2025 Apothecary Extracts. All rights reserved. | Must be 21+ to purchase
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
