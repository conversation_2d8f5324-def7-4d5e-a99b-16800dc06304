'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface TimelineEvent {
  id: string;
  date: string;
  title: string;
  description: string;
  icon?: ReactNode;
  image?: string;
  category?: string;
  featured?: boolean;
}

interface VerticalTimelineProps {
  events: TimelineEvent[];
  animate?: boolean;
  alternating?: boolean;
  showImages?: boolean;
  className?: string;
}

export default function VerticalTimeline({
  events,
  animate = true,
  alternating = true,
  showImages = true,
  className = ''
}: VerticalTimelineProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const eventVariants = {
    hidden: (isRight: boolean) => ({
      opacity: 0,
      x: isRight ? 50 : -50,
      y: 30
    }),
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: 'spring',
        stiffness: 500,
        damping: 30,
        delay: 0.2
      }
    }
  };

  const lineVariants = {
    hidden: { height: 0 },
    visible: {
      height: '100%',
      transition: {
        duration: 1.5,
        ease: 'easeInOut'
      }
    }
  };

  return (
    <motion.div
      variants={animate ? containerVariants : undefined}
      initial={animate ? 'hidden' : undefined}
      whileInView={animate ? 'visible' : undefined}
      viewport={{ once: true, margin: '-100px' }}
      className={`relative ${className}`}
    >
      {/* Timeline Line */}
      <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-cream-300 h-full">
        {animate && (
          <motion.div
            variants={lineVariants}
            className="w-full bg-primary-600 origin-top"
          />
        )}
      </div>

      {/* Timeline Events */}
      <div className="space-y-12">
        {events.map((event, index) => {
          const isRight = alternating ? index % 2 === 1 : false;
          const isLeft = alternating ? index % 2 === 0 : true;

          return (
            <motion.div
              key={event.id}
              custom={isRight}
              variants={animate ? eventVariants : undefined}
              className="relative flex items-center"
            >
              {/* Left Content */}
              {isLeft && (
                <div className="w-1/2 pr-8 text-right">
                  <div className="bg-cream-50 rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300">
                    {/* Date */}
                    <div className="text-primary-600 font-semibold text-sm mb-2">
                      {event.date}
                    </div>

                    {/* Category */}
                    {event.category && (
                      <div className="inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3">
                        {event.category}
                      </div>
                    )}

                    {/* Title */}
                    <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>
                      {event.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 leading-relaxed">
                      {event.description}
                    </p>

                    {/* Image */}
                    {showImages && event.image && (
                      <div className="mt-4 rounded-lg overflow-hidden">
                        <img
                          src={event.image}
                          alt={event.title}
                          className="w-full h-32 object-cover"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Center Icon */}
              <div className="absolute left-1/2 transform -translate-x-1/2 z-10">
                <motion.div
                  variants={animate ? iconVariants : undefined}
                  className={`w-12 h-12 rounded-full flex items-center justify-center shadow-medium ${
                    event.featured
                      ? 'bg-gold-500 text-primary-800'
                      : 'bg-primary-600 text-cream-50'
                  }`}
                >
                  {event.icon || (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                </motion.div>

                {/* Pulse Animation */}
                {event.featured && animate && (
                  <motion.div
                    className="absolute inset-0 rounded-full bg-gold-400"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.7, 0, 0.7]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                  />
                )}
              </div>

              {/* Right Content */}
              {isRight && (
                <div className="w-1/2 pl-8">
                  <div className="bg-cream-50 rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300">
                    {/* Date */}
                    <div className="text-primary-600 font-semibold text-sm mb-2">
                      {event.date}
                    </div>

                    {/* Category */}
                    {event.category && (
                      <div className="inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3">
                        {event.category}
                      </div>
                    )}

                    {/* Title */}
                    <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>
                      {event.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 leading-relaxed">
                      {event.description}
                    </p>

                    {/* Image */}
                    {showImages && event.image && (
                      <div className="mt-4 rounded-lg overflow-hidden">
                        <img
                          src={event.image}
                          alt={event.title}
                          className="w-full h-32 object-cover"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Mobile Layout (Full Width) */}
              <div className="md:hidden w-full pl-16">
                <div className="bg-cream-50 rounded-xl shadow-soft p-6">
                  <div className="text-primary-600 font-semibold text-sm mb-2">
                    {event.date}
                  </div>
                  
                  {event.category && (
                    <div className="inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3">
                      {event.category}
                    </div>
                  )}
                  
                  <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>
                    {event.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed">
                    {event.description}
                  </p>

                  {showImages && event.image && (
                    <div className="mt-4 rounded-lg overflow-hidden">
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-32 object-cover"
                      />
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
}

// Example usage with cannabis company timeline
export function ExampleVerticalTimeline() {
  const companyEvents: TimelineEvent[] = [
    {
      id: '1',
      date: '2015',
      title: 'Company Founded',
      description: 'Apothecary Extracts was established with a vision to provide premium cannabis products to Colorado residents.',
      category: 'Milestone',
      featured: true,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
        </svg>
      )
    },
    {
      id: '2',
      date: '2017',
      title: 'First Retail Location',
      description: 'Opened our flagship dispensary in Colorado Springs, bringing premium cannabis directly to our community.',
      category: 'Expansion',
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 2h6v4H7V6zm8 8v2a1 1 0 01-1 1H6a1 1 0 01-1-1v-2h8z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '3',
      date: '2019',
      title: 'Extraction Facility Launch',
      description: 'Launched our state-of-the-art extraction facility, enabling us to produce premium concentrates and extracts.',
      category: 'Innovation',
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '4',
      date: '2021',
      title: 'Organic Certification',
      description: 'Achieved organic certification for our cultivation practices, demonstrating our commitment to sustainable, natural growing methods.',
      category: 'Achievement',
      featured: true,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '5',
      date: '2023',
      title: 'Second Location Opening',
      description: 'Expanded to Denver Metro area, bringing our premium products and expert service to a wider Colorado community.',
      category: 'Expansion',
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '6',
      date: '2024',
      title: 'Innovation Lab',
      description: 'Launched our research and development lab focused on developing new extraction techniques and product formulations.',
      category: 'Innovation',
      featured: true,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      )
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-serif font-bold text-foreground mb-4">
          Our Journey
        </h2>
        <p className="text-lg text-gray-600">
          Nearly a decade of innovation, growth, and commitment to excellence
        </p>
      </div>
      
      <VerticalTimeline events={companyEvents} animate={true} alternating={true} />
    </div>
  );
}
