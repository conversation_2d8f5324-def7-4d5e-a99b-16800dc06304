'use client';

import React, { useEffect, useRef } from 'react';
import { NoiseOverlayGrainProps } from '../../types/visual';
import { brandColors } from '../../styles/brand';

/**
 * NoiseOverlayGrain Component
 * 
 * Adds subtle texture and analog warmth to full-page backgrounds.
 * Features animated film-grain texture with mint-green tint.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <NoiseOverlayGrain opacity={0.05} tintColor="#2FB886" />
 *   <h1>Hero Content</h1>
 * </div>
 * ```
 */
const NoiseOverlayGrain: React.FC<NoiseOverlayGrainProps> = ({
  className = '',
  style,
  opacity = 0.04,
  blendMode = 'overlay',
  animationSpeed = 3000,
  tintColor = brandColors.apothecary,
  grainSize = 'medium',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  // Generate noise pattern
  const generateNoise = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const imageData = ctx.createImageData(width, height);
    const data = imageData.data;

    // Convert tint color to RGB
    const tintRGB = hexToRgb(tintColor);

    for (let i = 0; i < data.length; i += 4) {
      const noise = Math.random() * 255;
      
      // Apply tint color
      data[i] = Math.min(255, noise + tintRGB.r * 0.1);     // Red
      data[i + 1] = Math.min(255, noise + tintRGB.g * 0.1); // Green
      data[i + 2] = Math.min(255, noise + tintRGB.b * 0.1); // Blue
      data[i + 3] = noise * opacity * 255;                  // Alpha
    }

    ctx.putImageData(imageData, 0, 0);
  };

  // Convert hex color to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 47, g: 184, b: 134 }; // Default to apothecary green
  };

  // Get grain size multiplier
  const getGrainSizeMultiplier = () => {
    switch (grainSize) {
      case 'small':
        return 0.5;
      case 'medium':
        return 1;
      case 'large':
        return 2;
      default:
        return 1;
    }
  };

  // Animation loop
  const animate = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Generate new noise pattern
    generateNoise(ctx, canvas.width, canvas.height);

    // Schedule next frame
    animationRef.current = setTimeout(() => {
      requestAnimationFrame(animate);
    }, animationSpeed);
  };

  // Setup canvas and start animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Set canvas size based on grain size
    const sizeMultiplier = getGrainSizeMultiplier();
    canvas.width = Math.floor(window.innerWidth * sizeMultiplier);
    canvas.height = Math.floor(window.innerHeight * sizeMultiplier);

    // Start animation
    animate();

    // Handle window resize
    const handleResize = () => {
      canvas.width = Math.floor(window.innerWidth * sizeMultiplier);
      canvas.height = Math.floor(window.innerHeight * sizeMultiplier);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [animationSpeed, grainSize, tintColor, opacity]);

  return (
    <canvas
      ref={canvasRef}
      className={`noise-overlay-grain absolute inset-0 pointer-events-none ${className}`}
      style={{
        mixBlendMode: blendMode,
        opacity,
        zIndex: 1,
        ...style,
      }}
    />
  );
};

export default NoiseOverlayGrain;
