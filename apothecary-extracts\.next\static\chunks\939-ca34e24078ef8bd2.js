"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[939],{1177:(e,r,s)=>{s.d(r,{A:()=>l});var a=s(5155),t=s(6874),i=s.n(t),o=s(1934);let n=[{name:"Premium Flower",description:"Hand-selected, top-shelf cannabis flower with exceptional quality and potency.",image:"/api/placeholder/400/300",href:"/products/flower",features:["Lab Tested","Organic Grown","Various Strains"],color:"from-primary-600 to-primary-700"},{name:"Concentrates & Extracts",description:"Pure, potent concentrates including wax, shatter, and live resin.",image:"/api/placeholder/400/300",href:"/products/concentrates",features:["High Potency","Pure Extraction","Multiple Forms"],color:"from-gold-500 to-gold-600"},{name:"Edibles",description:"Delicious, precisely dosed edibles for a controlled cannabis experience.",image:"/api/placeholder/400/300",href:"/products/edibles",features:["Precise Dosing","Great Taste","Long Lasting"],color:"from-sage-400 to-sage-500"},{name:"Topicals",description:"Therapeutic cannabis topicals for localized relief and wellness.",image:"/api/placeholder/400/300",href:"/products/topicals",features:["Non-Psychoactive","Therapeutic","Natural Relief"],color:"from-cream-400 to-cream-500"}];function l(){return(0,a.jsx)("section",{className:"py-20 bg-cream-100",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(o.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-4xl font-serif font-bold text-primary-800 mb-4",children:"Our Product Categories"}),(0,a.jsx)("p",{className:"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed",children:"Explore our carefully curated selection of premium cannabis products, each category offering unique benefits and experiences."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:n.map((e,r)=>(0,a.jsxs)(o.P.div,{className:"group bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{scale:1.05,y:-8},transition:{duration:.3,delay:.1*r},viewport:{once:!0},children:[(0,a.jsxs)("div",{className:"h-48 bg-gradient-to-br ".concat(e.color," relative overflow-hidden"),children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-cream-50",children:(0,a.jsxs)("div",{className:"w-16 h-16 mx-auto mb-2 bg-cream-50 bg-opacity-20 rounded-full flex items-center justify-center",children:[0===r&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),1===r&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}),2===r&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),3===r&&(0,a.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"})})]})})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-charcoal-600 text-sm mb-4 leading-relaxed",children:e.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.features.map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full",children:e},e))}),(0,a.jsxs)(i(),{href:e.href,className:"inline-flex items-center text-primary-700 hover:text-primary-800 font-medium text-sm group-hover:underline transition-colors duration-200",children:["Explore ",e.name,(0,a.jsx)("svg",{className:"ml-1 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.name))}),(0,a.jsx)(o.P.div,{className:"text-center mt-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},children:(0,a.jsxs)(i(),{href:"/products",className:"inline-flex items-center px-8 py-4 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:["View All Products",(0,a.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})})}},3900:(e,r,s)=>{s.d(r,{A:()=>i});var a=s(5155),t=s(2115);function i(e){let{onVerified:r}=e,[s,i]=(0,t.useState)(!1);(0,t.useEffect)(()=>{localStorage.getItem("ageVerified")?r():i(!0)},[r]);let o=e=>{e?(localStorage.setItem("ageVerified","true"),i(!1),r()):window.location.href="https://www.samhsa.gov/marijuana"};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-serif font-bold text-primary-800 mb-2",children:"Age Verification Required"}),(0,a.jsx)("p",{className:"text-charcoal-700 text-sm leading-relaxed",children:"You must be 21 years of age or older to view this website and purchase cannabis products. Please verify your age to continue."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>o(!0),className:"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",children:"Yes, I'm 21 or older"}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2",children:"No, I'm under 21"})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-charcoal-200",children:(0,a.jsx)("p",{className:"text-xs text-charcoal-500 leading-relaxed",children:"By entering this website, you certify that you are of legal age to purchase cannabis products in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended to diagnose, treat, cure, or prevent any disease."})})]})}):null}},5506:(e,r,s)=>{s.d(r,{A:()=>n});var a=s(5155),t=s(2115),i=s(6874),o=s.n(i);function n(){let[e,r]=(0,t.useState)(!1),s=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"Cultivars",href:"/cultivars"},{name:"Deals",href:"/deals"},{name:"Locations",href:"/locations"},{name:"About",href:"/about"}];return(0,a.jsxs)("nav",{className:"bg-cream-50 shadow-soft sticky top-0 z-40",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(o(),{href:"/",className:"flex items-center",children:(0,a.jsx)("div",{className:"text-2xl font-serif font-bold text-primary-800",children:"Apothecary Extracts"})})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-baseline space-x-8",children:s.map(e=>(0,a.jsx)(o(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200",children:e.name},e.name))})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("button",{onClick:()=>r(!e),className:"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600","aria-expanded":"false",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),e?(0,a.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):(0,a.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})})]})}),e&&(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200",children:s.map(e=>(0,a.jsx)(o(),{href:e.href,className:"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200",onClick:()=>r(!1),children:e.name},e.name))})})]})}},5720:(e,r,s)=>{s.d(r,{A:()=>n});var a=s(5155),t=s(6874),i=s.n(t),o=s(1934);function n(e){let{videoSrc:r,headline:s="Premium Cannabis",subheadline:t="Discover Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity.",ctas:n=["Shop Products","Find Locations"]}=e;return(0,a.jsxs)("section",{className:"relative h-screen overflow-hidden bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50",children:[r&&(0,a.jsx)("video",{autoPlay:!0,muted:!0,loop:!0,playsInline:!0,className:"absolute w-full h-full object-cover opacity-30",children:(0,a.jsx)("source",{src:r,type:"video/mp4"})}),!r&&(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsx)("div",{className:"absolute inset-0 bg-primary-900"})}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col items-center justify-center h-full text-center px-6",children:[(0,a.jsxs)(o.P.h1,{className:"text-4xl md:text-6xl font-serif font-bold leading-tight mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:[s,(0,a.jsx)("span",{className:"block text-gold-300",children:"Excellence"})]}),(0,a.jsx)(o.P.p,{className:"text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:t}),(0,a.jsxs)(o.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[(0,a.jsxs)(i(),{href:"/products",className:"inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:[n[0]||"Shop Products",(0,a.jsx)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]}),(0,a.jsxs)(i(),{href:"/locations",className:"inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cream-50 focus:ring-offset-2 focus:ring-offset-primary-800",children:[n[1]||"Find Locations",(0,a.jsxs)("svg",{className:"ml-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})]})]})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-20",children:(0,a.jsx)("svg",{viewBox:"0 0 1440 120",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z",fill:"#f8f6f0"})})})]})}}}]);