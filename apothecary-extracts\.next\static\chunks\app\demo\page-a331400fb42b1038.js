(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[436],{4426:(e,s,a)=>{Promise.resolve().then(a.bind(a,6374))},6374:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var n=a(5155),i=a(2115),t=a(5506),c=a(3900),l=a(5720),r=a(1177);function d(){let[e,s]=(0,i.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[!e&&(0,n.jsx)(c.A,{onVerified:()=>s(!0)}),e&&(0,n.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,n.jsx)(t.A,{}),(0,n.jsxs)("main",{children:[(0,n.jsx)(l.A,{headline:"Premium Cannabis Excellence",subheadline:"Experience Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity with cutting-edge extraction techniques.",ctas:["Explore Products","Visit Locations"]}),(0,n.jsx)(r.A,{}),(0,n.jsx)("section",{className:"py-20 bg-primary-800",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,n.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Enhanced Components Demo"}),(0,n.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"This page demonstrates the enhanced Hero and ProductShowcase components with framer-motion animations."}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 text-left",children:[(0,n.jsxs)("div",{className:"bg-cream-50 p-6 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-3",children:"Hero Enhancements"}),(0,n.jsxs)("ul",{className:"text-charcoal-600 space-y-2",children:[(0,n.jsx)("li",{children:"• Video background support"}),(0,n.jsx)("li",{children:"• Smooth fade-in animations"}),(0,n.jsx)("li",{children:"• Customizable headlines and CTAs"}),(0,n.jsx)("li",{children:"• Professional design system integration"}),(0,n.jsx)("li",{children:"• Full-screen responsive layout"})]})]}),(0,n.jsxs)("div",{className:"bg-cream-50 p-6 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-3",children:"ProductShowcase Enhancements"}),(0,n.jsxs)("ul",{className:"text-charcoal-600 space-y-2",children:[(0,n.jsx)("li",{children:"• Staggered card animations"}),(0,n.jsx)("li",{children:"• Hover scale effects"}),(0,n.jsx)("li",{children:"• Scroll-triggered animations"}),(0,n.jsx)("li",{children:"• Maintained professional styling"}),(0,n.jsx)("li",{children:"• Improved user engagement"})]})]})]})]})})]}),(0,n.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-8",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,n.jsx)("p",{className:"text-cream-400 text-sm",children:"Demo Page - Enhanced Components | Apothecary Extracts"})})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,934,939,441,684,358],()=>s(4426)),_N_E=e.O()}]);