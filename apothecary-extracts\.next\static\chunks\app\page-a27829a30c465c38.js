(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3792:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var a=r(5155),c=r(2115),t=r(5506),l=r(3900),i=r(5720),o=r(1177),n=r(1225),x=r(2056);function d(){let[e,s]=(0,c.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[!e&&(0,a.jsx)(l.A,{onVerified:()=>s(!0)}),e&&(0,a.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,a.jsx)(t.A,{}),(0,a.jsxs)("main",{children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("section",{className:"py-8 bg-cream-100",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)(x.A,{})})}),(0,a.jsx)(o.A,{}),(0,a.jsx)(n.A,{}),(0,a.jsx)("section",{className:"py-20 bg-primary-800",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Stay Updated with Apothecary Extracts"}),(0,a.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"Get the latest news on new products, special offers, and cannabis education."}),(0,a.jsxs)("form",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent text-charcoal-800",required:!0}),(0,a.jsx)("button",{type:"submit",className:"px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800",children:"Subscribe"})]}),(0,a.jsx)("p",{className:"text-sm text-cream-300 mt-4",children:"We respect your privacy. Unsubscribe at any time."})]})})]}),(0,a.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsx)("div",{className:"text-2xl font-serif font-bold text-cream-50 mb-4",children:"Apothecary Extracts"}),(0,a.jsx)("p",{className:"text-cream-300 mb-4 leading-relaxed",children:"Colorado's premier cannabis dispensary, committed to providing the highest quality products and exceptional customer service."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,a.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,a.jsxs)("a",{href:"#",className:"text-cream-300 hover:text-gold-400 transition-colors",children:[(0,a.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"})})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/locations",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Locations"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/education",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Education"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-cream-50 mb-4",children:"Legal"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/privacy",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Privacy Policy"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/terms",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Terms of Service"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/compliance",className:"text-cream-300 hover:text-gold-400 transition-colors",children:"Compliance"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-charcoal-600 mt-12 pt-8 text-center",children:(0,a.jsx)("p",{className:"text-cream-400 text-sm",children:"\xa9 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase"})})]})})]})]})}},6971:(e,s,r)=>{Promise.resolve().then(r.bind(r,3792))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,934,939,622,441,684,358],()=>s(6971)),_N_E=e.O()}]);