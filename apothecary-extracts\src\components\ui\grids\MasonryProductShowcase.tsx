'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useMemo, useEffect, useRef } from 'react';
import Image from 'next/image';
import { MasonryProductShowcaseProps, Product } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * MasonryProductShowcase Component
 * 
 * A masonry/carousel layout for dynamic product displays with filtering capabilities.
 * Features responsive masonry grid with smooth animations and filter controls.
 * 
 * @example
 * ```tsx
 * <MasonryProductShowcase
 *   products={products}
 *   columns={3}
 *   gap={20}
 *   carouselMode={false}
 *   filters={[
 *     { id: 'category', name: 'Category', value: 'flower' },
 *     { id: 'price', name: 'Price Range', value: 'under-50' }
 *   ]}
 *   onProductClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */
const MasonryProductShowcase: React.FC<MasonryProductShowcaseProps> = ({
  products,
  columns = 3,
  gap = 20,
  carouselMode = false,
  filters = [],
  onProductClick,
  className = '',
  style,
}) => {
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});
  const [currentSlide, setCurrentSlide] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Filter products based on active filters
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      return Object.entries(activeFilters).every(([filterId, filterValue]) => {
        if (!filterValue) return true;
        
        switch (filterId) {
          case 'category':
            return product.category === filterValue;
          case 'price':
            if (filterValue === 'under-25') return product.price < 25;
            if (filterValue === '25-50') return product.price >= 25 && product.price <= 50;
            if (filterValue === '50-100') return product.price > 50 && product.price <= 100;
            if (filterValue === 'over-100') return product.price > 100;
            return true;
          case 'effects':
            return product.effects?.includes(filterValue);
          default:
            return true;
        }
      });
    });
  }, [products, activeFilters]);

  // Handle filter changes
  const handleFilterChange = (filterId: string, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterId]: prev[filterId] === value ? '' : value
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setActiveFilters({});
  };

  // Carousel navigation
  const nextSlide = () => {
    if (carouselMode) {
      setCurrentSlide(prev => (prev + 1) % Math.ceil(filteredProducts.length / columns));
    }
  };

  const prevSlide = () => {
    if (carouselMode) {
      setCurrentSlide(prev => prev === 0 ? Math.ceil(filteredProducts.length / columns) - 1 : prev - 1);
    }
  };

  // Auto-advance carousel
  useEffect(() => {
    if (carouselMode) {
      const interval = setInterval(nextSlide, 5000);
      return () => clearInterval(interval);
    }
  }, [carouselMode]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: brandAnimations.easing.smooth
      }
    },
    hover: { y: -8, scale: 1.02 }
  };

  // Get random height for masonry effect
  const getRandomHeight = (index: number) => {
    const heights = [200, 250, 300, 350];
    return heights[index % heights.length];
  };

  return (
    <div className={`masonry-product-showcase ${className}`} style={style}>
      {/* Filter Controls */}
      {filters.length > 0 && (
        <div className="mb-8">
          <div className="flex flex-wrap gap-4 items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-charcoal-800">Filter Products</h3>
            {Object.keys(activeFilters).some(key => activeFilters[key]) && (
              <button
                onClick={clearFilters}
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Clear All Filters
              </button>
            )}
          </div>
          
          <div className="flex flex-wrap gap-3">
            {filters.map((filter) => (
              <div key={filter.id} className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleFilterChange(filter.id, filter.value)}
                  className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${
                    activeFilters[filter.id] === filter.value
                      ? 'bg-primary-600 text-cream-50 border-primary-600'
                      : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
                  }`}
                >
                  {filter.name}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          Showing {filteredProducts.length} of {products.length} products
        </p>
      </div>

      {/* Masonry Grid */}
      <div ref={containerRef} className="relative">
        {carouselMode ? (
          // Carousel Mode
          <div className="overflow-hidden">
            <motion.div
              className="flex transition-transform duration-500 ease-out"
              style={{
                transform: `translateX(-${currentSlide * 100}%)`,
                gap: `${gap}px`
              }}
            >
              {Array.from({ length: Math.ceil(filteredProducts.length / columns) }).map((_, slideIndex) => (
                <div
                  key={slideIndex}
                  className="flex-shrink-0 w-full grid gap-4"
                  style={{
                    gridTemplateColumns: `repeat(${columns}, 1fr)`,
                    gap: `${gap}px`
                  }}
                >
                  {filteredProducts
                    .slice(slideIndex * columns, (slideIndex + 1) * columns)
                    .map((product, index) => (
                      <motion.div
                        key={product.id}
                        variants={cardVariants}
                        whileHover="hover"
                        className="cursor-pointer"
                        onClick={() => onProductClick?.(product)}
                      >
                        <ProductCard 
                          product={product} 
                          height={getRandomHeight(slideIndex * columns + index)}
                        />
                      </motion.div>
                    ))}
                </div>
              ))}
            </motion.div>

            {/* Carousel Controls */}
            <div className="flex justify-center items-center gap-4 mt-6">
              <button
                onClick={prevSlide}
                className="p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors"
                style={{ boxShadow: brandShadows.medium }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <div className="flex gap-2">
                {Array.from({ length: Math.ceil(filteredProducts.length / columns) }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-primary-600' : 'bg-cream-300'
                    }`}
                  />
                ))}
              </div>
              
              <button
                onClick={nextSlide}
                className="p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors"
                style={{ boxShadow: brandShadows.medium }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        ) : (
          // Masonry Mode
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5"
            style={{ 
              columnGap: `${gap}px`,
              columnCount: columns 
            }}
          >
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                variants={cardVariants}
                whileHover="hover"
                className="cursor-pointer break-inside-avoid mb-4"
                onClick={() => onProductClick?.(product)}
              >
                <ProductCard 
                  product={product} 
                  height={getRandomHeight(index)}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-500">Try adjusting your filters or search terms.</p>
        </div>
      )}
    </div>
  );
};

// Product Card Component
interface ProductCardProps {
  product: Product;
  height: number;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, height }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <div 
      className="bg-cream-50 rounded-xl overflow-hidden group"
      style={{ boxShadow: brandShadows.soft }}
    >
      {/* Product Image */}
      <div 
        className="relative bg-gradient-to-br from-primary-100 to-sage-100"
        style={{ height: `${height}px` }}
      >
        {product.image ? (
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2L3 7v11h14V7l-7-5z" />
              </svg>
            </div>
          </div>
        )}

        {/* Category Badge */}
        <div className="absolute top-3 right-3">
          <span 
            className="text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize"
            style={{ backgroundColor: brandColors.primary[600] }}
          >
            {product.category}
          </span>
        </div>

        {/* Hover overlay */}
        <div className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
          {product.name}
        </h3>

        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-bold text-primary-600">
            {formatPrice(product.price)}
          </span>
          
          {product.strain && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {product.strain}
            </span>
          )}
        </div>

        {/* THC/CBD Info */}
        {(product.thc || product.cbd) && (
          <div className="flex gap-2 mb-3">
            {product.thc && (
              <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                THC: {product.thc}%
              </span>
            )}
            {product.cbd && (
              <span className="text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded">
                CBD: {product.cbd}%
              </span>
            )}
          </div>
        )}

        {/* Effects Tags */}
        {product.effects && product.effects.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {product.effects.slice(0, 2).map((effect, idx) => (
              <span 
                key={idx}
                className="text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded"
              >
                {effect}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MasonryProductShowcase;
