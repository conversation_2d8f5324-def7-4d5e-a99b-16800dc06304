/*! For license information please see tsparticles.interaction.external.attract.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],e);else{var i="object"==typeof exports?e(require("@tsparticles/engine")):e(t.window);for(var a in i)("object"==typeof exports?exports:t)[a]=i[a]}}(this,(t=>(()=>{var e={303:e=>{e.exports=t}},i={};function a(t){var n=i[t];if(void 0!==n)return n.exports;var o=i[t]={exports:{}};return e[t](o,o.exports,a),o.exports}a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),a.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};a.r(n),a.d(n,{Attract:()=>d,loadExternalAttractInteraction:()=>u});var o=a(303);const r=1,c=1;function s(t,e,i,a,n,s){const d=e.actualOptions.interactivity.modes.attract;if(!d)return;const l=e.particles.quadTree.query(n,s);for(const e of l){const{dx:n,dy:s,distance:l}=(0,o.getDistances)(e.position,i),p=d.speed*d.factor,u=(0,o.clamp)(t.getEasing(d.easing)(c-l/a)*p,r,d.maxSpeed),f=o.Vector.create(l?n/l*u:p,l?s/l*u:p);e.position.subFrom(f)}}class d{constructor(){this.distance=200,this.duration=.4,this.easing=o.EasingType.easeOutQuad,this.factor=1,this.maxSpeed=50,this.speed=1}load(t){(0,o.isNull)(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed),void 0!==t.speed&&(this.speed=t.speed))}}const l="attract";class p extends o.ExternalInteractorBase{constructor(t,e){super(e),this._engine=t,e.attract||(e.attract={particles:[]}),this.handleClickMode=t=>{const i=this.container.actualOptions.interactivity.modes.attract;if(i&&t===l){e.attract||(e.attract={particles:[]}),e.attract.clicking=!0,e.attract.count=0;for(const t of e.attract.particles)this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);e.attract.particles=[],e.attract.finish=!1,setTimeout((()=>{e.destroyed||(e.attract||(e.attract={particles:[]}),e.attract.clicking=!1)}),i.duration*o.millisecondsToSeconds)}}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.attract;e&&(t.retina.attractModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions,i=t.interactivity.status===o.mouseMoveEvent,a=e.interactivity.events,{enable:n,mode:r}=a.onHover,{enable:c,mode:d}=a.onClick;i&&n&&(0,o.isInArray)(l,r)?function(t,e,i){const a=e.interactivity.mouse.position,n=e.retina.attractModeDistance;!n||n<0||!a||s(t,e,a,n,new o.Circle(a.x,a.y,n),(t=>i(t)))}(this._engine,this.container,(t=>this.isEnabled(t))):c&&(0,o.isInArray)(l,d)&&function(t,e,i){e.attract||(e.attract={particles:[]});const{attract:a}=e;if(a.finish||(a.count||(a.count=0),a.count++,a.count===e.particles.count&&(a.finish=!0)),a.clicking){const a=e.interactivity.mouse.clickPosition,n=e.retina.attractModeDistance;if(!n||n<0||!a)return;s(t,e,a,n,new o.Circle(a.x,a.y,n),(t=>i(t)))}else!1===a.clicking&&(a.particles=[])}(this._engine,this.container,(t=>this.isEnabled(t)))}isEnabled(t){const e=this.container,i=e.actualOptions,a=e.interactivity.mouse,n=(t?.interactivity??i.interactivity).events;if(!(a.position&&n.onHover.enable||a.clickPosition&&n.onClick.enable))return!1;const r=n.onHover.mode,c=n.onClick.mode;return(0,o.isInArray)(l,r)||(0,o.isInArray)(l,c)}loadModeOptions(t,...e){t.attract||(t.attract=new d);for(const i of e)t.attract.load(i?.attract)}reset(){}}async function u(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalAttract",(e=>Promise.resolve(new p(t,e))),e)}return n})()));