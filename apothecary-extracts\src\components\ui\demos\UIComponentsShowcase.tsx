'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import HeroVideoWithCTA from '../hero/HeroVideoWithCTA';
import AnimatedCardSlider from '../cards/AnimatedCardSlider';
import TabbedProductGrid from '../grids/TabbedProductGrid';
import MasonryProductShowcase from '../grids/MasonryProductShowcase';
import GlitchTextEffect from '../effects/GlitchTextEffect';
import { Product } from '../../../types/ui';
import { brandColors } from '../../../styles/brand';

/**
 * UI Components Showcase
 * 
 * Comprehensive demo page showcasing all Apothecary Farms UI components
 */
const UIComponentsShowcase: React.FC = () => {
  // Sample data for demos
  const sampleProducts: Product[] = [
    {
      id: '1',
      name: 'Blue Dream',
      description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',
      image: '/api/placeholder/300/400',
      price: 45,
      category: 'flower',
      strain: 'Hybrid',
      thc: 18,
      cbd: 1,
      effects: ['Relaxed', 'Happy', 'Creative'],
      tags: ['Popular', 'Daytime']
    },
    {
      id: '2',
      name: 'Live Rosin',
      description: 'Premium solventless concentrate with full terpene profile.',
      image: '/api/placeholder/300/300',
      price: 80,
      category: 'extract',
      thc: 75,
      cbd: 2,
      effects: ['Euphoric', 'Focused', 'Uplifted'],
      tags: ['Premium', 'Solventless']
    },
    {
      id: '3',
      name: 'Gummy Bears',
      description: 'Delicious fruit-flavored gummies with precise dosing.',
      image: '/api/placeholder/300/250',
      price: 25,
      category: 'edible',
      thc: 10,
      effects: ['Relaxed', 'Sleepy', 'Pain Relief'],
      tags: ['Beginner Friendly', 'Tasty']
    },
    {
      id: '4',
      name: 'OG Kush',
      description: 'Classic indica-dominant strain with earthy pine flavors.',
      image: '/api/placeholder/300/350',
      price: 50,
      category: 'flower',
      strain: 'Indica',
      thc: 22,
      cbd: 0.5,
      effects: ['Relaxed', 'Sleepy', 'Hungry'],
      tags: ['Classic', 'Evening']
    },
    {
      id: '5',
      name: 'Shatter',
      description: 'Glass-like concentrate with high potency and purity.',
      image: '/api/placeholder/300/280',
      price: 60,
      category: 'extract',
      thc: 85,
      effects: ['Intense', 'Long-lasting', 'Cerebral'],
      tags: ['High Potency', 'Experienced']
    },
    {
      id: '6',
      name: 'Chocolate Bar',
      description: 'Rich dark chocolate infused with premium cannabis.',
      image: '/api/placeholder/300/200',
      price: 35,
      category: 'edible',
      thc: 100,
      effects: ['Euphoric', 'Relaxed', 'Creative'],
      tags: ['Luxury', 'Microdose']
    }
  ];

  const categories = [
    {
      id: 'flower',
      name: 'Flower',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z" />
        </svg>
      )
    },
    {
      id: 'extract',
      name: 'Extracts',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M6.5 3c-1.051 0-2.093.04-3.125.117a1 1 0 00-.831.814A6.5 6.5 0 004.5 12H13l2.856-2.856A3.5 3.5 0 0015.5 7a3.5 3.5 0 00-3.5-3.5H6.5z" />
        </svg>
      )
    },
    {
      id: 'edible',
      name: 'Edibles',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2a8 8 0 100 16 8 8 0 000-16zM8 12a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z" />
        </svg>
      )
    }
  ];

  const filters = [
    { id: 'category', name: 'Flower', value: 'flower' },
    { id: 'category', name: 'Extracts', value: 'extract' },
    { id: 'category', name: 'Edibles', value: 'edible' },
    { id: 'price', name: 'Under $25', value: 'under-25' },
    { id: 'price', name: '$25-$50', value: '25-50' },
    { id: 'price', name: '$50-$100', value: '50-100' },
    { id: 'effects', name: 'Relaxed', value: 'Relaxed' },
    { id: 'effects', name: 'Happy', value: 'Happy' },
    { id: 'effects', name: 'Creative', value: 'Creative' }
  ];

  return (
    <div className="ui-components-showcase">
      {/* Header */}
      <div className="bg-charcoal-800 text-cream-50 py-16">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <GlitchTextEffect
            text="APOTHECARY FARMS"
            intensity="medium"
            trigger="continuous"
            fontSize="4rem"
            className="mb-4"
          />
          <h2 className="text-2xl font-serif mb-4">UI Components Library</h2>
          <p className="text-xl text-cream-200 max-w-3xl mx-auto">
            Production-ready React components for cannabis industry applications
            featuring modern animations, responsive design, and brand consistency.
          </p>
        </div>
      </div>

      {/* Hero Video Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6 mb-8">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Hero Video with CTA
          </h3>
          <p className="text-charcoal-600 mb-6">
            Immersive video backgrounds with call-to-action overlays and cannabis industry branding.
          </p>
        </div>
        
        <div className="h-96 relative">
          <HeroVideoWithCTA
            videoSrc="/api/placeholder/video"
            posterSrc="/api/placeholder/1920/1080"
            headline="Premium Cannabis Extracts"
            subtitle="Award-winning quality, lab-tested purity"
            primaryCTA="Shop Now"
            onPrimaryCTA={() => console.log('Primary CTA clicked')}
            secondaryCTA="Learn More"
            onSecondaryCTA={() => console.log('Secondary CTA clicked')}
            overlayOpacity={0.5}
          />
        </div>
      </section>

      {/* Card Slider Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Animated Card Slider
          </h3>
          <p className="text-charcoal-600 mb-8">
            Smooth product carousels with auto-advance, touch support, and responsive design.
          </p>
          
          <AnimatedCardSlider
            products={sampleProducts}
            visibleCards={3}
            autoAdvance={5000}
            touchEnabled={true}
            showDots={true}
            showArrows={true}
            onCardClick={(product) => console.log('Card clicked:', product.name)}
          />
        </div>
      </section>

      {/* Tabbed Product Grid Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Tabbed Product Grid
          </h3>
          <p className="text-charcoal-600 mb-8">
            Organized product displays with category tabs, pagination, and smooth animations.
          </p>
          
          <TabbedProductGrid
            products={sampleProducts}
            categories={categories}
            columns={3}
            itemsPerPage={6}
            showPagination={true}
            onProductClick={(product) => console.log('Product clicked:', product.name)}
          />
        </div>
      </section>

      {/* Masonry Showcase Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Masonry Product Showcase
          </h3>
          <p className="text-charcoal-600 mb-8">
            Dynamic masonry layouts with filtering capabilities and responsive design.
          </p>
          
          <MasonryProductShowcase
            products={sampleProducts}
            columns={3}
            gap={20}
            carouselMode={false}
            filters={filters}
            onProductClick={(product) => console.log('Product clicked:', product.name)}
          />
        </div>
      </section>

      {/* Glitch Text Effects Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Glitch Text Effects
          </h3>
          <p className="text-charcoal-600 mb-8">
            Modern, edgy text effects with customizable intensity and trigger options.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">Hover Trigger</h4>
              <GlitchTextEffect
                text="HOVER ME"
                intensity="medium"
                trigger="hover"
                fontSize="1.5rem"
                colors={[brandColors.apothecary, '#ff0080', '#00ff80']}
              />
            </div>
            
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">Continuous</h4>
              <GlitchTextEffect
                text="ALWAYS ON"
                intensity="low"
                trigger="continuous"
                fontSize="1.5rem"
                colors={['#ffd700', '#ff4500', '#00ffff']}
              />
            </div>
            
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">High Intensity</h4>
              <GlitchTextEffect
                text="INTENSE"
                intensity="high"
                trigger="hover"
                fontSize="1.5rem"
                colors={['#ff0000', '#00ff00', '#0000ff']}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Component Features */}
      <section className="bg-cream-100 py-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-8 text-center">
            Component Features
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Cannabis Industry Focused
              </h4>
              <p className="text-charcoal-600">
                Built specifically for cannabis businesses with industry-appropriate styling and terminology.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-sage-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Responsive Design
              </h4>
              <p className="text-charcoal-600">
                Mobile-first approach with seamless adaptation across all device sizes and orientations.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-gold-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Premium Animations
              </h4>
              <p className="text-charcoal-600">
                Smooth Framer Motion animations with performance optimization and accessibility support.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-charcoal-800 text-cream-50 py-8">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <p className="text-cream-200">
            Built with ❤️ for the cannabis industry using React, Framer Motion, and TailwindCSS
          </p>
        </div>
      </footer>
    </div>
  );
};

export default UIComponentsShowcase;
