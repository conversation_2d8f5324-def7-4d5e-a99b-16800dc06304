'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import HeroVideoWithCTA from '../hero/HeroVideoWithCTA';
import AnimatedCardSlider from '../cards/AnimatedCardSlider';
import TabbedProductGrid from '../grids/TabbedProductGrid';
import MasonryProductShowcase from '../grids/MasonryProductShowcase';
import GlitchTextEffect from '../effects/GlitchTextEffect';
import FilterableStrainCards from '../cards/FilterableStrainCards';
import ParallaxSection from '../layout/ParallaxSection';
import VerticalTimeline from '../layout/VerticalTimeline';
import SplitMediaLayout from '../layout/SplitMediaLayout';
import FeatureIconsWithMotion from '../effects/FeatureIconsWithMotion';
import FAQAccordion from '../navigation/FAQAccordion';
import AnnouncementBanner from '../navigation/AnnouncementBanner';
import { Product, Strain, TimelineItem, FeatureItem, FAQItem } from '../../../types/ui';
import { brandColors } from '../../../styles/brand';

/**
 * UI Components Showcase
 * 
 * Comprehensive demo page showcasing all Apothecary Farms UI components
 */
const UIComponentsShowcase: React.FC = () => {
  // Sample data for demos
  const sampleProducts: Product[] = [
    {
      id: '1',
      name: 'Blue Dream',
      description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',
      image: '/api/placeholder/300/400',
      price: 45,
      category: 'flower',
      strain: 'Hybrid',
      thc: 18,
      cbd: 1,
      effects: ['Relaxed', 'Happy', 'Creative'],
      tags: ['Popular', 'Daytime']
    },
    {
      id: '2',
      name: 'Live Rosin',
      description: 'Premium solventless concentrate with full terpene profile.',
      image: '/api/placeholder/300/300',
      price: 80,
      category: 'extract',
      thc: 75,
      cbd: 2,
      effects: ['Euphoric', 'Focused', 'Uplifted'],
      tags: ['Premium', 'Solventless']
    },
    {
      id: '3',
      name: 'Gummy Bears',
      description: 'Delicious fruit-flavored gummies with precise dosing.',
      image: '/api/placeholder/300/250',
      price: 25,
      category: 'edible',
      thc: 10,
      effects: ['Relaxed', 'Sleepy', 'Pain Relief'],
      tags: ['Beginner Friendly', 'Tasty']
    },
    {
      id: '4',
      name: 'OG Kush',
      description: 'Classic indica-dominant strain with earthy pine flavors.',
      image: '/api/placeholder/300/350',
      price: 50,
      category: 'flower',
      strain: 'Indica',
      thc: 22,
      cbd: 0.5,
      effects: ['Relaxed', 'Sleepy', 'Hungry'],
      tags: ['Classic', 'Evening']
    },
    {
      id: '5',
      name: 'Shatter',
      description: 'Glass-like concentrate with high potency and purity.',
      image: '/api/placeholder/300/280',
      price: 60,
      category: 'extract',
      thc: 85,
      effects: ['Intense', 'Long-lasting', 'Cerebral'],
      tags: ['High Potency', 'Experienced']
    },
    {
      id: '6',
      name: 'Chocolate Bar',
      description: 'Rich dark chocolate infused with premium cannabis.',
      image: '/api/placeholder/300/200',
      price: 35,
      category: 'edible',
      thc: 100,
      effects: ['Euphoric', 'Relaxed', 'Creative'],
      tags: ['Luxury', 'Microdose']
    }
  ];

  const categories = [
    {
      id: 'flower',
      name: 'Flower',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z" />
        </svg>
      )
    },
    {
      id: 'extract',
      name: 'Extracts',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M6.5 3c-1.051 0-2.093.04-3.125.117a1 1 0 00-.831.814A6.5 6.5 0 004.5 12H13l2.856-2.856A3.5 3.5 0 0015.5 7a3.5 3.5 0 00-3.5-3.5H6.5z" />
        </svg>
      )
    },
    {
      id: 'edible',
      name: 'Edibles',
      icon: (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2a8 8 0 100 16 8 8 0 000-16zM8 12a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z" />
        </svg>
      )
    }
  ];

  const filters = [
    { id: 'category', name: 'Flower', value: 'flower' },
    { id: 'category', name: 'Extracts', value: 'extract' },
    { id: 'category', name: 'Edibles', value: 'edible' },
    { id: 'price', name: 'Under $25', value: 'under-25' },
    { id: 'price', name: '$25-$50', value: '25-50' },
    { id: 'price', name: '$50-$100', value: '50-100' },
    { id: 'effects', name: 'Relaxed', value: 'Relaxed' },
    { id: 'effects', name: 'Happy', value: 'Happy' },
    { id: 'effects', name: 'Creative', value: 'Creative' }
  ];

  // Sample strains data
  const sampleStrains: Strain[] = [
    {
      id: '1',
      name: 'Blue Dream',
      type: 'hybrid',
      description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',
      image: '/api/placeholder/300/400',
      thc: 18,
      cbd: 1,
      effects: ['Relaxed', 'Happy', 'Creative'],
      flavors: ['Berry', 'Sweet', 'Earthy'],
      genetics: 'Blueberry x Haze'
    },
    {
      id: '2',
      name: 'OG Kush',
      type: 'indica',
      description: 'Classic indica-dominant strain with earthy pine flavors.',
      image: '/api/placeholder/300/350',
      thc: 22,
      cbd: 0.5,
      effects: ['Relaxed', 'Sleepy', 'Hungry'],
      flavors: ['Pine', 'Earthy', 'Woody'],
      genetics: 'Chemdawg x Lemon Thai'
    },
    {
      id: '3',
      name: 'Sour Diesel',
      type: 'sativa',
      description: 'Energizing sativa with diesel-like aroma and uplifting effects.',
      image: '/api/placeholder/300/380',
      thc: 20,
      cbd: 1,
      effects: ['Energetic', 'Creative', 'Uplifted'],
      flavors: ['Diesel', 'Citrus', 'Pungent'],
      genetics: 'Chemdawg 91 x Super Skunk'
    }
  ];

  // Sample timeline data
  const timelineItems: TimelineItem[] = [
    {
      id: '1',
      title: 'Seed Selection',
      description: 'We carefully select premium genetics from trusted breeders worldwide.',
      date: 'Week 1',
      image: '/api/placeholder/400/300'
    },
    {
      id: '2',
      title: 'Germination',
      description: 'Seeds are germinated in controlled environment with optimal humidity and temperature.',
      date: 'Week 2',
      image: '/api/placeholder/400/300'
    },
    {
      id: '3',
      title: 'Vegetative Growth',
      description: 'Plants develop strong root systems and healthy foliage under specialized lighting.',
      date: 'Week 3-8',
      image: '/api/placeholder/400/300'
    },
    {
      id: '4',
      title: 'Flowering',
      description: 'Controlled light cycles trigger flowering, developing potent buds rich in cannabinoids.',
      date: 'Week 9-16',
      image: '/api/placeholder/400/300'
    },
    {
      id: '5',
      title: 'Harvest & Cure',
      description: 'Careful harvesting and curing process preserves terpenes and maximizes potency.',
      date: 'Week 17-20',
      image: '/api/placeholder/400/300'
    }
  ];

  // Sample features data
  const features: FeatureItem[] = [
    {
      id: '1',
      title: 'Lab Tested',
      description: 'All products undergo rigorous third-party testing for potency and purity.',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
          <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6.5A1.5 1.5 0 009.5 13h-3A1.5 1.5 0 005 11.5V5zM7 7a1 1 0 011-1h.01a1 1 0 110 2H8a1 1 0 01-1-1zm1 3a1 1 0 100 2h.01a1 1 0 100-2H8z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '2',
      title: 'Organic Growing',
      description: 'Cultivated using sustainable, organic practices without harmful pesticides.',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: '3',
      title: 'Expert Cultivation',
      description: 'Grown by master cultivators with decades of cannabis growing experience.',
      icon: (
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  // Sample FAQ data
  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: 'What is the difference between indica and sativa?',
      answer: 'Indica strains typically provide relaxing, sedating effects and are often used for evening consumption. Sativa strains tend to be more energizing and uplifting, making them popular for daytime use. Hybrid strains combine characteristics of both.',
      category: 'strains'
    },
    {
      id: '2',
      question: 'How should I store my cannabis products?',
      answer: 'Store cannabis in a cool, dark, dry place away from direct sunlight. Use airtight containers to preserve freshness and potency. Avoid storing in the refrigerator or freezer as this can damage trichomes.',
      category: 'storage'
    },
    {
      id: '3',
      question: 'What does lab testing include?',
      answer: 'Our comprehensive lab testing includes potency analysis (THC, CBD, other cannabinoids), terpene profiles, pesticide screening, heavy metals testing, and microbial analysis to ensure product safety and quality.',
      category: 'testing'
    }
  ];

  return (
    <div className="ui-components-showcase">
      {/* Header */}
      <div className="bg-charcoal-800 text-cream-50 py-16">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <GlitchTextEffect
            text="APOTHECARY FARMS"
            intensity="medium"
            trigger="continuous"
            fontSize="4rem"
            className="mb-4"
          />
          <h2 className="text-2xl font-serif mb-4">UI Components Library</h2>
          <p className="text-xl text-cream-200 max-w-3xl mx-auto">
            Production-ready React components for cannabis industry applications
            featuring modern animations, responsive design, and brand consistency.
          </p>
        </div>
      </div>

      {/* Hero Video Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6 mb-8">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Hero Video with CTA
          </h3>
          <p className="text-charcoal-600 mb-6">
            Immersive video backgrounds with call-to-action overlays and cannabis industry branding.
          </p>
        </div>
        
        <div className="h-96 relative">
          <HeroVideoWithCTA
            videoSrc="/api/placeholder/video"
            posterSrc="/api/placeholder/1920/1080"
            headline="Premium Cannabis Extracts"
            subtitle="Award-winning quality, lab-tested purity"
            primaryCTA="Shop Now"
            onPrimaryCTA={() => console.log('Primary CTA clicked')}
            secondaryCTA="Learn More"
            onSecondaryCTA={() => console.log('Secondary CTA clicked')}
            overlayOpacity={0.5}
          />
        </div>
      </section>

      {/* Card Slider Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Animated Card Slider
          </h3>
          <p className="text-charcoal-600 mb-8">
            Smooth product carousels with auto-advance, touch support, and responsive design.
          </p>
          
          <AnimatedCardSlider
            products={sampleProducts}
            visibleCards={3}
            autoAdvance={5000}
            touchEnabled={true}
            showDots={true}
            showArrows={true}
            onCardClick={(product) => console.log('Card clicked:', product.name)}
          />
        </div>
      </section>

      {/* Tabbed Product Grid Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Tabbed Product Grid
          </h3>
          <p className="text-charcoal-600 mb-8">
            Organized product displays with category tabs, pagination, and smooth animations.
          </p>
          
          <TabbedProductGrid
            products={sampleProducts}
            categories={categories}
            columns={3}
            itemsPerPage={6}
            showPagination={true}
            onProductClick={(product) => console.log('Product clicked:', product.name)}
          />
        </div>
      </section>

      {/* Masonry Showcase Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Masonry Product Showcase
          </h3>
          <p className="text-charcoal-600 mb-8">
            Dynamic masonry layouts with filtering capabilities and responsive design.
          </p>
          
          <MasonryProductShowcase
            products={sampleProducts}
            columns={3}
            gap={20}
            carouselMode={false}
            filters={filters}
            onProductClick={(product) => console.log('Product clicked:', product.name)}
          />
        </div>
      </section>

      {/* Glitch Text Effects Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Glitch Text Effects
          </h3>
          <p className="text-charcoal-600 mb-8">
            Modern, edgy text effects with customizable intensity and trigger options.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">Hover Trigger</h4>
              <GlitchTextEffect
                text="HOVER ME"
                intensity="medium"
                trigger="hover"
                fontSize="1.5rem"
                colors={[brandColors.apothecary, '#ff0080', '#00ff80']}
              />
            </div>
            
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">Continuous</h4>
              <GlitchTextEffect
                text="ALWAYS ON"
                intensity="low"
                trigger="continuous"
                fontSize="1.5rem"
                colors={['#ffd700', '#ff4500', '#00ffff']}
              />
            </div>
            
            <div className="text-center p-6 bg-charcoal-800 rounded-lg">
              <h4 className="text-cream-200 mb-4">High Intensity</h4>
              <GlitchTextEffect
                text="INTENSE"
                intensity="high"
                trigger="hover"
                fontSize="1.5rem"
                colors={['#ff0000', '#00ff00', '#0000ff']}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Filterable Strain Cards Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Filterable Strain Cards
          </h3>
          <p className="text-charcoal-600 mb-8">
            Advanced strain filtering with search, category filters, and sorting options.
          </p>

          <FilterableStrainCards
            strains={sampleStrains}
            enableSearch={true}
            filters={[
              { id: 'type', name: 'Type', options: ['sativa', 'indica', 'hybrid'] },
              { id: 'thc', name: 'THC Level', options: ['low', 'medium', 'high'] }
            ]}
            sortOptions={[
              { id: 'name', name: 'Name', field: 'name' },
              { id: 'thc', name: 'THC %', field: 'thc' }
            ]}
            cardsPerRow={3}
            onStrainClick={(strain) => console.log('Strain clicked:', strain.name)}
          />
        </div>
      </section>

      {/* Vertical Timeline Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Vertical Timeline
          </h3>
          <p className="text-charcoal-600 mb-8">
            Showcase cultivation process, company history, or product journey with animated timeline.
          </p>

          <VerticalTimeline
            items={timelineItems}
            theme="cultivation"
            alternating={true}
            showLine={true}
            animateOnScroll={true}
          />
        </div>
      </section>

      {/* Feature Icons Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Feature Icons with Motion
          </h3>
          <p className="text-charcoal-600 mb-8">
            Highlight key features with animated icons and smooth scroll triggers.
          </p>

          <FeatureIconsWithMotion
            features={features}
            layout="grid"
            animationTrigger="scroll"
            columns={3}
            iconSize="lg"
          />
        </div>
      </section>

      {/* FAQ Accordion Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            FAQ Accordion
          </h3>
          <p className="text-charcoal-600 mb-8">
            Interactive FAQ section with search, categories, and smooth animations.
          </p>

          <FAQAccordion
            items={faqItems}
            allowMultiple={false}
            showCategories={true}
            enableSearch={true}
          />
        </div>
      </section>

      {/* Announcement Banner Demo */}
      <section className="mb-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-4">
            Announcement Banner
          </h3>
          <p className="text-charcoal-600 mb-8">
            Eye-catching banners for deals, announcements, and important notices.
          </p>

          <div className="space-y-4">
            <AnnouncementBanner
              message="🌿 New Premium Rosin Collection Available - 20% Off This Week!"
              type="deal"
              dismissible={true}
              actionText="Shop Now"
              onAction={() => console.log('Shop now clicked')}
            />

            <AnnouncementBanner
              message="Lab results now available for all products. View certificates online."
              type="info"
              dismissible={true}
              actionText="View Results"
              onAction={() => console.log('View results clicked')}
            />
          </div>
        </div>
      </section>

      {/* Split Media Layout Demo */}
      <section className="mb-16">
        <SplitMediaLayout
          media={{
            type: 'image',
            src: '/api/placeholder/600/400',
            alt: 'Cannabis extraction process'
          }}
          content={{
            title: 'Our Extraction Process',
            description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes, ensuring the highest quality concentrates.',
            features: ['CO2 Extraction', 'Full Spectrum', 'Lab Tested', 'Solvent-Free'],
            cta: {
              text: 'Learn More',
              action: () => console.log('Learn more clicked')
            }
          }}
          mediaPosition="left"
          splitRatio="50-50"
          verticalAlign="center"
        />
      </section>

      {/* Component Features */}
      <section className="bg-cream-100 py-16">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-3xl font-serif font-bold text-charcoal-800 mb-8 text-center">
            Component Features
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Cannabis Industry Focused
              </h4>
              <p className="text-charcoal-600">
                Built specifically for cannabis businesses with industry-appropriate styling and terminology.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-sage-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Responsive Design
              </h4>
              <p className="text-charcoal-600">
                Mobile-first approach with seamless adaptation across all device sizes and orientations.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              className="bg-white p-6 rounded-lg shadow-soft"
            >
              <div className="w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-gold-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                Premium Animations
              </h4>
              <p className="text-charcoal-600">
                Smooth Framer Motion animations with performance optimization and accessibility support.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-charcoal-800 text-cream-50 py-8">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <p className="text-cream-200">
            Built with ❤️ for the cannabis industry using React, Framer Motion, and TailwindCSS
          </p>
        </div>
      </footer>
    </div>
  );
};

export default UIComponentsShowcase;
