{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/styles/brand.ts"], "sourcesContent": ["/**\n * Apothecary Farms Brand Utility System\n * Centralized brand tokens for colors, gradients, shadows, and animations\n */\n\n// Brand Colors\nexport const brandColors = {\n  // Primary Green Palette\n  primary: {\n    50: '#f0f9f4',\n    100: '#dcf2e4',\n    200: '#bce5cd',\n    300: '#8dd1a8',\n    400: '#57b67c',\n    500: '#359a5a', // Main brand green\n    600: '#267d47',\n    700: '#1f633a',\n    800: '#1b4332',\n    900: '#163a2b',\n  },\n  \n  // Apothecary Green (from memory - the mint green mentioned)\n  apothecary: '#2FB886',\n  \n  // Cream/Neutral Palette\n  cream: {\n    50: '#fefefe',\n    100: '#f8f6f0',\n    200: '#f4f1e8',\n    300: '#ede8db',\n    400: '#e4dcc8',\n    500: '#d8cdb0',\n  },\n  \n  // Gold/Amber Palette (for extracts/rosin)\n  gold: {\n    50: '#fefcf7',\n    100: '#fdf8ed',\n    200: '#f9eed5',\n    300: '#f4e4bc',\n    400: '#edd5a3',\n    500: '#d4a574',\n  },\n  \n  // Charcoal/<PERSON> Palette\n  charcoal: {\n    50: '#f8f9fa',\n    100: '#e9ecef',\n    200: '#dee2e6',\n    300: '#ced4da',\n    400: '#adb5bd',\n    500: '#6c757d',\n    600: '#495057',\n    700: '#343a40',\n    800: '#2d3436',\n    900: '#212529',\n  },\n  \n  // Sage Green Palette\n  sage: {\n    50: '#f7f9f8',\n    100: '#eef2f0',\n    200: '#dde5e1',\n    300: '#c4d2ca',\n    400: '#a5b8ad',\n    500: '#95a99c',\n  },\n} as const;\n\n// Terpene-Inspired Color Palettes\nexport const terpeneColors = {\n  // Pineapple Express - Orange to Yellow\n  pineapple: {\n    from: '#ff8c00',\n    to: '#ffd700',\n    gradient: 'linear-gradient(135deg, #ff8c00 0%, #ffd700 100%)',\n  },\n  \n  // GMO - Blue Frost\n  gmo: {\n    from: '#4a90e2',\n    to: '#87ceeb',\n    gradient: 'linear-gradient(135deg, #4a90e2 0%, #87ceeb 100%)',\n  },\n  \n  // Rosin - Gold to Amber\n  rosin: {\n    from: '#d4a574',\n    to: '#ff8c00',\n    gradient: 'linear-gradient(135deg, #d4a574 0%, #ff8c00 100%)',\n  },\n  \n  // Flower - Green to Yellow\n  flower: {\n    from: '#359a5a',\n    to: '#ffd700',\n    gradient: 'linear-gradient(135deg, #359a5a 0%, #ffd700 100%)',\n  },\n  \n  // Extract - Amber to Purple\n  extract: {\n    from: '#ff8c00',\n    to: '#9370db',\n    gradient: 'linear-gradient(135deg, #ff8c00 0%, #9370db 100%)',\n  },\n  \n  // Particle Palette - Soft colors for backgrounds\n  particles: {\n    yellow: '#fff9c4',\n    lime: '#d4edda',\n    lavender: '#e2d5f1',\n  },\n} as const;\n\n// Brand Gradients\nexport const brandGradients = {\n  // Main brand gradients\n  cannabis: 'linear-gradient(135deg, #1b4332 0%, #267d47 50%, #95a99c 100%)',\n  gold: 'linear-gradient(135deg, #d4a574 0%, #edd5a3 100%)',\n  sage: 'linear-gradient(135deg, #95a99c 0%, #c4d2ca 100%)',\n  \n  // Context-based gradients\n  hero: 'linear-gradient(135deg, #1b4332 0%, #163a2b 100%)',\n  card: 'linear-gradient(135deg, #f8f6f0 0%, #ede8db 100%)',\n  \n  // Animated gradients for text\n  dynamicText: {\n    pineapple: 'linear-gradient(45deg, #ff8c00, #ffd700, #ff8c00)',\n    gmo: 'linear-gradient(45deg, #4a90e2, #87ceeb, #4a90e2)',\n    rosin: 'linear-gradient(45deg, #d4a574, #ff8c00, #d4a574)',\n  },\n} as const;\n\n// Brand Shadows\nexport const brandShadows = {\n  // Soft shadows\n  soft: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n  medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n  large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n  \n  // Brand-specific glows\n  apothecaryGlow: '0 0 20px rgba(47, 184, 134, 0.3)',\n  goldGlow: '0 0 20px rgba(212, 165, 116, 0.3)',\n  \n  // Dynamic shadows for tilt effects\n  tiltShadow: (direction: 'left' | 'right' | 'up' | 'down') => {\n    const shadows = {\n      left: '-5px 5px 15px rgba(0, 0, 0, 0.2)',\n      right: '5px 5px 15px rgba(0, 0, 0, 0.2)',\n      up: '0 -5px 15px rgba(0, 0, 0, 0.2)',\n      down: '0 5px 15px rgba(0, 0, 0, 0.2)',\n    };\n    return shadows[direction];\n  },\n} as const;\n\n// Animation Presets\nexport const brandAnimations = {\n  // Easing functions (Framer Motion format)\n  easing: {\n    smooth: [0.4, 0, 0.2, 1],\n    bounce: [0.68, -0.55, 0.265, 1.55],\n    elastic: [0.175, 0.885, 0.32, 1.275],\n  },\n\n  // CSS easing functions (for CSS transitions)\n  cssEasing: {\n    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n    elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n  },\n  \n  // Duration presets\n  duration: {\n    fast: 200,\n    normal: 300,\n    slow: 500,\n    verySlow: 800,\n  },\n  \n  // Common animation variants for Framer Motion\n  fadeInUp: {\n    initial: { opacity: 0, y: 40 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.5, ease: 'easeOut' },\n  },\n  \n  scaleOnHover: {\n    whileHover: { scale: 1.07, brightness: 1.1 },\n    transition: { duration: 0.2 },\n  },\n  \n  tiltEffect: {\n    perspective: 1000,\n    rotateRange: 10,\n  },\n  \n  typewriter: {\n    charDelay: 50,\n    cursorBlink: 1000,\n  },\n} as const;\n\n// Typography Scale\nexport const brandTypography = {\n  fonts: {\n    sans: 'var(--font-inter), Inter, system-ui, sans-serif',\n    serif: 'var(--font-playfair), Playfair Display, Georgia, serif',\n  },\n  \n  sizes: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem',\n    '4xl': '2.25rem',\n    '5xl': '3rem',\n    '6xl': '3.75rem',\n  },\n  \n  weights: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700,\n  },\n} as const;\n\n// Spacing Scale\nexport const brandSpacing = {\n  xs: '0.25rem',\n  sm: '0.5rem',\n  md: '1rem',\n  lg: '1.5rem',\n  xl: '2rem',\n  '2xl': '3rem',\n  '3xl': '4rem',\n  '4xl': '6rem',\n  '5xl': '8rem',\n} as const;\n\n// Utility Functions\nexport const brandUtils = {\n  // Get color with opacity\n  withOpacity: (color: string, opacity: number) => {\n    if (color.startsWith('#')) {\n      const hex = color.slice(1);\n      const r = parseInt(hex.slice(0, 2), 16);\n      const g = parseInt(hex.slice(2, 4), 16);\n      const b = parseInt(hex.slice(4, 6), 16);\n      return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n    }\n    return color;\n  },\n  \n  // Generate random terpene gradient\n  randomTerpeneGradient: () => {\n    const terpenes = Object.keys(terpeneColors);\n    const randomTerpene = terpenes[Math.floor(Math.random() * terpenes.length)];\n    return terpeneColors[randomTerpene as keyof typeof terpeneColors].gradient;\n  },\n  \n  // Get appropriate text color for background\n  getTextColor: (backgroundColor: string) => {\n    // Simple light/dark detection - in production, use a proper contrast calculation\n    const darkColors = ['#1b4332', '#163a2b', '#267d47', '#2d3436', '#212529'];\n    return darkColors.includes(backgroundColor) ? brandColors.cream[50] : brandColors.charcoal[800];\n  },\n} as const;\n\nexport default {\n  colors: brandColors,\n  terpenes: terpeneColors,\n  gradients: brandGradients,\n  shadows: brandShadows,\n  animations: brandAnimations,\n  typography: brandTypography,\n  spacing: brandSpacing,\n  utils: brandUtils,\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,eAAe;;;;;;;;;;;;AACR,MAAM,cAAc;IACzB,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,4DAA4D;IAC5D,YAAY;IAEZ,wBAAwB;IACxB,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,0CAA0C;IAC1C,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,wBAAwB;IACxB,UAAU;QACR,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,qBAAqB;IACrB,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,gBAAgB;IAC3B,uCAAuC;IACvC,WAAW;QACT,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,mBAAmB;IACnB,KAAK;QACH,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,wBAAwB;IACxB,OAAO;QACL,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,2BAA2B;IAC3B,QAAQ;QACN,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,4BAA4B;IAC5B,SAAS;QACP,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,iDAAiD;IACjD,WAAW;QACT,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,MAAM,iBAAiB;IAC5B,uBAAuB;IACvB,UAAU;IACV,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,MAAM;IACN,MAAM;IAEN,8BAA8B;IAC9B,aAAa;QACX,WAAW;QACX,KAAK;QACL,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,eAAe;IACf,MAAM;IACN,QAAQ;IACR,OAAO;IAEP,uBAAuB;IACvB,gBAAgB;IAChB,UAAU;IAEV,mCAAmC;IACnC,YAAY,CAAC;QACX,MAAM,UAAU;YACd,MAAM;YACN,OAAO;YACP,IAAI;YACJ,MAAM;QACR;QACA,OAAO,OAAO,CAAC,UAAU;IAC3B;AACF;AAGO,MAAM,kBAAkB;IAC7B,0CAA0C;IAC1C,QAAQ;QACN,QAAQ;YAAC;YAAK;YAAG;YAAK;SAAE;QACxB,QAAQ;YAAC;YAAM,CAAC;YAAM;YAAO;SAAK;QAClC,SAAS;YAAC;YAAO;YAAO;YAAM;SAAM;IACtC;IAEA,6CAA6C;IAC7C,WAAW;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,mBAAmB;IACnB,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IAEA,8CAA8C;IAC9C,UAAU;QACR,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;IAEA,cAAc;QACZ,YAAY;YAAE,OAAO;YAAM,YAAY;QAAI;QAC3C,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,YAAY;QACV,aAAa;QACb,aAAa;IACf;IAEA,YAAY;QACV,WAAW;QACX,aAAa;IACf;AACF;AAGO,MAAM,kBAAkB;IAC7B,OAAO;QACL,MAAM;QACN,OAAO;IACT;IAEA,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,SAAS;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAGO,MAAM,aAAa;IACxB,yBAAyB;IACzB,aAAa,CAAC,OAAe;QAC3B,IAAI,MAAM,UAAU,CAAC,MAAM;YACzB,MAAM,MAAM,MAAM,KAAK,CAAC;YACxB,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC7C;QACA,OAAO;IACT;IAEA,mCAAmC;IACnC,uBAAuB;QACrB,MAAM,WAAW,OAAO,IAAI,CAAC;QAC7B,MAAM,gBAAgB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QAC3E,OAAO,aAAa,CAAC,cAA4C,CAAC,QAAQ;IAC5E;IAEA,4CAA4C;IAC5C,cAAc,CAAC;QACb,iFAAiF;QACjF,MAAM,aAAa;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;QAC1E,OAAO,WAAW,QAAQ,CAAC,mBAAmB,YAAY,KAAK,CAAC,GAAG,GAAG,YAAY,QAAQ,CAAC,IAAI;IACjG;AACF;uCAEe;IACb,QAAQ;IACR,UAAU;IACV,WAAW;IACX,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/hero/HeroVideoWithCTA.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { HeroVideoWithCTAProps } from '../../../types/ui';\nimport { brandColors, brandAnimations } from '../../../styles/brand';\n\n/**\n * HeroVideoWithCTA Component\n * \n * A hero video component with call-to-action overlays and cannabis industry branding.\n * Features auto-playing video backgrounds with customizable overlays and CTAs.\n * \n * @example\n * ```tsx\n * <HeroVideoWithCTA\n *   videoSrc=\"/videos/hero-cannabis.mp4\"\n *   headline=\"Premium Cannabis Extracts\"\n *   subtitle=\"Award-winning quality, lab-tested purity\"\n *   primaryCTA=\"Shop Now\"\n *   onPrimaryCTA={() => router.push('/products')}\n *   secondaryCTA=\"Learn More\"\n *   onSecondaryCTA={() => router.push('/about')}\n * />\n * ```\n */\nconst HeroVideoWithCTA: React.FC<HeroVideoWithCTAProps> = ({\n  videoSrc,\n  posterSrc,\n  headline,\n  subtitle,\n  primaryCTA,\n  onPrimaryCTA,\n  secondaryCTA,\n  onSecondaryCTA,\n  autoplay = true,\n  loop = true,\n  muted = true,\n  overlayOpacity = 0.4,\n  className = '',\n  style,\n}) => {\n  const [isVideoLoaded, setIsVideoLoaded] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    const video = videoRef.current;\n    if (video && autoplay) {\n      video.play().catch(() => {\n        // Auto-play failed, user interaction required\n        setIsPlaying(false);\n      });\n    }\n  }, [autoplay]);\n\n  const handleVideoLoad = () => {\n    setIsVideoLoaded(true);\n  };\n\n  const handlePlayPause = () => {\n    const video = videoRef.current;\n    if (video) {\n      if (video.paused) {\n        video.play();\n        setIsPlaying(true);\n      } else {\n        video.pause();\n        setIsPlaying(false);\n      }\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: brandAnimations.easing.smooth\n      }\n    }\n  };\n\n  const scrollIndicatorVariants = {\n    animate: {\n      y: [0, 10, 0],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  return (\n    <section \n      className={`relative h-screen overflow-hidden bg-primary-800 ${className}`}\n      style={style}\n    >\n      {/* Video Background */}\n      <div className=\"absolute inset-0\">\n        <video\n          ref={videoRef}\n          autoPlay={autoplay}\n          muted={muted}\n          loop={loop}\n          playsInline\n          poster={posterSrc}\n          onLoadedData={handleVideoLoad}\n          onPlay={() => setIsPlaying(true)}\n          onPause={() => setIsPlaying(false)}\n          className={`w-full h-full object-cover transition-opacity duration-1000 ${\n            isVideoLoaded ? 'opacity-100' : 'opacity-0'\n          }`}\n        >\n          <source src={videoSrc} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n\n        {/* Video Overlay */}\n        <div \n          className=\"absolute inset-0 bg-black transition-opacity duration-300\"\n          style={{ opacity: overlayOpacity }}\n        />\n\n        {/* Gradient Overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 h-full flex flex-col justify-center items-center text-center px-6 sm:px-8 lg:px-12\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"max-w-4xl\"\n        >\n          {/* Headline */}\n          <motion.h1\n            variants={itemVariants}\n            className=\"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-cream-50 leading-tight mb-6\"\n          >\n            {headline.split(' ').map((word, index) => (\n              <motion.span\n                key={index}\n                variants={{\n                  hidden: { opacity: 0, y: 50 },\n                  visible: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                      duration: 0.6,\n                      delay: index * 0.1,\n                      ease: brandAnimations.easing.smooth\n                    }\n                  }\n                }}\n                className=\"inline-block mr-3\"\n              >\n                {word}\n              </motion.span>\n            ))}\n          </motion.h1>\n\n          {/* Subtitle */}\n          {subtitle && (\n            <motion.p\n              variants={itemVariants}\n              className=\"text-xl sm:text-2xl md:text-3xl text-cream-200 mb-8 leading-relaxed max-w-3xl mx-auto\"\n            >\n              {subtitle}\n            </motion.p>\n          )}\n\n          {/* CTA Buttons */}\n          <motion.div\n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            {/* Primary CTA */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={onPrimaryCTA}\n              className=\"inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent bg-gold-500 text-primary-800 hover:bg-gold-400 focus:ring-gold-400\"\n            >\n              {primaryCTA}\n              <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </motion.button>\n\n            {/* Secondary CTA */}\n            {secondaryCTA && onSecondaryCTA && (\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={onSecondaryCTA}\n                className=\"inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent border-2 border-cream-50 text-cream-50 hover:bg-cream-50 hover:text-primary-800 focus:ring-cream-50\"\n              >\n                {secondaryCTA}\n              </motion.button>\n            )}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Video Controls */}\n      <div className=\"absolute bottom-6 left-6 z-20\">\n        <motion.button\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          onClick={handlePlayPause}\n          className=\"bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200\"\n          aria-label={isPlaying ? 'Pause video' : 'Play video'}\n        >\n          {isPlaying ? (\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          ) : (\n            <svg className=\"w-6 h-6 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n            </svg>\n          )}\n        </motion.button>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        variants={scrollIndicatorVariants}\n        animate=\"animate\"\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\"\n      >\n        <div className=\"flex flex-col items-center text-cream-50\">\n          <span className=\"text-sm font-medium mb-2\">Scroll</span>\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n          </svg>\n        </div>\n      </motion.div>\n\n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 right-0 z-20\">\n        <svg viewBox=\"0 0 1440 120\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path \n            d=\"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 *********** 720 120C600 *********** 360 120C240 *********** 60 120H0Z\" \n            fill=\"var(--background)\"\n          />\n        </svg>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroVideoWithCTA;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;;;AANA;;;;AAQA;;;;;;;;;;;;;;;;;;CAkBC,GACD,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,WAAW,IAAI,EACf,OAAO,IAAI,EACX,QAAQ,IAAI,EACZ,iBAAiB,GAAG,EACpB,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,SAAS,UAAU;gBACrB,MAAM,IAAI,GAAG,KAAK;kDAAC;wBACjB,8CAA8C;wBAC9C,aAAa;oBACf;;YACF;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,IAAI,MAAM,MAAM,EAAE;gBAChB,MAAM,IAAI;gBACV,aAAa;YACf,OAAO;gBACL,MAAM,KAAK;gBACX,aAAa;YACf;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,MAAM,0BAA0B;QAC9B,SAAS;YACP,GAAG;gBAAC;gBAAG;gBAAI;aAAE;YACb,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,iDAAiD,EAAE,WAAW;QAC1E,OAAO;;0BAGP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,QAAQ,IAAM,aAAa;wBAC3B,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,4DAA4D,EACtE,gBAAgB,gBAAgB,aAChC;;0CAEF,6LAAC;gCAAO,KAAK;gCAAU,MAAK;;;;;;4BAAc;;;;;;;kCAK5C,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;wBAAe;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAET,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,UAAU;wCACR,QAAQ;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC5B,SAAS;4CACP,SAAS;4CACT,GAAG;4CACH,YAAY;gDACV,UAAU;gDACV,OAAO,QAAQ;gDACf,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;4CACrC;wCACF;oCACF;oCACA,WAAU;8CAET;mCAfI;;;;;;;;;;wBAqBV,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET;;;;;;sCAKL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;wCAET;sDACD,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;gCAKxE,gBAAgB,gCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;8CAET;;;;;;;;;;;;;;;;;;;;;;;0BAQX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAI;oBACvB,SAAS;oBACT,WAAU;oBACV,cAAY,YAAY,gBAAgB;8BAEvC,0BACC,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAe,SAAQ;kCACnD,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAsH,UAAS;;;;;;;;;;6CAG5J,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAe,SAAQ;kCACxD,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAA0G,UAAS;;;;;;;;;;;;;;;;;;;;;0BAOtJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAA2B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,SAAQ;oBAAe,MAAK;oBAAO,OAAM;8BAC5C,cAAA,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB;GAhPM;KAAA;uCAkPS", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/cards/AnimatedCardSlider.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport { AnimatedCardSliderProps, Product } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * AnimatedCardSlider Component\n * \n * An animated card slider for product showcases with smooth transitions and touch support.\n * Features auto-advance, navigation controls, and responsive design.\n * \n * @example\n * ```tsx\n * <AnimatedCardSlider\n *   products={products}\n *   visibleCards={3}\n *   autoAdvance={5000}\n *   touchEnabled={true}\n *   onCardClick={(product) => router.push(`/products/${product.id}`)}\n * />\n * ```\n */\nconst AnimatedCardSlider: React.FC<AnimatedCardSliderProps> = ({\n  products,\n  visibleCards = 3,\n  autoAdvance = 4000,\n  touchEnabled = true,\n  showDots = true,\n  showArrows = true,\n  onCardClick,\n  className = '',\n  style,\n}) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isHovered, setIsHovered] = useState(false);\n  const [touchStart, setTouchStart] = useState(0);\n  const [touchEnd, setTouchEnd] = useState(0);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  const maxIndex = Math.max(0, products.length - visibleCards);\n\n  // Auto-advance functionality\n  useEffect(() => {\n    if (autoAdvance && !isHovered && products.length > visibleCards) {\n      intervalRef.current = setInterval(() => {\n        setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));\n      }, autoAdvance);\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [autoAdvance, isHovered, maxIndex, products.length, visibleCards]);\n\n  // Touch handlers\n  const handleTouchStart = (e: React.TouchEvent) => {\n    if (!touchEnabled) return;\n    setTouchStart(e.targetTouches[0].clientX);\n  };\n\n  const handleTouchMove = (e: React.TouchEvent) => {\n    if (!touchEnabled) return;\n    setTouchEnd(e.targetTouches[0].clientX);\n  };\n\n  const handleTouchEnd = () => {\n    if (!touchEnabled || !touchStart || !touchEnd) return;\n    \n    const distance = touchStart - touchEnd;\n    const isLeftSwipe = distance > 50;\n    const isRightSwipe = distance < -50;\n\n    if (isLeftSwipe) {\n      nextSlide();\n    } else if (isRightSwipe) {\n      prevSlide();\n    }\n  };\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));\n  };\n\n  const nextSlide = () => {\n    setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));\n  };\n\n  const prevSlide = () => {\n    setCurrentIndex(prev => (prev <= 0 ? maxIndex : prev - 1));\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 20 },\n    visible: { opacity: 1, scale: 1, y: 0 },\n    hover: { y: -8, scale: 1.02 }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`relative ${className}`}\n      style={style}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      onTouchStart={handleTouchStart}\n      onTouchMove={handleTouchMove}\n      onTouchEnd={handleTouchEnd}\n    >\n      {/* Slider Container */}\n      <div className=\"overflow-hidden\">\n        <motion.div\n          className=\"flex transition-transform duration-500 ease-out\"\n          style={{\n            transform: `translateX(-${currentIndex * (100 / visibleCards)}%)`,\n            gap: '24px'\n          }}\n        >\n          {products.map((product, index) => (\n            <motion.div\n              key={product.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              className=\"flex-shrink-0 cursor-pointer\"\n              style={{ width: `calc(${100 / visibleCards}% - ${24 * (visibleCards - 1) / visibleCards}px)` }}\n              onClick={() => onCardClick?.(product)}\n            >\n              <div className=\"bg-cream-50 rounded-xl overflow-hidden group\"\n                   style={{ boxShadow: brandShadows.soft }}>\n                {/* Product Image */}\n                <div className=\"relative h-48 bg-gradient-to-br from-primary-100 to-sage-100\">\n                  {product.image ? (\n                    <Image\n                      src={product.image}\n                      alt={product.name}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full flex items-center justify-center\">\n                      <div className=\"w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center\">\n                        <svg className=\"w-8 h-8 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n                        </svg>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Category Badge */}\n                  <div className=\"absolute top-3 right-3\">\n                    <span \n                      className=\"text-xs font-semibold px-2 py-1 rounded-full text-cream-50\"\n                      style={{ backgroundColor: brandColors.primary[600] }}\n                    >\n                      {product.category}\n                    </span>\n                  </div>\n\n                  {/* Hover overlay */}\n                  <div className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\" />\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                    {product.name}\n                  </h3>\n\n                  {product.description && (\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                      {product.description}\n                    </p>\n                  )}\n\n                  {/* Product Details */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      {formatPrice(product.price)}\n                    </span>\n                    \n                    {product.strain && (\n                      <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                        {product.strain}\n                      </span>\n                    )}\n                  </div>\n\n                  {/* THC/CBD Info */}\n                  {(product.thc || product.cbd) && (\n                    <div className=\"flex gap-2 mb-4\">\n                      {product.thc && (\n                        <span className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded\">\n                          THC: {product.thc}%\n                        </span>\n                      )}\n                      {product.cbd && (\n                        <span className=\"text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded\">\n                          CBD: {product.cbd}%\n                        </span>\n                      )}\n                    </div>\n                  )}\n\n                  {/* Effects Tags */}\n                  {product.effects && product.effects.length > 0 && (\n                    <div className=\"flex flex-wrap gap-1\">\n                      {product.effects.slice(0, 3).map((effect, idx) => (\n                        <span \n                          key={idx}\n                          className=\"text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded\"\n                        >\n                          {effect}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {showArrows && products.length > visibleCards && (\n        <>\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={prevSlide}\n            className=\"absolute left-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10\"\n            style={{ boxShadow: brandShadows.medium }}\n            aria-label=\"Previous slide\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </motion.button>\n\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={nextSlide}\n            className=\"absolute right-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10\"\n            style={{ boxShadow: brandShadows.medium }}\n            aria-label=\"Next slide\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {showDots && products.length > visibleCards && (\n        <div className=\"flex justify-center mt-6 space-x-2\">\n          {Array.from({ length: maxIndex + 1 }).map((_, index) => (\n            <motion.button\n              key={index}\n              whileHover={{ scale: 1.2 }}\n              whileTap={{ scale: 0.8 }}\n              onClick={() => goToSlide(index)}\n              className={`w-3 h-3 rounded-full transition-colors duration-200 ${\n                index === currentIndex\n                  ? 'bg-primary-600'\n                  : 'bg-cream-300 hover:bg-primary-300'\n              }`}\n              aria-label={`Go to slide ${index + 1}`}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Progress Bar */}\n      {autoAdvance && !isHovered && (\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-cream-200\">\n          <motion.div\n            className=\"h-full bg-primary-600\"\n            initial={{ width: '0%' }}\n            animate={{ width: '100%' }}\n            transition={{\n              duration: autoAdvance / 1000,\n              ease: 'linear',\n              repeat: Infinity\n            }}\n          />\n        </div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default AnimatedCardSlider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAQA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,qBAAwD,CAAC,EAC7D,QAAQ,EACR,eAAe,CAAC,EAChB,cAAc,IAAI,EAClB,eAAe,IAAI,EACnB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,WAAW,EACX,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG;IAE/C,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,eAAe,CAAC,aAAa,SAAS,MAAM,GAAG,cAAc;gBAC/D,YAAY,OAAO,GAAG;oDAAY;wBAChC;4DAAgB,CAAA,OAAS,QAAQ,WAAW,IAAI,OAAO;;oBACzD;mDAAG;YACL;YAEA;gDAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;uCAAG;QAAC;QAAa;QAAW;QAAU,SAAS,MAAM;QAAE;KAAa;IAEpE,iBAAiB;IACjB,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,cAAc;QACnB,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;IAC1C;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,cAAc;QACnB,YAAY,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU;QAE/C,MAAM,WAAW,aAAa;QAC9B,MAAM,cAAc,WAAW;QAC/B,MAAM,eAAe,WAAW,CAAC;QAEjC,IAAI,aAAa;YACf;QACF,OAAO,IAAI,cAAc;YACvB;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;IAC9C;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAS,QAAQ,WAAW,IAAI,OAAO;IACzD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAS,QAAQ,IAAI,WAAW,OAAO;IACzD;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAK,GAAG;QAAG;QACxC,SAAS;YAAE,SAAS;YAAG,OAAO;YAAG,GAAG;QAAE;QACtC,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;IAC9B;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,OAAO;QACP,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;QACjC,cAAc;QACd,aAAa;QACb,YAAY;;0BAGZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,YAAY,EAAE,EAAE,CAAC;wBACjE,KAAK;oBACP;8BAEC,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,WAAU;4BACV,OAAO;gCAAE,OAAO,CAAC,KAAK,EAAE,MAAM,aAAa,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,aAAa,GAAG,CAAC;4BAAC;4BAC7F,SAAS,IAAM,cAAc;sCAE7B,cAAA,6LAAC;gCAAI,WAAU;gCACV,OAAO;oCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;gCAAC;;kDAEzC,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,KAAK,iBACZ,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAe,SAAQ;kEACpE,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;0DAOhB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oDAAC;8DAElD,QAAQ,QAAQ;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;4CAGd,QAAQ,WAAW,kBAClB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAKxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,YAAY,QAAQ,KAAK;;;;;;oDAG3B,QAAQ,MAAM,kBACb,6LAAC;wDAAK,WAAU;kEACb,QAAQ,MAAM;;;;;;;;;;;;4CAMpB,CAAC,QAAQ,GAAG,IAAI,QAAQ,GAAG,mBAC1B,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,GAAG,kBACV,6LAAC;wDAAK,WAAU;;4DAA4D;4DACpE,QAAQ,GAAG;4DAAC;;;;;;;oDAGrB,QAAQ,GAAG,kBACV,6LAAC;wDAAK,WAAU;;4DAAsD;4DAC9D,QAAQ,GAAG;4DAAC;;;;;;;;;;;;;4CAOzB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACxC,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;2BAxFZ,QAAQ,EAAE;;;;;;;;;;;;;;;YAwGtB,cAAc,SAAS,MAAM,GAAG,8BAC/B;;kCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS;wBACT,WAAU;wBACV,OAAO;4BAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,MAAM;wBAAC;wBACxC,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAIzE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS;wBACT,WAAU;wBACV,OAAO;4BAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,MAAM;wBAAC;wBACxC,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;YAO5E,YAAY,SAAS,MAAM,GAAG,8BAC7B,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ,WAAW;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS,IAAM,UAAU;wBACzB,WAAW,CAAC,oDAAoD,EAC9D,UAAU,eACN,mBACA,qCACJ;wBACF,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;uBATjC;;;;;;;;;;YAgBZ,eAAe,CAAC,2BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;oBAAK;oBACvB,SAAS;wBAAE,OAAO;oBAAO;oBACzB,YAAY;wBACV,UAAU,cAAc;wBACxB,MAAM;wBACN,QAAQ;oBACV;;;;;;;;;;;;;;;;;AAMZ;GAnSM;KAAA;uCAqSS", "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/grids/TabbedProductGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useMemo } from 'react';\nimport Image from 'next/image';\nimport { TabbedProductGridProps, Product } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * TabbedProductGrid Component\n * \n * A tabbed grid system for organizing products by categories (flower, extracts, edibles).\n * Features smooth animations, pagination, and responsive design.\n * \n * @example\n * ```tsx\n * <TabbedProductGrid\n *   products={products}\n *   categories={[\n *     { id: 'flower', name: 'Flower', icon: <FlowerIcon /> },\n *     { id: 'extract', name: 'Extracts', icon: <ExtractIcon /> }\n *   ]}\n *   columns={3}\n *   itemsPerPage={9}\n *   onProductClick={(product) => router.push(`/products/${product.id}`)}\n * />\n * ```\n */\nconst TabbedProductGrid: React.FC<TabbedProductGridProps> = ({\n  products,\n  categories,\n  defaultCategory,\n  columns = 3,\n  itemsPerPage = 9,\n  showPagination = true,\n  onProductClick,\n  className = '',\n  style,\n}) => {\n  const [activeCategory, setActiveCategory] = useState(\n    defaultCategory || categories[0]?.id || 'all'\n  );\n  const [currentPage, setCurrentPage] = useState(1);\n\n  // Filter products by active category\n  const filteredProducts = useMemo(() => {\n    if (activeCategory === 'all') return products;\n    return products.filter(product => product.category === activeCategory);\n  }, [products, activeCategory]);\n\n  // Paginate products\n  const paginatedProducts = useMemo(() => {\n    if (!showPagination) return filteredProducts;\n    \n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return filteredProducts.slice(startIndex, endIndex);\n  }, [filteredProducts, currentPage, itemsPerPage, showPagination]);\n\n  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);\n\n  // Reset to first page when category changes\n  const handleCategoryChange = (categoryId: string) => {\n    setActiveCategory(categoryId);\n    setCurrentPage(1);\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  const getCategoryIcon = (categoryId: string) => {\n    const category = categories.find(cat => cat.id === categoryId);\n    return category?.icon || (\n      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n        <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n      </svg>\n    );\n  };\n\n  const tabVariants = {\n    hidden: { opacity: 0, y: 10 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  const gridVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 20 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: {\n        duration: 0.4,\n        ease: brandAnimations.easing.smooth\n      }\n    },\n    hover: { y: -8, scale: 1.02 }\n  };\n\n  return (\n    <div className={`tabbed-product-grid ${className}`} style={style}>\n      {/* Tab Navigation */}\n      <div className=\"mb-8\">\n        <nav className=\"flex flex-wrap gap-2 justify-center\">\n          {/* All Products Tab */}\n          <motion.button\n            variants={tabVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            onClick={() => handleCategoryChange('all')}\n            className={`px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${\n              activeCategory === 'all'\n                ? 'bg-primary-600 text-cream-50 border-primary-600'\n                : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n            }`}\n          >\n            <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\n            </svg>\n            All Products\n            <span className=\"bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full\">\n              {products.length}\n            </span>\n          </motion.button>\n\n          {/* Category Tabs */}\n          {categories.map((category) => {\n            const categoryProducts = products.filter(p => p.category === category.id);\n            return (\n              <motion.button\n                key={category.id}\n                variants={tabVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                onClick={() => handleCategoryChange(category.id)}\n                className={`px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${\n                  activeCategory === category.id\n                    ? 'bg-primary-600 text-cream-50 border-primary-600'\n                    : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n                }`}\n              >\n                {category.icon}\n                {category.name}\n                <span className=\"bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full\">\n                  {categoryProducts.length}\n                </span>\n              </motion.button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Product Grid */}\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={activeCategory}\n          variants={gridVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"hidden\"\n          className={`grid gap-6 ${\n            columns === 1 ? 'grid-cols-1' :\n            columns === 2 ? 'grid-cols-1 md:grid-cols-2' :\n            columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :\n            columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :\n            'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'\n          }`}\n        >\n          {paginatedProducts.map((product) => (\n            <motion.div\n              key={product.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              className=\"cursor-pointer\"\n              onClick={() => onProductClick?.(product)}\n            >\n              <div \n                className=\"bg-cream-50 rounded-xl overflow-hidden group\"\n                style={{ boxShadow: brandShadows.soft }}\n              >\n                {/* Product Image */}\n                <div className=\"relative h-48 bg-gradient-to-br from-primary-100 to-sage-100\">\n                  {product.image ? (\n                    <Image\n                      src={product.image}\n                      alt={product.name}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full flex items-center justify-center\">\n                      <div className=\"w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center\">\n                        {getCategoryIcon(product.category)}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Category Badge */}\n                  <div className=\"absolute top-3 right-3\">\n                    <span \n                      className=\"text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize\"\n                      style={{ backgroundColor: brandColors.primary[600] }}\n                    >\n                      {product.category}\n                    </span>\n                  </div>\n\n                  {/* Hover overlay */}\n                  <div className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\" />\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                    {product.name}\n                  </h3>\n\n                  {product.description && (\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                      {product.description}\n                    </p>\n                  )}\n\n                  {/* Product Details */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      {formatPrice(product.price)}\n                    </span>\n                    \n                    {product.strain && (\n                      <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                        {product.strain}\n                      </span>\n                    )}\n                  </div>\n\n                  {/* THC/CBD Info */}\n                  {(product.thc || product.cbd) && (\n                    <div className=\"flex gap-2 mb-4\">\n                      {product.thc && (\n                        <span className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded\">\n                          THC: {product.thc}%\n                        </span>\n                      )}\n                      {product.cbd && (\n                        <span className=\"text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded\">\n                          CBD: {product.cbd}%\n                        </span>\n                      )}\n                    </div>\n                  )}\n\n                  {/* Effects Tags */}\n                  {product.effects && product.effects.length > 0 && (\n                    <div className=\"flex flex-wrap gap-1\">\n                      {product.effects.slice(0, 3).map((effect, idx) => (\n                        <span \n                          key={idx}\n                          className=\"text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded\"\n                        >\n                          {effect}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </AnimatePresence>\n\n      {/* Pagination */}\n      {showPagination && totalPages > 1 && (\n        <div className=\"flex justify-center items-center gap-2 mt-8\">\n          <button\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n            className=\"px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors\"\n          >\n            Previous\n          </button>\n          \n          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\n            <button\n              key={page}\n              onClick={() => handlePageChange(page)}\n              className={`px-3 py-2 rounded-md border transition-colors ${\n                page === currentPage\n                  ? 'bg-primary-600 text-cream-50 border-primary-600'\n                  : 'border-cream-300 hover:border-primary-300'\n              }`}\n            >\n              {page}\n            </button>\n          ))}\n          \n          <button\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            className=\"px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors\"\n          >\n            Next\n          </button>\n        </div>\n      )}\n\n      {/* Empty State */}\n      {paginatedProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-500\">Try selecting a different category.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TabbedProductGrid;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAEA;;;AANA;;;;;AAQA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,UAAU,EACV,eAAe,EACf,UAAU,CAAC,EACX,eAAe,CAAC,EAChB,iBAAiB,IAAI,EACrB,cAAc,EACd,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,mBAAmB,UAAU,CAAC,EAAE,EAAE,MAAM;IAE1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qCAAqC;IACrC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YAC/B,IAAI,mBAAmB,OAAO,OAAO;YACrC,OAAO,SAAS,MAAM;+DAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;;QACzD;sDAAG;QAAC;QAAU;KAAe;IAE7B,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAChC,IAAI,CAAC,gBAAgB,OAAO;YAE5B,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;YACvC,MAAM,WAAW,aAAa;YAC9B,OAAO,iBAAiB,KAAK,CAAC,YAAY;QAC5C;uDAAG;QAAC;QAAkB;QAAa;QAAc;KAAe;IAEhE,MAAM,aAAa,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;IAEvD,4CAA4C;IAC5C,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACnD,OAAO,UAAU,sBACf,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;sBACnD,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;IAGd;IAEA,MAAM,cAAc;QAClB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAK,GAAG;QAAG;QACxC,SAAS;YACP,SAAS;YACT,OAAO;YACP,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;QACA,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,oBAAoB,EAAE,WAAW;QAAE,OAAO;;0BAEzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,SAAS,IAAM,qBAAqB;4BACpC,WAAW,CAAC,sGAAsG,EAChH,mBAAmB,QACf,oDACA,oEACJ;;8CAEF,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;gCACJ;8CAEN,6LAAC;oCAAK,WAAU;8CACb,SAAS,MAAM;;;;;;;;;;;;wBAKnB,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,EAAE;4BACxE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,SAAS,IAAM,qBAAqB,SAAS,EAAE;gCAC/C,WAAW,CAAC,sGAAsG,EAChH,mBAAmB,SAAS,EAAE,GAC1B,oDACA,oEACJ;;oCAED,SAAS,IAAI;oCACb,SAAS,IAAI;kDACd,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,MAAM;;;;;;;+BAdrB,SAAS,EAAE;;;;;wBAkBtB;;;;;;;;;;;;0BAKJ,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,WAAW,CAAC,WAAW,EACrB,YAAY,IAAI,gBAChB,YAAY,IAAI,+BAChB,YAAY,IAAI,8CAChB,YAAY,IAAI,6DAChB,4EACA;8BAED,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,WAAU;4BACV,SAAS,IAAM,iBAAiB;sCAEhC,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;gCAAC;;kDAGtC,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,KAAK,iBACZ,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,QAAQ,QAAQ;;;;;;;;;;;0DAMvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oDAAC;8DAElD,QAAQ,QAAQ;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;4CAGd,QAAQ,WAAW,kBAClB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAKxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,YAAY,QAAQ,KAAK;;;;;;oDAG3B,QAAQ,MAAM,kBACb,6LAAC;wDAAK,WAAU;kEACb,QAAQ,MAAM;;;;;;;;;;;;4CAMpB,CAAC,QAAQ,GAAG,IAAI,QAAQ,GAAG,mBAC1B,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,GAAG,kBACV,6LAAC;wDAAK,WAAU;;4DAA4D;4DACpE,QAAQ,GAAG;4DAAC;;;;;;;oDAGrB,QAAQ,GAAG,kBACV,6LAAC;wDAAK,WAAU;;4DAAsD;4DAC9D,QAAQ,GAAG;4DAAC;;;;;;;;;;;;;4CAOzB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACxC,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;2BAvFZ,QAAQ,EAAE;;;;;mBAfd;;;;;;;;;;YAsHR,kBAAkB,aAAa,mBAC9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;oBAIA,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,qBACxD,6LAAC;4BAEC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,8CAA8C,EACxD,SAAS,cACL,oDACA,6CACJ;sCAED;2BARI;;;;;kCAYT,6LAAC;wBACC,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;;;;;;;YAOJ,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GAvTM;KAAA;uCAyTS", "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/grids/MasonryProductShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useMemo, useEffect, useRef } from 'react';\nimport Image from 'next/image';\nimport { MasonryProductShowcaseProps, Product } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * MasonryProductShowcase Component\n * \n * A masonry/carousel layout for dynamic product displays with filtering capabilities.\n * Features responsive masonry grid with smooth animations and filter controls.\n * \n * @example\n * ```tsx\n * <MasonryProductShowcase\n *   products={products}\n *   columns={3}\n *   gap={20}\n *   carouselMode={false}\n *   filters={[\n *     { id: 'category', name: 'Category', value: 'flower' },\n *     { id: 'price', name: 'Price Range', value: 'under-50' }\n *   ]}\n *   onProductClick={(product) => router.push(`/products/${product.id}`)}\n * />\n * ```\n */\nconst MasonryProductShowcase: React.FC<MasonryProductShowcaseProps> = ({\n  products,\n  columns = 3,\n  gap = 20,\n  carouselMode = false,\n  filters = [],\n  onProductClick,\n  className = '',\n  style,\n}) => {\n  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Filter products based on active filters\n  const filteredProducts = useMemo(() => {\n    return products.filter(product => {\n      return Object.entries(activeFilters).every(([filterId, filterValue]) => {\n        if (!filterValue) return true;\n        \n        switch (filterId) {\n          case 'category':\n            return product.category === filterValue;\n          case 'price':\n            if (filterValue === 'under-25') return product.price < 25;\n            if (filterValue === '25-50') return product.price >= 25 && product.price <= 50;\n            if (filterValue === '50-100') return product.price > 50 && product.price <= 100;\n            if (filterValue === 'over-100') return product.price > 100;\n            return true;\n          case 'effects':\n            return product.effects?.includes(filterValue);\n          default:\n            return true;\n        }\n      });\n    });\n  }, [products, activeFilters]);\n\n  // Handle filter changes\n  const handleFilterChange = (filterId: string, value: string) => {\n    setActiveFilters(prev => ({\n      ...prev,\n      [filterId]: prev[filterId] === value ? '' : value\n    }));\n  };\n\n  // Clear all filters\n  const clearFilters = () => {\n    setActiveFilters({});\n  };\n\n  // Carousel navigation\n  const nextSlide = () => {\n    if (carouselMode) {\n      setCurrentSlide(prev => (prev + 1) % Math.ceil(filteredProducts.length / columns));\n    }\n  };\n\n  const prevSlide = () => {\n    if (carouselMode) {\n      setCurrentSlide(prev => prev === 0 ? Math.ceil(filteredProducts.length / columns) - 1 : prev - 1);\n    }\n  };\n\n  // Auto-advance carousel\n  useEffect(() => {\n    if (carouselMode) {\n      const interval = setInterval(nextSlide, 5000);\n      return () => clearInterval(interval);\n    }\n  }, [carouselMode]);\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 20 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: {\n        duration: 0.4,\n        ease: brandAnimations.easing.smooth\n      }\n    },\n    hover: { y: -8, scale: 1.02 }\n  };\n\n  // Get random height for masonry effect\n  const getRandomHeight = (index: number) => {\n    const heights = [200, 250, 300, 350];\n    return heights[index % heights.length];\n  };\n\n  return (\n    <div className={`masonry-product-showcase ${className}`} style={style}>\n      {/* Filter Controls */}\n      {filters.length > 0 && (\n        <div className=\"mb-8\">\n          <div className=\"flex flex-wrap gap-4 items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-charcoal-800\">Filter Products</h3>\n            {Object.keys(activeFilters).some(key => activeFilters[key]) && (\n              <button\n                onClick={clearFilters}\n                className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\"\n              >\n                Clear All Filters\n              </button>\n            )}\n          </div>\n          \n          <div className=\"flex flex-wrap gap-3\">\n            {filters.map((filter) => (\n              <div key={filter.id} className=\"flex flex-wrap gap-2\">\n                <button\n                  onClick={() => handleFilterChange(filter.id, filter.value)}\n                  className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${\n                    activeFilters[filter.id] === filter.value\n                      ? 'bg-primary-600 text-cream-50 border-primary-600'\n                      : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n                  }`}\n                >\n                  {filter.name}\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Results Count */}\n      <div className=\"mb-6\">\n        <p className=\"text-sm text-gray-600\">\n          Showing {filteredProducts.length} of {products.length} products\n        </p>\n      </div>\n\n      {/* Masonry Grid */}\n      <div ref={containerRef} className=\"relative\">\n        {carouselMode ? (\n          // Carousel Mode\n          <div className=\"overflow-hidden\">\n            <motion.div\n              className=\"flex transition-transform duration-500 ease-out\"\n              style={{\n                transform: `translateX(-${currentSlide * 100}%)`,\n                gap: `${gap}px`\n              }}\n            >\n              {Array.from({ length: Math.ceil(filteredProducts.length / columns) }).map((_, slideIndex) => (\n                <div\n                  key={slideIndex}\n                  className=\"flex-shrink-0 w-full grid gap-4\"\n                  style={{\n                    gridTemplateColumns: `repeat(${columns}, 1fr)`,\n                    gap: `${gap}px`\n                  }}\n                >\n                  {filteredProducts\n                    .slice(slideIndex * columns, (slideIndex + 1) * columns)\n                    .map((product, index) => (\n                      <motion.div\n                        key={product.id}\n                        variants={cardVariants}\n                        whileHover=\"hover\"\n                        className=\"cursor-pointer\"\n                        onClick={() => onProductClick?.(product)}\n                      >\n                        <ProductCard \n                          product={product} \n                          height={getRandomHeight(slideIndex * columns + index)}\n                        />\n                      </motion.div>\n                    ))}\n                </div>\n              ))}\n            </motion.div>\n\n            {/* Carousel Controls */}\n            <div className=\"flex justify-center items-center gap-4 mt-6\">\n              <button\n                onClick={prevSlide}\n                className=\"p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors\"\n                style={{ boxShadow: brandShadows.medium }}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </button>\n              \n              <div className=\"flex gap-2\">\n                {Array.from({ length: Math.ceil(filteredProducts.length / columns) }).map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentSlide(index)}\n                    className={`w-3 h-3 rounded-full transition-colors ${\n                      index === currentSlide ? 'bg-primary-600' : 'bg-cream-300'\n                    }`}\n                  />\n                ))}\n              </div>\n              \n              <button\n                onClick={nextSlide}\n                className=\"p-2 rounded-full bg-cream-50 hover:bg-primary-600 hover:text-cream-50 transition-colors\"\n                style={{ boxShadow: brandShadows.medium }}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        ) : (\n          // Masonry Mode\n          <motion.div\n            variants={containerVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            className=\"columns-1 md:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5\"\n            style={{ \n              columnGap: `${gap}px`,\n              columnCount: columns \n            }}\n          >\n            {filteredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                variants={cardVariants}\n                whileHover=\"hover\"\n                className=\"cursor-pointer break-inside-avoid mb-4\"\n                onClick={() => onProductClick?.(product)}\n              >\n                <ProductCard \n                  product={product} \n                  height={getRandomHeight(index)}\n                />\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n      </div>\n\n      {/* Empty State */}\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products found</h3>\n          <p className=\"text-gray-500\">Try adjusting your filters or search terms.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Product Card Component\ninterface ProductCardProps {\n  product: Product;\n  height: number;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product, height }) => {\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  return (\n    <div \n      className=\"bg-cream-50 rounded-xl overflow-hidden group\"\n      style={{ boxShadow: brandShadows.soft }}\n    >\n      {/* Product Image */}\n      <div \n        className=\"relative bg-gradient-to-br from-primary-100 to-sage-100\"\n        style={{ height: `${height}px` }}\n      >\n        {product.image ? (\n          <Image\n            src={product.image}\n            alt={product.name}\n            fill\n            className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center\">\n            <div className=\"w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n              </svg>\n            </div>\n          </div>\n        )}\n\n        {/* Category Badge */}\n        <div className=\"absolute top-3 right-3\">\n          <span \n            className=\"text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize\"\n            style={{ backgroundColor: brandColors.primary[600] }}\n          >\n            {product.category}\n          </span>\n        </div>\n\n        {/* Hover overlay */}\n        <div className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n          {product.name}\n        </h3>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-lg font-bold text-primary-600\">\n            {formatPrice(product.price)}\n          </span>\n          \n          {product.strain && (\n            <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n              {product.strain}\n            </span>\n          )}\n        </div>\n\n        {/* THC/CBD Info */}\n        {(product.thc || product.cbd) && (\n          <div className=\"flex gap-2 mb-3\">\n            {product.thc && (\n              <span className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded\">\n                THC: {product.thc}%\n              </span>\n            )}\n            {product.cbd && (\n              <span className=\"text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded\">\n                CBD: {product.cbd}%\n              </span>\n            )}\n          </div>\n        )}\n\n        {/* Effects Tags */}\n        {product.effects && product.effects.length > 0 && (\n          <div className=\"flex flex-wrap gap-1\">\n            {product.effects.slice(0, 2).map((effect, idx) => (\n              <span \n                key={idx}\n                className=\"text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded\"\n              >\n                {effect}\n              </span>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default MasonryProductShowcase;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAQA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,yBAAgE,CAAC,EACrE,QAAQ,EACR,UAAU,CAAC,EACX,MAAM,EAAE,EACR,eAAe,KAAK,EACpB,UAAU,EAAE,EACZ,cAAc,EACd,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4DAAE;YAC/B,OAAO,SAAS,MAAM;oEAAC,CAAA;oBACrB,OAAO,OAAO,OAAO,CAAC,eAAe,KAAK;4EAAC,CAAC,CAAC,UAAU,YAAY;4BACjE,IAAI,CAAC,aAAa,OAAO;4BAEzB,OAAQ;gCACN,KAAK;oCACH,OAAO,QAAQ,QAAQ,KAAK;gCAC9B,KAAK;oCACH,IAAI,gBAAgB,YAAY,OAAO,QAAQ,KAAK,GAAG;oCACvD,IAAI,gBAAgB,SAAS,OAAO,QAAQ,KAAK,IAAI,MAAM,QAAQ,KAAK,IAAI;oCAC5E,IAAI,gBAAgB,UAAU,OAAO,QAAQ,KAAK,GAAG,MAAM,QAAQ,KAAK,IAAI;oCAC5E,IAAI,gBAAgB,YAAY,OAAO,QAAQ,KAAK,GAAG;oCACvD,OAAO;gCACT,KAAK;oCACH,OAAO,QAAQ,OAAO,EAAE,SAAS;gCACnC;oCACE,OAAO;4BACX;wBACF;;gBACF;;QACF;2DAAG;QAAC;QAAU;KAAc;IAE5B,wBAAwB;IACxB,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,QAAQ,KAAK;YAC9C,CAAC;IACH;IAEA,oBAAoB;IACpB,MAAM,eAAe;QACnB,iBAAiB,CAAC;IACpB;IAEA,sBAAsB;IACtB,MAAM,YAAY;QAChB,IAAI,cAAc;YAChB,gBAAgB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;QAC3E;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,cAAc;YAChB,gBAAgB,CAAA,OAAQ,SAAS,IAAI,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,WAAW,IAAI,OAAO;QACjG;IACF;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,cAAc;gBAChB,MAAM,WAAW,YAAY,WAAW;gBACxC;wDAAO,IAAM,cAAc;;YAC7B;QACF;2CAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAK,GAAG;QAAG;QACxC,SAAS;YACP,SAAS;YACT,OAAO;YACP,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;QACA,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;IAC9B;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU;YAAC;YAAK;YAAK;YAAK;SAAI;QACpC,OAAO,OAAO,CAAC,QAAQ,QAAQ,MAAM,CAAC;IACxC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;QAAE,OAAO;;YAE7D,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;4BACvD,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,MAAO,aAAa,CAAC,IAAI,mBACxD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAAoB,WAAU;0CAC7B,cAAA,6LAAC;oCACC,SAAS,IAAM,mBAAmB,OAAO,EAAE,EAAE,OAAO,KAAK;oCACzD,WAAW,CAAC,8EAA8E,EACxF,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,KAAK,GACrC,oDACA,oEACJ;8CAED,OAAO,IAAI;;;;;;+BATN,OAAO,EAAE;;;;;;;;;;;;;;;;0BAkB3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAwB;wBAC1B,iBAAiB,MAAM;wBAAC;wBAAK,SAAS,MAAM;wBAAC;;;;;;;;;;;;0BAK1D,6LAAC;gBAAI,KAAK;gBAAc,WAAU;0BAC/B,eACC,gBAAgB;8BAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,WAAW,CAAC,YAAY,EAAE,eAAe,IAAI,EAAE,CAAC;gCAChD,KAAK,GAAG,IAAI,EAAE,CAAC;4BACjB;sCAEC,MAAM,IAAI,CAAC;gCAAE,QAAQ,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;4BAAS,GAAG,GAAG,CAAC,CAAC,GAAG,2BAC5E,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCACL,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;wCAC9C,KAAK,GAAG,IAAI,EAAE,CAAC;oCACjB;8CAEC,iBACE,KAAK,CAAC,aAAa,SAAS,CAAC,aAAa,CAAC,IAAI,SAC/C,GAAG,CAAC,CAAC,SAAS,sBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,YAAW;4CACX,WAAU;4CACV,SAAS,IAAM,iBAAiB;sDAEhC,cAAA,6LAAC;gDACC,SAAS;gDACT,QAAQ,gBAAgB,aAAa,UAAU;;;;;;2CAR5C,QAAQ,EAAE;;;;;mCAXhB;;;;;;;;;;sCA4BX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO;wCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,MAAM;oCAAC;8CAExC,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAIzE,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG;oCAAS,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC5E,6LAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,uCAAuC,EACjD,UAAU,eAAe,mBAAmB,gBAC5C;2CAJG;;;;;;;;;;8CASX,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO;wCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,MAAM;oCAAC;8CAExC,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAM7E,eAAe;8BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;oBACV,OAAO;wBACL,WAAW,GAAG,IAAI,EAAE,CAAC;wBACrB,aAAa;oBACf;8BAEC,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,WAAU;4BACV,SAAS,IAAM,iBAAiB;sCAEhC,cAAA,6LAAC;gCACC,SAAS;gCACT,QAAQ,gBAAgB;;;;;;2BARrB,QAAQ,EAAE;;;;;;;;;;;;;;;YAiBxB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GAjRM;KAAA;AAyRN,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE;IAClE,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;QAAC;;0BAGtC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;gBAAC;;oBAE9B,QAAQ,KAAK,iBACZ,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAA2B,MAAK;gCAAe,SAAQ;0CACpE,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;kCAOhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;4BAAC;sCAElD,QAAQ,QAAQ;;;;;;;;;;;kCAKrB,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,YAAY,QAAQ,KAAK;;;;;;4BAG3B,QAAQ,MAAM,kBACb,6LAAC;gCAAK,WAAU;0CACb,QAAQ,MAAM;;;;;;;;;;;;oBAMpB,CAAC,QAAQ,GAAG,IAAI,QAAQ,GAAG,mBAC1B,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,GAAG,kBACV,6LAAC;gCAAK,WAAU;;oCAA4D;oCACpE,QAAQ,GAAG;oCAAC;;;;;;;4BAGrB,QAAQ,GAAG,kBACV,6LAAC;gCAAK,WAAU;;oCAAsD;oCAC9D,QAAQ,GAAG;oCAAC;;;;;;;;;;;;;oBAOzB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACxC,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;;;;;;;;AAWrB;MAnGM;uCAqGS", "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/effects/GlitchTextEffect.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { GlitchTextEffectProps } from '../../../types/ui';\nimport { brandColors, brandTypography } from '../../../styles/brand';\n\n/**\n * GlitchTextEffect Component\n * \n * Creates glitch text effects for modern, edgy branding elements.\n * Features customizable intensity, colors, and animation triggers.\n * \n * @example\n * ```tsx\n * <GlitchTextEffect\n *   text=\"APOTHECARY FARMS\"\n *   intensity=\"high\"\n *   trigger=\"hover\"\n *   colors={['#2FB886', '#ff0080', '#00ff80']}\n *   fontSize=\"4rem\"\n * />\n * ```\n */\nconst GlitchTextEffect: React.FC<GlitchTextEffectProps> = ({\n  text,\n  intensity = 'medium',\n  trigger = 'hover',\n  colors = [brandColors.apothecary, '#ff0080', '#00ff80'],\n  fontSize = '2rem',\n  fontWeight = 700,\n  className = '',\n  style,\n}) => {\n  const [isGlitching, setIsGlitching] = useState(trigger === 'continuous');\n  const [glitchText, setGlitchText] = useState(text);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  const [ref, inView] = useInView({\n    threshold: 0.1,\n    triggerOnce: trigger === 'scroll',\n  });\n\n  // Glitch characters for text corruption\n  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';\n  const numbers = '0123456789';\n  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';\n\n  // Get intensity settings\n  const getIntensitySettings = () => {\n    switch (intensity) {\n      case 'low':\n        return {\n          glitchDuration: 100,\n          glitchInterval: 2000,\n          corruptionChance: 0.1,\n          maxCorruptedChars: 2,\n        };\n      case 'medium':\n        return {\n          glitchDuration: 200,\n          glitchInterval: 1500,\n          corruptionChance: 0.2,\n          maxCorruptedChars: 4,\n        };\n      case 'high':\n        return {\n          glitchDuration: 300,\n          glitchInterval: 1000,\n          corruptionChance: 0.3,\n          maxCorruptedChars: 6,\n        };\n      default:\n        return {\n          glitchDuration: 200,\n          glitchInterval: 1500,\n          corruptionChance: 0.2,\n          maxCorruptedChars: 4,\n        };\n    }\n  };\n\n  const settings = getIntensitySettings();\n\n  // Generate corrupted text\n  const generateGlitchedText = () => {\n    const chars = text.split('');\n    const corruptedIndices = new Set<number>();\n    \n    // Randomly select characters to corrupt\n    const numToCorrupt = Math.min(\n      Math.floor(Math.random() * settings.maxCorruptedChars) + 1,\n      chars.length\n    );\n    \n    while (corruptedIndices.size < numToCorrupt) {\n      const randomIndex = Math.floor(Math.random() * chars.length);\n      if (chars[randomIndex] !== ' ') {\n        corruptedIndices.add(randomIndex);\n      }\n    }\n    \n    // Replace selected characters with glitch characters\n    corruptedIndices.forEach(index => {\n      const charSets = [glitchChars, numbers, letters];\n      const randomSet = charSets[Math.floor(Math.random() * charSets.length)];\n      chars[index] = randomSet[Math.floor(Math.random() * randomSet.length)];\n    });\n    \n    return chars.join('');\n  };\n\n  // Start glitch effect\n  const startGlitch = () => {\n    if (intervalRef.current) return;\n    \n    setIsGlitching(true);\n    \n    intervalRef.current = setInterval(() => {\n      setGlitchText(generateGlitchedText());\n      \n      // Reset to original text after glitch duration\n      timeoutRef.current = setTimeout(() => {\n        setGlitchText(text);\n      }, settings.glitchDuration);\n    }, settings.glitchInterval);\n  };\n\n  // Stop glitch effect\n  const stopGlitch = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = null;\n    }\n    setIsGlitching(false);\n    setGlitchText(text);\n  };\n\n  // Handle trigger effects\n  useEffect(() => {\n    if (trigger === 'continuous') {\n      startGlitch();\n    } else if (trigger === 'scroll' && inView) {\n      startGlitch();\n      // Stop after a few cycles\n      setTimeout(stopGlitch, 5000);\n    }\n\n    return () => {\n      stopGlitch();\n    };\n  }, [trigger, inView]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      stopGlitch();\n    };\n  }, []);\n\n  const handleMouseEnter = () => {\n    if (trigger === 'hover') {\n      startGlitch();\n    }\n  };\n\n  const handleMouseLeave = () => {\n    if (trigger === 'hover') {\n      stopGlitch();\n    }\n  };\n\n  // Generate CSS for glitch effect\n  const glitchStyles = {\n    position: 'relative' as const,\n    fontSize,\n    fontWeight,\n    fontFamily: brandTypography.fonts.sans,\n    color: colors[0],\n    cursor: trigger === 'hover' ? 'pointer' : 'default',\n    ...style,\n  };\n\n  const beforeAfterStyles = {\n    content: `\"${glitchText}\"`,\n    position: 'absolute' as const,\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    background: 'transparent',\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      className={`glitch-text-effect ${className}`}\n      style={glitchStyles}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Main text */}\n      <span className=\"relative z-10\">{glitchText}</span>\n      \n      {/* Glitch layers */}\n      {isGlitching && (\n        <>\n          {/* Red glitch layer */}\n          <motion.span\n            className=\"absolute top-0 left-0 z-0\"\n            style={{\n              color: colors[1] || '#ff0080',\n              clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)',\n            }}\n            animate={{\n              x: [-2, 2, -1, 1, 0],\n              y: [0, -1, 1, 0, -1],\n            }}\n            transition={{\n              duration: 0.1,\n              repeat: Infinity,\n              repeatType: 'mirror',\n            }}\n          >\n            {glitchText}\n          </motion.span>\n          \n          {/* Blue glitch layer */}\n          <motion.span\n            className=\"absolute top-0 left-0 z-0\"\n            style={{\n              color: colors[2] || '#00ff80',\n              clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)',\n            }}\n            animate={{\n              x: [1, -2, 2, -1, 0],\n              y: [1, 0, -1, 1, 0],\n            }}\n            transition={{\n              duration: 0.1,\n              repeat: Infinity,\n              repeatType: 'mirror',\n              delay: 0.05,\n            }}\n          >\n            {glitchText}\n          </motion.span>\n          \n          {/* Scan lines */}\n          <motion.div\n            className=\"absolute inset-0 z-20 pointer-events-none\"\n            style={{\n              background: `repeating-linear-gradient(\n                0deg,\n                transparent,\n                transparent 2px,\n                rgba(255, 255, 255, 0.03) 2px,\n                rgba(255, 255, 255, 0.03) 4px\n              )`,\n            }}\n            animate={{\n              opacity: [0.5, 1, 0.5],\n            }}\n            transition={{\n              duration: 0.1,\n              repeat: Infinity,\n              repeatType: 'mirror',\n            }}\n          />\n          \n          {/* Random noise overlay */}\n          <motion.div\n            className=\"absolute inset-0 z-10 pointer-events-none\"\n            style={{\n              background: `radial-gradient(circle, transparent 50%, rgba(255, 255, 255, 0.1) 50%)`,\n              backgroundSize: '4px 4px',\n            }}\n            animate={{\n              backgroundPosition: ['0px 0px', '4px 4px', '0px 4px', '4px 0px'],\n              opacity: [0, 0.3, 0, 0.2, 0],\n            }}\n            transition={{\n              duration: 0.2,\n              repeat: Infinity,\n              ease: 'linear',\n            }}\n          />\n        </>\n      )}\n      \n      {/* Glow effect */}\n      {isGlitching && (\n        <motion.div\n          className=\"absolute inset-0 z-0\"\n          style={{\n            color: colors[0],\n            filter: 'blur(4px)',\n            opacity: 0.5,\n          }}\n          animate={{\n            opacity: [0.3, 0.7, 0.3],\n          }}\n          transition={{\n            duration: 0.2,\n            repeat: Infinity,\n            repeatType: 'mirror',\n          }}\n        >\n          {glitchText}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default GlitchTextEffect;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAQA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,mBAAoD,CAAC,EACzD,IAAI,EACJ,YAAY,QAAQ,EACpB,UAAU,OAAO,EACjB,SAAS;IAAC,yHAAA,CAAA,cAAW,CAAC,UAAU;IAAE;IAAW;CAAU,EACvD,WAAW,MAAM,EACjB,aAAa,GAAG,EAChB,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa,YAAY;IAC3B;IAEA,wCAAwC;IACxC,MAAM,cAAc;IACpB,MAAM,UAAU;IAChB,MAAM,UAAU;IAEhB,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB;oBAChB,kBAAkB;oBAClB,mBAAmB;gBACrB;YACF,KAAK;gBACH,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB;oBAChB,kBAAkB;oBAClB,mBAAmB;gBACrB;YACF,KAAK;gBACH,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB;oBAChB,kBAAkB;oBAClB,mBAAmB;gBACrB;YACF;gBACE,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB;oBAChB,kBAAkB;oBAClB,mBAAmB;gBACrB;QACJ;IACF;IAEA,MAAM,WAAW;IAEjB,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,mBAAmB,IAAI;QAE7B,wCAAwC;QACxC,MAAM,eAAe,KAAK,GAAG,CAC3B,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,iBAAiB,IAAI,GACzD,MAAM,MAAM;QAGd,MAAO,iBAAiB,IAAI,GAAG,aAAc;YAC3C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;YAC3D,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK;gBAC9B,iBAAiB,GAAG,CAAC;YACvB;QACF;QAEA,qDAAqD;QACrD,iBAAiB,OAAO,CAAC,CAAA;YACvB,MAAM,WAAW;gBAAC;gBAAa;gBAAS;aAAQ;YAChD,MAAM,YAAY,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;YACvE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;QACxE;QAEA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,sBAAsB;IACtB,MAAM,cAAc;QAClB,IAAI,YAAY,OAAO,EAAE;QAEzB,eAAe;QAEf,YAAY,OAAO,GAAG,YAAY;YAChC,cAAc;YAEd,+CAA+C;YAC/C,WAAW,OAAO,GAAG,WAAW;gBAC9B,cAAc;YAChB,GAAG,SAAS,cAAc;QAC5B,GAAG,SAAS,cAAc;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa;QACjB,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;YACjC,YAAY,OAAO,GAAG;QACxB;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;YAC/B,WAAW,OAAO,GAAG;QACvB;QACA,eAAe;QACf,cAAc;IAChB;IAEA,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY,cAAc;gBAC5B;YACF,OAAO,IAAI,YAAY,YAAY,QAAQ;gBACzC;gBACA,0BAA0B;gBAC1B,WAAW,YAAY;YACzB;YAEA;8CAAO;oBACL;gBACF;;QACF;qCAAG;QAAC;QAAS;KAAO;IAEpB,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;8CAAO;oBACL;gBACF;;QACF;qCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,YAAY,SAAS;YACvB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,YAAY,SAAS;YACvB;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM,eAAe;QACnB,UAAU;QACV;QACA;QACA,YAAY,yHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,IAAI;QACtC,OAAO,MAAM,CAAC,EAAE;QAChB,QAAQ,YAAY,UAAU,YAAY;QAC1C,GAAG,KAAK;IACV;IAEA,MAAM,oBAAoB;QACxB,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC1B,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAC,mBAAmB,EAAE,WAAW;QAC5C,OAAO;QACP,cAAc;QACd,cAAc;QACd,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;YAGhC,6BACC;;kCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,OAAO;4BACL,OAAO,MAAM,CAAC,EAAE,IAAI;4BACpB,UAAU;wBACZ;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAG;gCAAG,CAAC;gCAAG;gCAAG;6BAAE;4BACpB,GAAG;gCAAC;gCAAG,CAAC;gCAAG;gCAAG;gCAAG,CAAC;6BAAE;wBACtB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;wBACd;kCAEC;;;;;;kCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,OAAO;4BACL,OAAO,MAAM,CAAC,EAAE,IAAI;4BACpB,UAAU;wBACZ;wBACA,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAG;gCAAG,CAAC;gCAAG;6BAAE;4BACpB,GAAG;gCAAC;gCAAG;gCAAG,CAAC;gCAAG;gCAAG;6BAAE;wBACrB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,OAAO;wBACT;kCAEC;;;;;;kCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC;;;;;;eAMZ,CAAC;wBACJ;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;wBACd;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,sEAAsE,CAAC;4BACpF,gBAAgB;wBAClB;wBACA,SAAS;4BACP,oBAAoB;gCAAC;gCAAW;gCAAW;gCAAW;6BAAU;4BAChE,SAAS;gCAAC;gCAAG;gCAAK;gCAAG;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;YAML,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,OAAO,MAAM,CAAC,EAAE;oBAChB,QAAQ;oBACR,SAAS;gBACX;gBACA,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAKX;GAzSM;;QAekB,sKAAA,CAAA,YAAS;;;KAf3B;uCA2SS", "debugId": null}}, {"offset": {"line": 2853, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/cards/FilterableStrainCards.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Image from 'next/image';\nimport { FilterableStrainCardsProps, Strain } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * FilterableStrainCards Component\n * \n * Build filterable strain cards with search, category filters, and sorting options.\n * Features comprehensive filtering system for cannabis strains with responsive design.\n * \n * @example\n * ```tsx\n * <FilterableStrainCards\n *   strains={strains}\n *   enableSearch={true}\n *   filters={[\n *     { id: 'type', name: 'Type', options: ['sativa', 'indica', 'hybrid'] },\n *     { id: 'effects', name: 'Effects', options: ['relaxed', 'energetic', 'creative'] }\n *   ]}\n *   sortOptions={[\n *     { id: 'name', name: 'Name', field: 'name' },\n *     { id: 'thc', name: 'THC %', field: 'thc' }\n *   ]}\n *   cardsPerRow={3}\n *   onStrainClick={(strain) => router.push(`/strains/${strain.id}`)}\n * />\n * ```\n */\nconst FilterableStrainCards: React.FC<FilterableStrainCardsProps> = ({\n  strains,\n  enableSearch = true,\n  filters = [],\n  sortOptions = [],\n  cardsPerRow = 3,\n  onStrainClick,\n  className = '',\n  style,\n}) => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});\n  const [sortBy, setSortBy] = useState<string>('');\n\n  // Filter and sort strains\n  const filteredAndSortedStrains = useMemo(() => {\n    let result = strains;\n\n    // Apply search filter\n    if (searchTerm) {\n      result = result.filter(strain =>\n        strain.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        strain.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        strain.effects.some(effect => effect.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        strain.flavors.some(flavor => flavor.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    }\n\n    // Apply category filters\n    Object.entries(activeFilters).forEach(([filterId, filterValue]) => {\n      if (filterValue) {\n        result = result.filter(strain => {\n          switch (filterId) {\n            case 'type':\n              return strain.type === filterValue;\n            case 'effects':\n              return strain.effects.includes(filterValue);\n            case 'flavors':\n              return strain.flavors.includes(filterValue);\n            case 'thc':\n              if (filterValue === 'low') return strain.thc < 15;\n              if (filterValue === 'medium') return strain.thc >= 15 && strain.thc <= 25;\n              if (filterValue === 'high') return strain.thc > 25;\n              return true;\n            case 'cbd':\n              if (filterValue === 'low') return strain.cbd < 5;\n              if (filterValue === 'medium') return strain.cbd >= 5 && strain.cbd <= 15;\n              if (filterValue === 'high') return strain.cbd > 15;\n              return true;\n            default:\n              return true;\n          }\n        });\n      }\n    });\n\n    // Apply sorting\n    if (sortBy) {\n      const sortOption = sortOptions.find(option => option.id === sortBy);\n      if (sortOption) {\n        result = [...result].sort((a, b) => {\n          const aValue = a[sortOption.field as keyof Strain];\n          const bValue = b[sortOption.field as keyof Strain];\n          \n          if (typeof aValue === 'string' && typeof bValue === 'string') {\n            return aValue.localeCompare(bValue);\n          }\n          if (typeof aValue === 'number' && typeof bValue === 'number') {\n            return bValue - aValue; // Descending order for numbers\n          }\n          return 0;\n        });\n      }\n    }\n\n    return result;\n  }, [strains, searchTerm, activeFilters, sortBy, sortOptions]);\n\n  // Handle filter changes\n  const handleFilterChange = (filterId: string, value: string) => {\n    setActiveFilters(prev => ({\n      ...prev,\n      [filterId]: prev[filterId] === value ? '' : value\n    }));\n  };\n\n  // Clear all filters\n  const clearAllFilters = () => {\n    setSearchTerm('');\n    setActiveFilters({});\n    setSortBy('');\n  };\n\n  // Get strain type color\n  const getStrainTypeColor = (type: string) => {\n    switch (type) {\n      case 'sativa':\n        return brandColors.primary[500];\n      case 'indica':\n        return brandColors.sage[500];\n      case 'hybrid':\n        return brandColors.gold[500];\n      default:\n        return brandColors.charcoal[500];\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 20 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: {\n        duration: 0.4,\n        ease: brandAnimations.easing.smooth\n      }\n    },\n    hover: { y: -8, scale: 1.02 }\n  };\n\n  return (\n    <div className={`filterable-strain-cards ${className}`} style={style}>\n      {/* Search and Filter Controls */}\n      <div className=\"mb-8 space-y-4\">\n        {/* Search Bar */}\n        {enableSearch && (\n          <div className=\"relative max-w-md\">\n            <input\n              type=\"text\"\n              placeholder=\"Search strains...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n            <svg className=\"absolute left-3 top-3.5 h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n        )}\n\n        {/* Filters and Sort */}\n        <div className=\"flex flex-wrap gap-4 items-center justify-between\">\n          <div className=\"flex flex-wrap gap-3\">\n            {/* Filter Buttons */}\n            {filters.map((filter) => (\n              <div key={filter.id} className=\"flex flex-wrap gap-2\">\n                {filter.options.map((option) => (\n                  <button\n                    key={option}\n                    onClick={() => handleFilterChange(filter.id, option)}\n                    className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 capitalize ${\n                      activeFilters[filter.id] === option\n                        ? 'bg-primary-600 text-cream-50 border-primary-600'\n                        : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n                    }`}\n                  >\n                    {option}\n                  </button>\n                ))}\n              </div>\n            ))}\n          </div>\n\n          {/* Sort Dropdown */}\n          {sortOptions.length > 0 && (\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-4 py-2 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            >\n              <option value=\"\">Sort by...</option>\n              {sortOptions.map((option) => (\n                <option key={option.id} value={option.id}>\n                  {option.name}\n                </option>\n              ))}\n            </select>\n          )}\n        </div>\n\n        {/* Active Filters and Clear */}\n        {(searchTerm || Object.values(activeFilters).some(v => v) || sortBy) && (\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex flex-wrap gap-2\">\n              {searchTerm && (\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700\">\n                  Search: \"{searchTerm}\"\n                </span>\n              )}\n              {Object.entries(activeFilters).map(([filterId, value]) => \n                value && (\n                  <span key={filterId} className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700 capitalize\">\n                    {filterId}: {value}\n                  </span>\n                )\n              )}\n              {sortBy && (\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700\">\n                  Sorted by: {sortOptions.find(o => o.id === sortBy)?.name}\n                </span>\n              )}\n            </div>\n            <button\n              onClick={clearAllFilters}\n              className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\"\n            >\n              Clear All\n            </button>\n          </div>\n        )}\n\n        {/* Results Count */}\n        <p className=\"text-sm text-gray-600\">\n          Showing {filteredAndSortedStrains.length} of {strains.length} strains\n        </p>\n      </div>\n\n      {/* Strain Cards Grid */}\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={`${searchTerm}-${JSON.stringify(activeFilters)}-${sortBy}`}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"hidden\"\n          className={`grid gap-6 ${\n            cardsPerRow === 1 ? 'grid-cols-1' :\n            cardsPerRow === 2 ? 'grid-cols-1 md:grid-cols-2' :\n            cardsPerRow === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :\n            cardsPerRow === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :\n            'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'\n          }`}\n        >\n          {filteredAndSortedStrains.map((strain) => (\n            <motion.div\n              key={strain.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              className=\"cursor-pointer\"\n              onClick={() => onStrainClick?.(strain)}\n            >\n              <div \n                className=\"bg-cream-50 rounded-xl overflow-hidden group\"\n                style={{ boxShadow: brandShadows.soft }}\n              >\n                {/* Strain Image */}\n                <div className=\"relative h-48 bg-gradient-to-br from-primary-100 to-sage-100\">\n                  {strain.image ? (\n                    <Image\n                      src={strain.image}\n                      alt={strain.name}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full flex items-center justify-center\">\n                      <div className=\"w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center\">\n                        <svg className=\"w-8 h-8 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z\" />\n                        </svg>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Strain Type Badge */}\n                  <div className=\"absolute top-3 right-3\">\n                    <span \n                      className=\"text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize\"\n                      style={{ backgroundColor: getStrainTypeColor(strain.type) }}\n                    >\n                      {strain.type}\n                    </span>\n                  </div>\n\n                  {/* Hover overlay */}\n                  <div className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\" />\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                    {strain.name}\n                  </h3>\n\n                  <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                    {strain.description}\n                  </p>\n\n                  {/* THC/CBD Info */}\n                  <div className=\"flex gap-2 mb-4\">\n                    <span className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded\">\n                      THC: {strain.thc}%\n                    </span>\n                    <span className=\"text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded\">\n                      CBD: {strain.cbd}%\n                    </span>\n                  </div>\n\n                  {/* Effects */}\n                  <div className=\"flex flex-wrap gap-1 mb-3\">\n                    {strain.effects.slice(0, 3).map((effect, idx) => (\n                      <span \n                        key={idx}\n                        className=\"text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded\"\n                      >\n                        {effect}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Flavors */}\n                  <div className=\"flex flex-wrap gap-1\">\n                    {strain.flavors.slice(0, 2).map((flavor, idx) => (\n                      <span \n                        key={idx}\n                        className=\"text-xs bg-cream-200 text-charcoal-600 px-2 py-1 rounded\"\n                      >\n                        {flavor}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Genetics */}\n                  {strain.genetics && (\n                    <div className=\"mt-3 pt-3 border-t border-cream-200\">\n                      <p className=\"text-xs text-gray-500\">\n                        <span className=\"font-medium\">Genetics:</span> {strain.genetics}\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </AnimatePresence>\n\n      {/* Empty State */}\n      {filteredAndSortedStrains.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No strains found</h3>\n          <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FilterableStrainCards;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AAQA;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,MAAM,wBAA8D,CAAC,EACnE,OAAO,EACP,eAAe,IAAI,EACnB,UAAU,EAAE,EACZ,cAAc,EAAE,EAChB,cAAc,CAAC,EACf,aAAa,EACb,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,0BAA0B;IAC1B,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mEAAE;YACvC,IAAI,SAAS;YAEb,sBAAsB;YACtB,IAAI,YAAY;gBACd,SAAS,OAAO,MAAM;+EAAC,CAAA,SACrB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,OAAO,OAAO,CAAC,IAAI;uFAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;yFAClF,OAAO,OAAO,CAAC,IAAI;uFAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;YAEtF;YAEA,yBAAyB;YACzB,OAAO,OAAO,CAAC,eAAe,OAAO;2EAAC,CAAC,CAAC,UAAU,YAAY;oBAC5D,IAAI,aAAa;wBACf,SAAS,OAAO,MAAM;uFAAC,CAAA;gCACrB,OAAQ;oCACN,KAAK;wCACH,OAAO,OAAO,IAAI,KAAK;oCACzB,KAAK;wCACH,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC;oCACjC,KAAK;wCACH,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC;oCACjC,KAAK;wCACH,IAAI,gBAAgB,OAAO,OAAO,OAAO,GAAG,GAAG;wCAC/C,IAAI,gBAAgB,UAAU,OAAO,OAAO,GAAG,IAAI,MAAM,OAAO,GAAG,IAAI;wCACvE,IAAI,gBAAgB,QAAQ,OAAO,OAAO,GAAG,GAAG;wCAChD,OAAO;oCACT,KAAK;wCACH,IAAI,gBAAgB,OAAO,OAAO,OAAO,GAAG,GAAG;wCAC/C,IAAI,gBAAgB,UAAU,OAAO,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI;wCACtE,IAAI,gBAAgB,QAAQ,OAAO,OAAO,GAAG,GAAG;wCAChD,OAAO;oCACT;wCACE,OAAO;gCACX;4BACF;;oBACF;gBACF;;YAEA,gBAAgB;YAChB,IAAI,QAAQ;gBACV,MAAM,aAAa,YAAY,IAAI;0FAAC,CAAA,SAAU,OAAO,EAAE,KAAK;;gBAC5D,IAAI,YAAY;oBACd,SAAS;2BAAI;qBAAO,CAAC,IAAI;mFAAC,CAAC,GAAG;4BAC5B,MAAM,SAAS,CAAC,CAAC,WAAW,KAAK,CAAiB;4BAClD,MAAM,SAAS,CAAC,CAAC,WAAW,KAAK,CAAiB;4BAElD,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gCAC5D,OAAO,OAAO,aAAa,CAAC;4BAC9B;4BACA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gCAC5D,OAAO,SAAS,QAAQ,+BAA+B;4BACzD;4BACA,OAAO;wBACT;;gBACF;YACF;YAEA,OAAO;QACT;kEAAG;QAAC;QAAS;QAAY;QAAe;QAAQ;KAAY;IAE5D,wBAAwB;IACxB,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,QAAQ,KAAK;YAC9C,CAAC;IACH;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,cAAc;QACd,iBAAiB,CAAC;QAClB,UAAU;IACZ;IAEA,wBAAwB;IACxB,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;YACjC,KAAK;gBACH,OAAO,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;YAC9B,KAAK;gBACH,OAAO,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;YAC9B;gBACE,OAAO,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;QACpC;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAK,GAAG;QAAG;QACxC,SAAS;YACP,SAAS;YACT,OAAO;YACP,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;QACA,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;QAAE,OAAO;;0BAE7D,6LAAC;gBAAI,WAAU;;oBAEZ,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;gCAAgD,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACvG,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAEZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAAoB,WAAU;kDAC5B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,6LAAC;gDAEC,SAAS,IAAM,mBAAmB,OAAO,EAAE,EAAE;gDAC7C,WAAW,CAAC,yFAAyF,EACnG,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,SACzB,oDACA,oEACJ;0DAED;+CARI;;;;;uCAHD,OAAO,EAAE;;;;;;;;;;4BAmBtB,YAAY,MAAM,GAAG,mBACpB,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAAuB,OAAO,OAAO,EAAE;sDACrC,OAAO,IAAI;2CADD,OAAO,EAAE;;;;;;;;;;;;;;;;;oBAS7B,CAAC,cAAc,OAAO,MAAM,CAAC,eAAe,IAAI,CAAC,CAAA,IAAK,MAAM,MAAM,mBACjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,4BACC,6LAAC;wCAAK,WAAU;;4CAA0F;4CAC9F;4CAAW;;;;;;;oCAGxB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GACnD,uBACE,6LAAC;4CAAoB,WAAU;;gDAC5B;gDAAS;gDAAG;;2CADJ;;;;;oCAKd,wBACC,6LAAC;wCAAK,WAAU;;4CAA0F;4CAC5F,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;;;;;;;;;;;;;0CAI1D,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,yBAAyB,MAAM;4BAAC;4BAAK,QAAQ,MAAM;4BAAC;;;;;;;;;;;;;0BAKjE,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,WAAW,CAAC,WAAW,EACrB,gBAAgB,IAAI,gBACpB,gBAAgB,IAAI,+BACpB,gBAAgB,IAAI,8CACpB,gBAAgB,IAAI,6DACpB,4EACA;8BAED,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,WAAU;4BACV,SAAS,IAAM,gBAAgB;sCAE/B,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;gCAAC;;kDAGtC,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,KAAK,iBACX,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,OAAO,KAAK;gDACjB,KAAK,OAAO,IAAI;gDAChB,IAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAe,SAAQ;kEACpE,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;0DAOhB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,mBAAmB,OAAO,IAAI;oDAAE;8DAEzD,OAAO,IAAI;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,OAAO,IAAI;;;;;;0DAGd,6LAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAA4D;4DACpE,OAAO,GAAG;4DAAC;;;;;;;kEAEnB,6LAAC;wDAAK,WAAU;;4DAAsD;4DAC9D,OAAO,GAAG;4DAAC;;;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACvC,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DASX,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,oBACvC,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;4CASV,OAAO,QAAQ,kBACd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAgB;wDAAE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;2BA3FpE,OAAO,EAAE;;;;;mBAfb,GAAG,WAAW,CAAC,EAAE,KAAK,SAAS,CAAC,eAAe,CAAC,EAAE,QAAQ;;;;;;;;;;YAsHlE,yBAAyB,MAAM,KAAK,mBACnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GA3WM;KAAA;uCA6WS", "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/layout/VerticalTimeline.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport Image from 'next/image';\nimport { VerticalTimelineProps, TimelineItem } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * VerticalTimeline Component\n * \n * Create a vertical timeline for showing cultivation process, company history, or product journey.\n * Features alternating layout, scroll animations, and themed styling.\n * \n * @example\n * ```tsx\n * <VerticalTimeline\n *   items={timelineItems}\n *   theme=\"cultivation\"\n *   alternating={true}\n *   showLine={true}\n *   animateOnScroll={true}\n * />\n * ```\n */\nconst VerticalTimeline: React.FC<VerticalTimelineProps> = ({\n  items,\n  theme = 'cultivation',\n  alternating = true,\n  showLine = true,\n  animateOnScroll = true,\n  className = '',\n  style,\n}) => {\n  // Get theme colors\n  const getThemeColors = () => {\n    switch (theme) {\n      case 'cultivation':\n        return {\n          primary: brandColors.primary[600],\n          secondary: brandColors.sage[400],\n          accent: brandColors.gold[400],\n        };\n      case 'company':\n        return {\n          primary: brandColors.charcoal[700],\n          secondary: brandColors.cream[300],\n          accent: brandColors.apothecary,\n        };\n      case 'product':\n        return {\n          primary: brandColors.gold[600],\n          secondary: brandColors.primary[300],\n          accent: brandColors.sage[500],\n        };\n      default:\n        return {\n          primary: brandColors.primary[600],\n          secondary: brandColors.sage[400],\n          accent: brandColors.gold[400],\n        };\n    }\n  };\n\n  const themeColors = getThemeColors();\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  return (\n    <div className={`vertical-timeline relative ${className}`} style={style}>\n      {/* Timeline Line */}\n      {showLine && (\n        <div \n          className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full\"\n          style={{ backgroundColor: themeColors.secondary }}\n        />\n      )}\n\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, margin: '-100px' }}\n        className=\"space-y-12\"\n      >\n        {items.map((item, index) => (\n          <TimelineItemComponent\n            key={item.id}\n            item={item}\n            index={index}\n            isAlternating={alternating}\n            themeColors={themeColors}\n            animateOnScroll={animateOnScroll}\n          />\n        ))}\n      </motion.div>\n    </div>\n  );\n};\n\n// Individual Timeline Item Component\ninterface TimelineItemComponentProps {\n  item: TimelineItem;\n  index: number;\n  isAlternating: boolean;\n  themeColors: {\n    primary: string;\n    secondary: string;\n    accent: string;\n  };\n  animateOnScroll: boolean;\n}\n\nconst TimelineItemComponent: React.FC<TimelineItemComponentProps> = ({\n  item,\n  index,\n  isAlternating,\n  themeColors,\n  animateOnScroll,\n}) => {\n  const [ref, inView] = useInView({\n    threshold: 0.3,\n    triggerOnce: animateOnScroll,\n  });\n\n  const isLeft = isAlternating ? index % 2 === 0 : false;\n\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      x: isLeft ? -50 : 50,\n      y: 30,\n    },\n    visible: {\n      opacity: 1,\n      x: 0,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: brandAnimations.easing.smooth,\n      },\n    },\n  };\n\n  const iconVariants = {\n    hidden: { scale: 0, rotate: -180 },\n    visible: {\n      scale: 1,\n      rotate: 0,\n      transition: {\n        duration: 0.5,\n        ease: brandAnimations.easing.bounce,\n        delay: 0.2,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      variants={itemVariants}\n      initial=\"hidden\"\n      animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}\n      className={`relative flex items-center ${\n        isAlternating\n          ? isLeft\n            ? 'flex-row-reverse'\n            : 'flex-row'\n          : 'flex-row'\n      }`}\n    >\n      {/* Content */}\n      <div \n        className={`w-full ${\n          isAlternating ? 'md:w-5/12' : 'md:w-10/12 md:ml-16'\n        } ${isLeft ? 'md:pr-8' : 'md:pl-8'}`}\n      >\n        <motion.div\n          className=\"bg-cream-50 rounded-xl p-6 relative\"\n          style={{ boxShadow: brandShadows.soft }}\n          whileHover={{ y: -4 }}\n          transition={{ duration: 0.2 }}\n        >\n          {/* Arrow */}\n          {isAlternating && (\n            <div\n              className={`absolute top-6 w-0 h-0 ${\n                isLeft\n                  ? 'right-0 border-l-8 border-l-cream-50 border-y-8 border-y-transparent'\n                  : 'left-0 border-r-8 border-r-cream-50 border-y-8 border-y-transparent'\n              }`}\n            />\n          )}\n\n          {/* Date */}\n          <div className=\"mb-3\">\n            <span \n              className=\"inline-block px-3 py-1 rounded-full text-sm font-medium text-cream-50\"\n              style={{ backgroundColor: themeColors.accent }}\n            >\n              {item.date}\n            </span>\n          </div>\n\n          {/* Title */}\n          <h3 className=\"text-xl font-bold text-charcoal-800 mb-3\">\n            {item.title}\n          </h3>\n\n          {/* Description */}\n          <p className=\"text-charcoal-600 leading-relaxed mb-4\">\n            {item.description}\n          </p>\n\n          {/* Image */}\n          {item.image && (\n            <div className=\"relative h-48 rounded-lg overflow-hidden mb-4\">\n              <Image\n                src={item.image}\n                alt={item.title}\n                fill\n                className=\"object-cover\"\n              />\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Timeline Icon */}\n      <motion.div\n        variants={iconVariants}\n        initial=\"hidden\"\n        animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}\n        className={`absolute ${\n          isAlternating ? 'left-1/2 transform -translate-x-1/2' : 'left-6'\n        } z-10`}\n      >\n        <div\n          className=\"w-12 h-12 rounded-full flex items-center justify-center border-4 border-cream-50\"\n          style={{ backgroundColor: themeColors.primary }}\n        >\n          {item.icon ? (\n            <div className=\"text-cream-50\">\n              {item.icon}\n            </div>\n          ) : (\n            <div className=\"w-3 h-3 bg-cream-50 rounded-full\" />\n          )}\n        </div>\n      </motion.div>\n\n      {/* Spacer for alternating layout */}\n      {isAlternating && (\n        <div className=\"w-5/12 hidden md:block\" />\n      )}\n    </motion.div>\n  );\n};\n\nexport default VerticalTimeline;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;;;AAPA;;;;;AASA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,mBAAoD,CAAC,EACzD,KAAK,EACL,QAAQ,aAAa,EACrB,cAAc,IAAI,EAClB,WAAW,IAAI,EACf,kBAAkB,IAAI,EACtB,YAAY,EAAE,EACd,KAAK,EACN;IACC,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACjC,WAAW,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAChC,QAAQ,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;gBAC/B;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;oBAClC,WAAW,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;oBACjC,QAAQ,yHAAA,CAAA,cAAW,CAAC,UAAU;gBAChC;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAC9B,WAAW,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACnC,QAAQ,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;gBAC/B;YACF;gBACE,OAAO;oBACL,SAAS,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACjC,WAAW,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAChC,QAAQ,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;gBAC/B;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;QAAE,OAAO;;YAE/D,0BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,YAAY,SAAS;gBAAC;;;;;;0BAIpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAS;gBACzC,WAAU;0BAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wBAEC,MAAM;wBACN,OAAO;wBACP,eAAe;wBACf,aAAa;wBACb,iBAAiB;uBALZ,KAAK,EAAE;;;;;;;;;;;;;;;;AAWxB;KAlFM;AAiGN,MAAM,wBAA8D,CAAC,EACnE,IAAI,EACJ,KAAK,EACL,aAAa,EACb,WAAW,EACX,eAAe,EAChB;;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa;IACf;IAEA,MAAM,SAAS,gBAAgB,QAAQ,MAAM,IAAI;IAEjD,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG,SAAS,CAAC,KAAK;YAClB,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QACjC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACnC,OAAO;YACT;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,UAAU;QACV,SAAQ;QACR,SAAS,kBAAmB,SAAS,YAAY,WAAY;QAC7D,WAAW,CAAC,2BAA2B,EACrC,gBACI,SACE,qBACA,aACF,YACJ;;0BAGF,6LAAC;gBACC,WAAW,CAAC,OAAO,EACjB,gBAAgB,cAAc,sBAC/B,CAAC,EAAE,SAAS,YAAY,WAAW;0BAEpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;oBAAC;oBACtC,YAAY;wBAAE,GAAG,CAAC;oBAAE;oBACpB,YAAY;wBAAE,UAAU;oBAAI;;wBAG3B,+BACC,6LAAC;4BACC,WAAW,CAAC,uBAAuB,EACjC,SACI,yEACA,uEACJ;;;;;;sCAKN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,YAAY,MAAM;gCAAC;0CAE5C,KAAK,IAAI;;;;;;;;;;;sCAKd,6LAAC;4BAAG,WAAU;sCACX,KAAK,KAAK;;;;;;sCAIb,6LAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;wBAIlB,KAAK,KAAK,kBACT,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,KAAK;gCACf,KAAK,KAAK,KAAK;gCACf,IAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAS,kBAAmB,SAAS,YAAY,WAAY;gBAC7D,WAAW,CAAC,SAAS,EACnB,gBAAgB,wCAAwC,SACzD,KAAK,CAAC;0BAEP,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,YAAY,OAAO;oBAAC;8BAE7C,KAAK,IAAI,iBACR,6LAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI;;;;;6CAGZ,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAMpB,+BACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;GAhJM;;QAOkB,sKAAA,CAAA,YAAS;;;MAP3B;uCAkJS", "debugId": null}}, {"offset": {"line": 3836, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/layout/SplitMediaLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport Image from 'next/image';\nimport { SplitMediaLayoutProps } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * SplitMediaLayout Component\n * \n * Build split-screen layouts combining media and content for product education.\n * Features responsive design, animation triggers, and flexible media/content positioning.\n * \n * @example\n * ```tsx\n * <SplitMediaLayout\n *   media={{\n *     type: 'image',\n *     src: '/images/extraction-process.jpg',\n *     alt: 'Cannabis extraction process'\n *   }}\n *   content={{\n *     title: 'Our Extraction Process',\n *     description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes.',\n *     features: ['CO2 Extraction', 'Full Spectrum', 'Lab Tested', 'Solvent-Free'],\n *     cta: {\n *       text: 'Learn More',\n *       action: () => router.push('/process')\n *     }\n *   }}\n *   mediaPosition=\"left\"\n *   splitRatio=\"50-50\"\n *   verticalAlign=\"center\"\n * />\n * ```\n */\nconst SplitMediaLayout: React.FC<SplitMediaLayoutProps> = ({\n  media,\n  content,\n  mediaPosition = 'left',\n  splitRatio = '50-50',\n  verticalAlign = 'center',\n  className = '',\n  style,\n}) => {\n  const [ref, inView] = useInView({\n    threshold: 0.3,\n    triggerOnce: true,\n  });\n\n  // Get split ratio classes\n  const getSplitRatioClasses = () => {\n    switch (splitRatio) {\n      case '60-40':\n        return mediaPosition === 'left' \n          ? { media: 'lg:w-3/5', content: 'lg:w-2/5' }\n          : { media: 'lg:w-2/5', content: 'lg:w-3/5' };\n      case '40-60':\n        return mediaPosition === 'left'\n          ? { media: 'lg:w-2/5', content: 'lg:w-3/5' }\n          : { media: 'lg:w-3/5', content: 'lg:w-2/5' };\n      case '50-50':\n      default:\n        return { media: 'lg:w-1/2', content: 'lg:w-1/2' };\n    }\n  };\n\n  const ratioClasses = getSplitRatioClasses();\n\n  // Get vertical alignment classes\n  const getVerticalAlignClasses = () => {\n    switch (verticalAlign) {\n      case 'top':\n        return 'items-start';\n      case 'bottom':\n        return 'items-end';\n      case 'center':\n      default:\n        return 'items-center';\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const mediaVariants = {\n    hidden: { \n      opacity: 0, \n      x: mediaPosition === 'left' ? -50 : 50,\n      scale: 0.9\n    },\n    visible: { \n      opacity: 1, \n      x: 0,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: brandAnimations.easing.smooth\n      }\n    }\n  };\n\n  const contentVariants = {\n    hidden: { \n      opacity: 0, \n      x: mediaPosition === 'left' ? 50 : -50,\n      y: 20\n    },\n    visible: { \n      opacity: 1, \n      x: 0,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: brandAnimations.easing.smooth,\n        delay: 0.2\n      }\n    }\n  };\n\n  return (\n    <motion.section\n      ref={ref}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate={inView ? 'visible' : 'hidden'}\n      className={`split-media-layout py-16 ${className}`}\n      style={style}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`flex flex-col lg:flex-row ${getVerticalAlignClasses()} gap-8 lg:gap-12`}>\n          {/* Media Section */}\n          <motion.div\n            variants={mediaVariants}\n            className={`${ratioClasses.media} ${\n              mediaPosition === 'right' ? 'lg:order-2' : 'lg:order-1'\n            }`}\n          >\n            <div className=\"relative\">\n              {media.type === 'image' ? (\n                <div \n                  className=\"relative aspect-[4/3] rounded-xl overflow-hidden\"\n                  style={{ boxShadow: brandShadows.large }}\n                >\n                  <Image\n                    src={media.src}\n                    alt={media.alt || ''}\n                    fill\n                    className=\"object-cover\"\n                    sizes=\"(max-width: 768px) 100vw, 50vw\"\n                  />\n                  \n                  {/* Overlay gradient */}\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\" />\n                </div>\n              ) : (\n                <div \n                  className=\"relative aspect-video rounded-xl overflow-hidden\"\n                  style={{ boxShadow: brandShadows.large }}\n                >\n                  <video\n                    src={media.src}\n                    controls\n                    className=\"w-full h-full object-cover\"\n                    poster={media.alt} // Using alt as poster for video\n                  >\n                    Your browser does not support the video tag.\n                  </video>\n                </div>\n              )}\n\n              {/* Decorative elements */}\n              <motion.div\n                className=\"absolute -top-4 -right-4 w-8 h-8 rounded-full\"\n                style={{ backgroundColor: brandColors.apothecary }}\n                initial={{ scale: 0, opacity: 0 }}\n                animate={inView ? { scale: 1, opacity: 0.6 } : { scale: 0, opacity: 0 }}\n                transition={{ delay: 0.8, duration: 0.4 }}\n              />\n              <motion.div\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 rounded-full\"\n                style={{ backgroundColor: brandColors.gold[400] }}\n                initial={{ scale: 0, opacity: 0 }}\n                animate={inView ? { scale: 1, opacity: 0.4 } : { scale: 0, opacity: 0 }}\n                transition={{ delay: 1, duration: 0.4 }}\n              />\n            </div>\n          </motion.div>\n\n          {/* Content Section */}\n          <motion.div\n            variants={contentVariants}\n            className={`${ratioClasses.content} ${\n              mediaPosition === 'right' ? 'lg:order-1' : 'lg:order-2'\n            } flex flex-col justify-center`}\n          >\n            <div className=\"space-y-6\">\n              {/* Title */}\n              <motion.h2\n                className=\"text-3xl lg:text-4xl font-serif font-bold text-charcoal-800 leading-tight\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                transition={{ delay: 0.4, duration: 0.5 }}\n              >\n                {content.title}\n              </motion.h2>\n\n              {/* Description */}\n              <motion.p\n                className=\"text-lg text-charcoal-600 leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                transition={{ delay: 0.5, duration: 0.5 }}\n              >\n                {content.description}\n              </motion.p>\n\n              {/* Features List */}\n              {content.features && content.features.length > 0 && (\n                <motion.div\n                  className=\"space-y-3\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                  transition={{ delay: 0.6, duration: 0.5 }}\n                >\n                  {content.features.map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"flex items-center space-x-3\"\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}\n                      transition={{ delay: 0.7 + index * 0.1, duration: 0.4 }}\n                    >\n                      <div \n                        className=\"w-2 h-2 rounded-full flex-shrink-0\"\n                        style={{ backgroundColor: brandColors.apothecary }}\n                      />\n                      <span className=\"text-charcoal-700 font-medium\">\n                        {feature}\n                      </span>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              )}\n\n              {/* CTA Button */}\n              {content.cta && (\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n                  transition={{ delay: 0.8, duration: 0.5 }}\n                >\n                  <motion.button\n                    onClick={content.cta.action}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"inline-flex items-center px-6 py-3 rounded-lg font-semibold text-cream-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\"\n                    style={{\n                      backgroundColor: brandColors.primary[600],\n                      focusRingColor: brandColors.primary[400],\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.backgroundColor = brandColors.primary[700];\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.backgroundColor = brandColors.primary[600];\n                    }}\n                  >\n                    {content.cta.text}\n                    <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </motion.button>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default SplitMediaLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;;;AAPA;;;;;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,MAAM,mBAAoD,CAAC,EACzD,KAAK,EACL,OAAO,EACP,gBAAgB,MAAM,EACtB,aAAa,OAAO,EACpB,gBAAgB,QAAQ,EACxB,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa;IACf;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO,kBAAkB,SACrB;oBAAE,OAAO;oBAAY,SAAS;gBAAW,IACzC;oBAAE,OAAO;oBAAY,SAAS;gBAAW;YAC/C,KAAK;gBACH,OAAO,kBAAkB,SACrB;oBAAE,OAAO;oBAAY,SAAS;gBAAW,IACzC;oBAAE,OAAO;oBAAY,SAAS;gBAAW;YAC/C,KAAK;YACL;gBACE,OAAO;oBAAE,OAAO;oBAAY,SAAS;gBAAW;QACpD;IACF;IAEA,MAAM,eAAe;IAErB,iCAAiC;IACjC,MAAM,0BAA0B;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ;YACN,SAAS;YACT,GAAG,kBAAkB,SAAS,CAAC,KAAK;YACpC,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,GAAG,kBAAkB,SAAS,KAAK,CAAC;YACpC,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACnC,OAAO;YACT;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,UAAU;QACV,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,WAAW,CAAC,yBAAyB,EAAE,WAAW;QAClD,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,0BAA0B,EAAE,0BAA0B,gBAAgB,CAAC;;kCAEtF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAW,GAAG,aAAa,KAAK,CAAC,CAAC,EAChC,kBAAkB,UAAU,eAAe,cAC3C;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,IAAI,KAAK,wBACd,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,KAAK;oCAAC;;sDAEvC,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,MAAM,GAAG;4CACd,KAAK,MAAM,GAAG,IAAI;4CAClB,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;sDAIR,6LAAC;4CAAI,WAAU;;;;;;;;;;;yDAGjB,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,KAAK;oCAAC;8CAEvC,cAAA,6LAAC;wCACC,KAAK,MAAM,GAAG;wCACd,QAAQ;wCACR,WAAU;wCACV,QAAQ,MAAM,GAAG;kDAClB;;;;;;;;;;;8CAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,UAAU;oCAAC;oCACjD,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,SAAS,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAI,IAAI;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCACtE,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;;;;;8CAE1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oCAAC;oCAChD,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,SAAS,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAI,IAAI;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCACtE,YAAY;wCAAE,OAAO;wCAAG,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAM5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAW,GAAG,aAAa,OAAO,CAAC,CAAC,EAClC,kBAAkB,UAAU,eAAe,aAC5C,6BAA6B,CAAC;kCAE/B,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAEvC,QAAQ,KAAK;;;;;;8CAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAEvC,QAAQ,WAAW;;;;;;gCAIrB,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAEvC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9D,YAAY;gDAAE,OAAO,MAAM,QAAQ;gDAAK,UAAU;4CAAI;;8DAEtD,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,yHAAA,CAAA,cAAW,CAAC,UAAU;oDAAC;;;;;;8DAEnD,6LAAC;oDAAK,WAAU;8DACb;;;;;;;2CAXE;;;;;;;;;;gCAmBZ,QAAQ,GAAG,kBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAExC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,QAAQ,GAAG,CAAC,MAAM;wCAC3B,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;wCACV,OAAO;4CACL,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;4CACzC,gBAAgB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;wCAClE;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;wCAClE;;4CAEC,QAAQ,GAAG,CAAC,IAAI;0DACjB,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3F;GA7PM;;QASkB,sKAAA,CAAA,YAAS;;;KAT3B;uCA+PS", "debugId": null}}, {"offset": {"line": 4329, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/effects/FeatureIconsWithMotion.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { FeatureIconsWithMotionProps, FeatureItem } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * FeatureIconsWithMotion Component\n * \n * Build feature highlight sections with animated icons using Framer Motion.\n * Features customizable layouts, animation triggers, and cannabis industry icons.\n * \n * @example\n * ```tsx\n * <FeatureIconsWithMotion\n *   features={features}\n *   layout=\"grid\"\n *   animationTrigger=\"scroll\"\n *   columns={3}\n *   iconSize=\"lg\"\n * />\n * ```\n */\nconst FeatureIconsWithMotion: React.FC<FeatureIconsWithMotionProps> = ({\n  features,\n  layout = 'grid',\n  animationTrigger = 'scroll',\n  columns = 3,\n  iconSize = 'md',\n  className = '',\n  style,\n}) => {\n  const [ref, inView] = useInView({\n    threshold: 0.2,\n    triggerOnce: animationTrigger === 'scroll',\n  });\n\n  // Get icon size classes\n  const getIconSizeClasses = () => {\n    switch (iconSize) {\n      case 'sm':\n        return { container: 'w-12 h-12', icon: 'w-6 h-6' };\n      case 'md':\n        return { container: 'w-16 h-16', icon: 'w-8 h-8' };\n      case 'lg':\n        return { container: 'w-20 h-20', icon: 'w-10 h-10' };\n      case 'xl':\n        return { container: 'w-24 h-24', icon: 'w-12 h-12' };\n      default:\n        return { container: 'w-16 h-16', icon: 'w-8 h-8' };\n    }\n  };\n\n  const iconSizeClasses = getIconSizeClasses();\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'horizontal':\n        return 'flex flex-wrap justify-center gap-8';\n      case 'vertical':\n        return 'space-y-8';\n      case 'grid':\n      default:\n        return `grid gap-8 ${\n          columns === 1 ? 'grid-cols-1' :\n          columns === 2 ? 'grid-cols-1 md:grid-cols-2' :\n          columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :\n          columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :\n          'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'\n        }`;\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { \n      opacity: 0, \n      y: 30,\n      scale: 0.8\n    },\n    visible: { \n      opacity: 1, \n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        ease: brandAnimations.easing.smooth\n      }\n    }\n  };\n\n  const iconVariants = {\n    hidden: { \n      scale: 0,\n      rotate: -180\n    },\n    visible: { \n      scale: 1,\n      rotate: 0,\n      transition: {\n        duration: 0.6,\n        ease: brandAnimations.easing.bounce,\n        delay: 0.2\n      }\n    },\n    hover: {\n      scale: 1.1,\n      rotate: 5,\n      transition: {\n        duration: 0.2,\n        ease: brandAnimations.easing.smooth\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      ref={ref}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate={\n        animationTrigger === 'scroll' \n          ? (inView ? 'visible' : 'hidden')\n          : animationTrigger === 'load' \n          ? 'visible' \n          : 'hidden'\n      }\n      className={`feature-icons-with-motion ${className}`}\n      style={style}\n    >\n      <div className={getLayoutClasses()}>\n        {features.map((feature, index) => (\n          <FeatureCard\n            key={feature.id}\n            feature={feature}\n            index={index}\n            iconSizeClasses={iconSizeClasses}\n            itemVariants={itemVariants}\n            iconVariants={iconVariants}\n            animationTrigger={animationTrigger}\n            layout={layout}\n          />\n        ))}\n      </div>\n    </motion.div>\n  );\n};\n\n// Individual Feature Card Component\ninterface FeatureCardProps {\n  feature: FeatureItem;\n  index: number;\n  iconSizeClasses: { container: string; icon: string };\n  itemVariants: any;\n  iconVariants: any;\n  animationTrigger: 'scroll' | 'hover' | 'load';\n  layout: 'grid' | 'horizontal' | 'vertical';\n}\n\nconst FeatureCard: React.FC<FeatureCardProps> = ({\n  feature,\n  index,\n  iconSizeClasses,\n  itemVariants,\n  iconVariants,\n  animationTrigger,\n  layout,\n}) => {\n  const [cardRef, cardInView] = useInView({\n    threshold: 0.3,\n    triggerOnce: true,\n  });\n\n  const getFeatureColor = () => {\n    if (feature.color) return feature.color;\n    \n    // Default colors based on index\n    const colors = [\n      brandColors.primary[500],\n      brandColors.apothecary,\n      brandColors.gold[500],\n      brandColors.sage[500],\n      brandColors.charcoal[600],\n    ];\n    return colors[index % colors.length];\n  };\n\n  const featureColor = getFeatureColor();\n\n  return (\n    <motion.div\n      ref={cardRef}\n      variants={itemVariants}\n      className={`feature-card text-center ${\n        layout === 'horizontal' ? 'flex-shrink-0' : ''\n      }`}\n      whileHover={animationTrigger === 'hover' ? { y: -8 } : undefined}\n    >\n      {/* Icon Container */}\n      <motion.div\n        variants={iconVariants}\n        whileHover=\"hover\"\n        className={`${iconSizeClasses.container} mx-auto mb-4 rounded-xl flex items-center justify-center relative overflow-hidden`}\n        style={{ \n          backgroundColor: `${featureColor}15`,\n          boxShadow: brandShadows.soft \n        }}\n      >\n        {/* Background Glow */}\n        <motion.div\n          className=\"absolute inset-0 rounded-xl\"\n          style={{ backgroundColor: featureColor }}\n          initial={{ opacity: 0.1 }}\n          whileHover={{ opacity: 0.2 }}\n          transition={{ duration: 0.2 }}\n        />\n        \n        {/* Icon */}\n        <motion.div\n          className={`${iconSizeClasses.icon} relative z-10`}\n          style={{ color: featureColor }}\n          whileHover={{ \n            filter: 'brightness(1.2)',\n            transition: { duration: 0.2 }\n          }}\n        >\n          {feature.icon}\n        </motion.div>\n\n        {/* Pulse Effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-xl border-2\"\n          style={{ borderColor: featureColor }}\n          initial={{ scale: 1, opacity: 0 }}\n          animate={\n            animationTrigger === 'scroll' && cardInView\n              ? {\n                  scale: [1, 1.2, 1],\n                  opacity: [0, 0.6, 0],\n                }\n              : {}\n          }\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      </motion.div>\n\n      {/* Content */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={\n          animationTrigger === 'scroll' && cardInView\n            ? { opacity: 1, y: 0 }\n            : animationTrigger === 'load'\n            ? { opacity: 1, y: 0 }\n            : { opacity: 0, y: 20 }\n        }\n        transition={{\n          duration: 0.5,\n          delay: 0.3 + index * 0.1,\n          ease: brandAnimations.easing.smooth,\n        }}\n      >\n        <h3 \n          className=\"text-lg font-semibold mb-2\"\n          style={{ color: featureColor }}\n        >\n          {feature.title}\n        </h3>\n        <p className=\"text-charcoal-600 leading-relaxed\">\n          {feature.description}\n        </p>\n      </motion.div>\n\n      {/* Decorative Elements */}\n      <motion.div\n        className=\"absolute -top-2 -right-2 w-4 h-4 rounded-full\"\n        style={{ backgroundColor: featureColor }}\n        initial={{ scale: 0, opacity: 0 }}\n        animate={\n          animationTrigger === 'scroll' && cardInView\n            ? { scale: 1, opacity: 0.3 }\n            : animationTrigger === 'load'\n            ? { scale: 1, opacity: 0.3 }\n            : { scale: 0, opacity: 0 }\n        }\n        transition={{\n          duration: 0.4,\n          delay: 0.6 + index * 0.1,\n          ease: brandAnimations.easing.bounce,\n        }}\n      />\n    </motion.div>\n  );\n};\n\nexport default FeatureIconsWithMotion;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;;;AANA;;;;AAQA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,yBAAgE,CAAC,EACrE,QAAQ,EACR,SAAS,MAAM,EACf,mBAAmB,QAAQ,EAC3B,UAAU,CAAC,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa,qBAAqB;IACpC;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,WAAW;oBAAa,MAAM;gBAAU;YACnD,KAAK;gBACH,OAAO;oBAAE,WAAW;oBAAa,MAAM;gBAAU;YACnD,KAAK;gBACH,OAAO;oBAAE,WAAW;oBAAa,MAAM;gBAAY;YACrD,KAAK;gBACH,OAAO;oBAAE,WAAW;oBAAa,MAAM;gBAAY;YACrD;gBACE,OAAO;oBAAE,WAAW;oBAAa,MAAM;gBAAU;QACrD;IACF;IAEA,MAAM,kBAAkB;IAExB,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO,CAAC,WAAW,EACjB,YAAY,IAAI,gBAChB,YAAY,IAAI,+BAChB,YAAY,IAAI,8CAChB,YAAY,IAAI,6DAChB,4EACA;QACN;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,OAAO;YACP,QAAQ,CAAC;QACX;QACA,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACnC,OAAO;YACT;QACF;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,UAAU;QACV,SAAQ;QACR,SACE,qBAAqB,WAChB,SAAS,YAAY,WACtB,qBAAqB,SACrB,YACA;QAEN,WAAW,CAAC,0BAA0B,EAAE,WAAW;QACnD,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAW;sBACb,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oBAEC,SAAS;oBACT,OAAO;oBACP,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,kBAAkB;oBAClB,QAAQ;mBAPH,QAAQ,EAAE;;;;;;;;;;;;;;;AAa3B;GAtIM;;QASkB,sKAAA,CAAA,YAAS;;;KAT3B;AAmJN,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,KAAK,EACL,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,MAAM,EACP;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QACtC,WAAW;QACX,aAAa;IACf;IAEA,MAAM,kBAAkB;QACtB,IAAI,QAAQ,KAAK,EAAE,OAAO,QAAQ,KAAK;QAEvC,gCAAgC;QAChC,MAAM,SAAS;YACb,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;YACxB,yHAAA,CAAA,cAAW,CAAC,UAAU;YACtB,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;YACrB,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;YACrB,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;SAC1B;QACD,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;IACtC;IAEA,MAAM,eAAe;IAErB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,UAAU;QACV,WAAW,CAAC,yBAAyB,EACnC,WAAW,eAAe,kBAAkB,IAC5C;QACF,YAAY,qBAAqB,UAAU;YAAE,GAAG,CAAC;QAAE,IAAI;;0BAGvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,YAAW;gBACX,WAAW,GAAG,gBAAgB,SAAS,CAAC,kFAAkF,CAAC;gBAC3H,OAAO;oBACL,iBAAiB,GAAG,aAAa,EAAE,CAAC;oBACpC,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;gBAC9B;;kCAGA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAa;wBACvC,SAAS;4BAAE,SAAS;wBAAI;wBACxB,YAAY;4BAAE,SAAS;wBAAI;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;;;;;;kCAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,GAAG,gBAAgB,IAAI,CAAC,cAAc,CAAC;wBAClD,OAAO;4BAAE,OAAO;wBAAa;wBAC7B,YAAY;4BACV,QAAQ;4BACR,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCAEC,QAAQ,IAAI;;;;;;kCAIf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAa;wBACnC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,SACE,qBAAqB,YAAY,aAC7B;4BACE,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;wBACtB,IACA,CAAC;wBAEP,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,OAAO,QAAQ;wBACjB;;;;;;;;;;;;0BAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SACE,qBAAqB,YAAY,aAC7B;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IACnB,qBAAqB,SACrB;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IACnB;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAE1B,YAAY;oBACV,UAAU;oBACV,OAAO,MAAM,QAAQ;oBACrB,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACrC;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAa;kCAE5B,QAAQ,KAAK;;;;;;kCAEhB,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;;;;;;;0BAKxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAAa;gBACvC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,SACE,qBAAqB,YAAY,aAC7B;oBAAE,OAAO;oBAAG,SAAS;gBAAI,IACzB,qBAAqB,SACrB;oBAAE,OAAO;oBAAG,SAAS;gBAAI,IACzB;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAE7B,YAAY;oBACV,UAAU;oBACV,OAAO,MAAM,QAAQ;oBACrB,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACrC;;;;;;;;;;;;AAIR;IA1IM;;QAS0B,sKAAA,CAAA,YAAS;;;MATnC;uCA4IS", "debugId": null}}, {"offset": {"line": 4707, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/navigation/FAQAccordion.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FAQAccordionProps, FAQItem } from '../../../types/ui';\nimport { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';\n\n/**\n * FAQAccordion Component\n * \n * Create an FAQ accordion with smooth animations and cannabis industry specific content.\n * Features search functionality, categories, and smooth expand/collapse animations.\n * \n * @example\n * ```tsx\n * <FAQAccordion\n *   items={faqItems}\n *   allowMultiple={false}\n *   defaultOpen={['faq-1']}\n *   showCategories={true}\n *   enableSearch={true}\n * />\n * ```\n */\nconst FAQAccordion: React.FC<FAQAccordionProps> = ({\n  items,\n  allowMultiple = false,\n  defaultOpen = [],\n  showCategories = false,\n  enableSearch = false,\n  className = '',\n  style,\n}) => {\n  const [openItems, setOpenItems] = useState<string[]>(defaultOpen);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n\n  // Get unique categories\n  const categories = useMemo(() => {\n    const cats = Array.from(new Set(items.map(item => item.category).filter(Boolean)));\n    return cats as string[];\n  }, [items]);\n\n  // Filter items based on search and category\n  const filteredItems = useMemo(() => {\n    let filtered = items;\n\n    // Apply search filter\n    if (searchTerm) {\n      filtered = filtered.filter(item =>\n        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.answer.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    // Apply category filter\n    if (selectedCategory) {\n      filtered = filtered.filter(item => item.category === selectedCategory);\n    }\n\n    return filtered;\n  }, [items, searchTerm, selectedCategory]);\n\n  // Toggle item open/closed\n  const toggleItem = (itemId: string) => {\n    setOpenItems(prev => {\n      if (allowMultiple) {\n        return prev.includes(itemId)\n          ? prev.filter(id => id !== itemId)\n          : [...prev, itemId];\n      } else {\n        return prev.includes(itemId) ? [] : [itemId];\n      }\n    });\n  };\n\n  // Clear search and filters\n  const clearFilters = () => {\n    setSearchTerm('');\n    setSelectedCategory('');\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: {\n        duration: 0.4,\n        ease: brandAnimations.easing.smooth\n      }\n    }\n  };\n\n  return (\n    <div className={`faq-accordion ${className}`} style={style}>\n      {/* Search and Filter Controls */}\n      {(enableSearch || showCategories) && (\n        <div className=\"mb-8 space-y-4\">\n          {/* Search Bar */}\n          {enableSearch && (\n            <div className=\"relative max-w-md\">\n              <input\n                type=\"text\"\n                placeholder=\"Search FAQs...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <svg className=\"absolute left-3 top-3.5 h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n          )}\n\n          {/* Category Filter */}\n          {showCategories && categories.length > 0 && (\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => setSelectedCategory('')}\n                className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 ${\n                  selectedCategory === ''\n                    ? 'bg-primary-600 text-cream-50 border-primary-600'\n                    : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n                }`}\n              >\n                All Categories\n              </button>\n              {categories.map((category) => (\n                <button\n                  key={category}\n                  onClick={() => setSelectedCategory(category)}\n                  className={`px-4 py-2 text-sm font-medium rounded-full border transition-all duration-200 capitalize ${\n                    selectedCategory === category\n                      ? 'bg-primary-600 text-cream-50 border-primary-600'\n                      : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'\n                  }`}\n                >\n                  {category}\n                </button>\n              ))}\n            </div>\n          )}\n\n          {/* Active Filters */}\n          {(searchTerm || selectedCategory) && (\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex flex-wrap gap-2\">\n                {searchTerm && (\n                  <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700\">\n                    Search: \"{searchTerm}\"\n                  </span>\n                )}\n                {selectedCategory && (\n                  <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700 capitalize\">\n                    Category: {selectedCategory}\n                  </span>\n                )}\n              </div>\n              <button\n                onClick={clearFilters}\n                className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          )}\n\n          {/* Results Count */}\n          <p className=\"text-sm text-gray-600\">\n            Showing {filteredItems.length} of {items.length} questions\n          </p>\n        </div>\n      )}\n\n      {/* FAQ Items */}\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, margin: '-50px' }}\n        className=\"space-y-4\"\n      >\n        <AnimatePresence>\n          {filteredItems.map((item) => (\n            <FAQItemComponent\n              key={item.id}\n              item={item}\n              isOpen={openItems.includes(item.id)}\n              onToggle={() => toggleItem(item.id)}\n              itemVariants={itemVariants}\n            />\n          ))}\n        </AnimatePresence>\n      </motion.div>\n\n      {/* Empty State */}\n      {filteredItems.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No questions found</h3>\n          <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Individual FAQ Item Component\ninterface FAQItemComponentProps {\n  item: FAQItem;\n  isOpen: boolean;\n  onToggle: () => void;\n  itemVariants: any;\n}\n\nconst FAQItemComponent: React.FC<FAQItemComponentProps> = ({\n  item,\n  isOpen,\n  onToggle,\n  itemVariants,\n}) => {\n  return (\n    <motion.div\n      variants={itemVariants}\n      layout\n      className=\"faq-item bg-cream-50 rounded-lg overflow-hidden\"\n      style={{ boxShadow: brandShadows.soft }}\n    >\n      {/* Question */}\n      <motion.button\n        onClick={onToggle}\n        className=\"w-full px-6 py-4 text-left flex items-center justify-between hover:bg-cream-100 transition-colors duration-200\"\n        whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}\n        whileTap={{ scale: 0.99 }}\n      >\n        <span className=\"text-lg font-semibold text-charcoal-800 pr-4\">\n          {item.question}\n        </span>\n        \n        <motion.div\n          animate={{ rotate: isOpen ? 180 : 0 }}\n          transition={{ duration: 0.2, ease: brandAnimations.easing.smooth }}\n          className=\"flex-shrink-0\"\n        >\n          <svg \n            className=\"w-5 h-5 text-primary-600\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M19 9l-7 7-7-7\" \n            />\n          </svg>\n        </motion.div>\n      </motion.button>\n\n      {/* Answer */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            transition={{ \n              duration: 0.3, \n              ease: brandAnimations.easing.smooth \n            }}\n            className=\"overflow-hidden\"\n          >\n            <div className=\"px-6 pb-4\">\n              <div className=\"border-t border-cream-200 pt-4\">\n                <p className=\"text-charcoal-600 leading-relaxed\">\n                  {item.answer}\n                </p>\n                \n                {/* Category Badge */}\n                {item.category && (\n                  <div className=\"mt-3\">\n                    <span className=\"inline-block px-3 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full capitalize\">\n                      {item.category}\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n};\n\nexport default FAQAccordion;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAOA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,gBAAgB,KAAK,EACrB,cAAc,EAAE,EAChB,iBAAiB,KAAK,EACtB,eAAe,KAAK,EACpB,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG;yDAAC,CAAA,OAAQ,KAAK,QAAQ;wDAAE,MAAM,CAAC;YACxE,OAAO;QACT;2CAAG;QAAC;KAAM;IAEV,4CAA4C;IAC5C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC5B,IAAI,WAAW;YAEf,sBAAsB;YACtB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;2DAAC,CAAA,OACzB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAE7D;YAEA,wBAAwB;YACxB,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;2DAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;YACvD;YAEA,OAAO;QACT;8CAAG;QAAC;QAAO;QAAY;KAAiB;IAExC,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA;YACX,IAAI,eAAe;gBACjB,OAAO,KAAK,QAAQ,CAAC,UACjB,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;uBAAI;oBAAM;iBAAO;YACvB,OAAO;gBACL,OAAO,KAAK,QAAQ,CAAC,UAAU,EAAE,GAAG;oBAAC;iBAAO;YAC9C;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe;QACnB,cAAc;QACd,oBAAoB;IACtB;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;QAAE,OAAO;;YAElD,CAAC,gBAAgB,cAAc,mBAC9B,6LAAC;gBAAI,WAAU;;oBAEZ,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;gCAAgD,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACvG,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;oBAM1E,kBAAkB,WAAW,MAAM,GAAG,mBACrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC,8EAA8E,EACxF,qBAAqB,KACjB,oDACA,oEACJ;0CACH;;;;;;4BAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oCAEC,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,yFAAyF,EACnG,qBAAqB,WACjB,oDACA,oEACJ;8CAED;mCARI;;;;;;;;;;;oBAeZ,CAAC,cAAc,gBAAgB,mBAC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,4BACC,6LAAC;wCAAK,WAAU;;4CAA0F;4CAC9F;4CAAW;;;;;;;oCAGxB,kCACC,6LAAC;wCAAK,WAAU;;4CAAqG;4CACxG;;;;;;;;;;;;;0CAIjB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,cAAc,MAAM;4BAAC;4BAAK,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAMtD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAQ;gBACxC,WAAU;0BAEV,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;4BAEC,MAAM;4BACN,QAAQ,UAAU,QAAQ,CAAC,KAAK,EAAE;4BAClC,UAAU,IAAM,WAAW,KAAK,EAAE;4BAClC,cAAc;2BAJT,KAAK,EAAE;;;;;;;;;;;;;;;YAWnB,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GArMM;KAAA;AA+MN,MAAM,mBAAoD,CAAC,EACzD,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,YAAY,EACb;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,MAAM;QACN,WAAU;QACV,OAAO;YAAE,WAAW,yHAAA,CAAA,eAAY,CAAC,IAAI;QAAC;;0BAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,iBAAiB;gBAAsB;gBACrD,UAAU;oBAAE,OAAO;gBAAK;;kCAExB,6LAAC;wBAAK,WAAU;kCACb,KAAK,QAAQ;;;;;;kCAGhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,SAAS,MAAM;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;wBAAC;wBACjE,WAAU;kCAEV,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBAC9B,YAAY;wBACV,UAAU;wBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;oBACrC;oBACA,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,KAAK,MAAM;;;;;;gCAIb,KAAK,QAAQ,kBACZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpC;MA/EM;uCAiFS", "debugId": null}}, {"offset": {"line": 5213, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/navigation/AnnouncementBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { AnnouncementBannerProps } from '../../../types/ui';\nimport { brandColors, brandAnimations } from '../../../styles/brand';\n\n/**\n * AnnouncementBanner Component\n * \n * Build announcement banners for deals, new products, and compliance notices.\n * Features auto-dismiss, sticky positioning, and themed styling for different message types.\n * \n * @example\n * ```tsx\n * <AnnouncementBanner\n *   message=\"🌿 New Premium Rosin Collection Available - 20% Off This Week!\"\n *   type=\"deal\"\n *   dismissible={true}\n *   autoDismiss={10000}\n *   actionText=\"Shop Now\"\n *   onAction={() => router.push('/products/rosin')}\n *   sticky={true}\n * />\n * ```\n */\nconst AnnouncementBanner: React.FC<AnnouncementBannerProps> = ({\n  message,\n  type = 'info',\n  dismissible = true,\n  autoDismiss,\n  actionText,\n  onAction,\n  onClose,\n  sticky = false,\n  className = '',\n  style,\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const [timeLeft, setTimeLeft] = useState<number | null>(null);\n\n  // Auto-dismiss functionality\n  useEffect(() => {\n    if (autoDismiss && autoDismiss > 0) {\n      setTimeLeft(autoDismiss);\n      \n      const interval = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev && prev <= 1000) {\n            handleClose();\n            return null;\n          }\n          return prev ? prev - 1000 : null;\n        });\n      }, 1000);\n\n      return () => clearInterval(interval);\n    }\n  }, [autoDismiss]);\n\n  // Handle close\n  const handleClose = () => {\n    setIsVisible(false);\n    onClose?.();\n  };\n\n  // Get banner styling based on type\n  const getBannerStyling = () => {\n    switch (type) {\n      case 'info':\n        return {\n          backgroundColor: brandColors.primary[600],\n          textColor: 'text-cream-50',\n          iconColor: brandColors.cream[100],\n          actionBg: brandColors.cream[50],\n          actionText: brandColors.primary[600],\n          actionHover: brandColors.cream[100],\n        };\n      case 'warning':\n        return {\n          backgroundColor: '#f59e0b',\n          textColor: 'text-cream-50',\n          iconColor: '#fef3c7',\n          actionBg: brandColors.cream[50],\n          actionText: '#f59e0b',\n          actionHover: '#fef3c7',\n        };\n      case 'success':\n        return {\n          backgroundColor: brandColors.sage[600],\n          textColor: 'text-cream-50',\n          iconColor: brandColors.sage[100],\n          actionBg: brandColors.cream[50],\n          actionText: brandColors.sage[600],\n          actionHover: brandColors.sage[100],\n        };\n      case 'error':\n        return {\n          backgroundColor: '#dc2626',\n          textColor: 'text-cream-50',\n          iconColor: '#fecaca',\n          actionBg: brandColors.cream[50],\n          actionText: '#dc2626',\n          actionHover: '#fecaca',\n        };\n      case 'deal':\n        return {\n          backgroundColor: brandColors.gold[600],\n          textColor: 'text-charcoal-800',\n          iconColor: brandColors.gold[200],\n          actionBg: brandColors.charcoal[800],\n          actionText: brandColors.cream[50],\n          actionHover: brandColors.charcoal[700],\n        };\n      default:\n        return {\n          backgroundColor: brandColors.primary[600],\n          textColor: 'text-cream-50',\n          iconColor: brandColors.cream[100],\n          actionBg: brandColors.cream[50],\n          actionText: brandColors.primary[600],\n          actionHover: brandColors.cream[100],\n        };\n    }\n  };\n\n  const styling = getBannerStyling();\n\n  // Get icon based on type\n  const getIcon = () => {\n    switch (type) {\n      case 'info':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'warning':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'success':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'error':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'deal':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  // Format time left for display\n  const formatTimeLeft = (ms: number) => {\n    const seconds = Math.ceil(ms / 1000);\n    return seconds;\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ y: -100, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          exit={{ y: -100, opacity: 0 }}\n          transition={{ \n            duration: 0.4, \n            ease: brandAnimations.easing.smooth \n          }}\n          className={`announcement-banner ${\n            sticky ? 'fixed top-0 left-0 right-0 z-50' : 'relative'\n          } ${className}`}\n          style={{\n            backgroundColor: styling.backgroundColor,\n            ...style,\n          }}\n        >\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between py-3\">\n              {/* Content */}\n              <div className=\"flex items-center space-x-3 flex-1\">\n                {/* Icon */}\n                <div style={{ color: styling.iconColor }}>\n                  {getIcon()}\n                </div>\n\n                {/* Message */}\n                <p className={`font-medium ${styling.textColor} flex-1`}>\n                  {message}\n                </p>\n\n                {/* Auto-dismiss countdown */}\n                {timeLeft && timeLeft > 0 && (\n                  <div className={`text-sm ${styling.textColor} opacity-75`}>\n                    Auto-close in {formatTimeLeft(timeLeft)}s\n                  </div>\n                )}\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex items-center space-x-3\">\n                {/* Action Button */}\n                {actionText && onAction && (\n                  <motion.button\n                    onClick={onAction}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200\"\n                    style={{\n                      backgroundColor: styling.actionBg,\n                      color: styling.actionText,\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.backgroundColor = styling.actionHover;\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.backgroundColor = styling.actionBg;\n                    }}\n                  >\n                    {actionText}\n                  </motion.button>\n                )}\n\n                {/* Close Button */}\n                {dismissible && (\n                  <motion.button\n                    onClick={handleClose}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    className={`p-1 rounded-md transition-colors duration-200 ${styling.textColor} hover:bg-black hover:bg-opacity-10`}\n                    aria-label=\"Close announcement\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </motion.button>\n                )}\n              </div>\n            </div>\n\n            {/* Progress bar for auto-dismiss */}\n            {autoDismiss && timeLeft && (\n              <motion.div\n                className=\"h-1 bg-black bg-opacity-20\"\n                initial={{ scaleX: 1 }}\n                animate={{ scaleX: 0 }}\n                transition={{ \n                  duration: autoDismiss / 1000, \n                  ease: 'linear' \n                }}\n                style={{ transformOrigin: 'left' }}\n              />\n            )}\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default AnnouncementBanner;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAOA;;;;;;;;;;;;;;;;;;CAkBC,GACD,MAAM,qBAAwD,CAAC,EAC7D,OAAO,EACP,OAAO,MAAM,EACb,cAAc,IAAI,EAClB,WAAW,EACX,UAAU,EACV,QAAQ,EACR,OAAO,EACP,SAAS,KAAK,EACd,YAAY,EAAE,EACd,KAAK,EACN;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExD,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,eAAe,cAAc,GAAG;gBAClC,YAAY;gBAEZ,MAAM,WAAW;6DAAY;wBAC3B;qEAAY,CAAA;gCACV,IAAI,QAAQ,QAAQ,MAAM;oCACxB;oCACA,OAAO;gCACT;gCACA,OAAO,OAAO,OAAO,OAAO;4BAC9B;;oBACF;4DAAG;gBAEH;oDAAO,IAAM,cAAc;;YAC7B;QACF;uCAAG;QAAC;KAAY;IAEhB,eAAe;IACf,MAAM,cAAc;QAClB,aAAa;QACb;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACzC,WAAW;oBACX,WAAW,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;oBACjC,UAAU,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBAC/B,YAAY,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACpC,aAAa,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;gBACrC;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB;oBACjB,WAAW;oBACX,WAAW;oBACX,UAAU,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBAC/B,YAAY;oBACZ,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBACtC,WAAW;oBACX,WAAW,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAChC,UAAU,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBAC/B,YAAY,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBACjC,aAAa,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;gBACpC;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB;oBACjB,WAAW;oBACX,WAAW;oBACX,UAAU,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBAC/B,YAAY;oBACZ,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBACtC,WAAW;oBACX,WAAW,yHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAChC,UAAU,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;oBACnC,YAAY,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBACjC,aAAa,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;gBACxC;YACF;gBACE,OAAO;oBACL,iBAAiB,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACzC,WAAW;oBACX,WAAW,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;oBACjC,UAAU,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;oBAC/B,YAAY,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBACpC,aAAa,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;gBACrC;QACJ;IACF;IAEA,MAAM,UAAU;IAEhB,yBAAyB;IACzB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAmI,UAAS;;;;;;;;;;;YAG7K,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAoN,UAAS;;;;;;;;;;;YAG9P,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAwI,UAAS;;;;;;;;;;;YAGlL,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAA0N,UAAS;;;;;;;;;;;YAGpQ,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAA0c,UAAS;;;;;;;;;;;YAGpf;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK;QAC/B,OAAO;IACT;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,GAAG,CAAC;gBAAK,SAAS;YAAE;YAC/B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,MAAM;gBAAE,GAAG,CAAC;gBAAK,SAAS;YAAE;YAC5B,YAAY;gBACV,UAAU;gBACV,MAAM,yHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;YACA,WAAW,CAAC,oBAAoB,EAC9B,SAAS,oCAAoC,WAC9C,CAAC,EAAE,WAAW;YACf,OAAO;gBACL,iBAAiB,QAAQ,eAAe;gBACxC,GAAG,KAAK;YACV;sBAEA,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,OAAO;4CAAE,OAAO,QAAQ,SAAS;wCAAC;kDACpC;;;;;;kDAIH,6LAAC;wCAAE,WAAW,CAAC,YAAY,EAAE,QAAQ,SAAS,CAAC,OAAO,CAAC;kDACpD;;;;;;oCAIF,YAAY,WAAW,mBACtB,6LAAC;wCAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,CAAC,WAAW,CAAC;;4CAAE;4CAC1C,eAAe;4CAAU;;;;;;;;;;;;;0CAM9C,6LAAC;gCAAI,WAAU;;oCAEZ,cAAc,0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;wCACV,OAAO;4CACL,iBAAiB,QAAQ,QAAQ;4CACjC,OAAO,QAAQ,UAAU;wCAC3B;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,WAAW;wCAC7D;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,QAAQ;wCAC1D;kDAEC;;;;;;oCAKJ,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,WAAW,CAAC,8CAA8C,EAAE,QAAQ,SAAS,CAAC,mCAAmC,CAAC;wCAClH,cAAW;kDAEX,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQlP,eAAe,0BACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAE;wBACrB,SAAS;4BAAE,QAAQ;wBAAE;wBACrB,YAAY;4BACV,UAAU,cAAc;4BACxB,MAAM;wBACR;wBACA,OAAO;4BAAE,iBAAiB;wBAAO;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAtPM;KAAA;uCAwPS", "debugId": null}}, {"offset": {"line": 5639, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/demos/UIComponentsShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport HeroVideoWithCTA from '../hero/HeroVideoWithCTA';\nimport AnimatedCardSlider from '../cards/AnimatedCardSlider';\nimport TabbedProductGrid from '../grids/TabbedProductGrid';\nimport MasonryProductShowcase from '../grids/MasonryProductShowcase';\nimport GlitchTextEffect from '../effects/GlitchTextEffect';\nimport FilterableStrainCards from '../cards/FilterableStrainCards';\nimport ParallaxSection from '../layout/ParallaxSection';\nimport VerticalTimeline from '../layout/VerticalTimeline';\nimport SplitMediaLayout from '../layout/SplitMediaLayout';\nimport FeatureIconsWithMotion from '../effects/FeatureIconsWithMotion';\nimport FAQAccordion from '../navigation/FAQAccordion';\nimport AnnouncementBanner from '../navigation/AnnouncementBanner';\nimport { Product, Strain, TimelineItem, FeatureItem, FAQItem } from '../../../types/ui';\nimport { brandColors } from '../../../styles/brand';\n\n/**\n * UI Components Showcase\n * \n * Comprehensive demo page showcasing all Apothecary Farms UI components\n */\nconst UIComponentsShowcase: React.FC = () => {\n  // Sample data for demos\n  const sampleProducts: Product[] = [\n    {\n      id: '1',\n      name: 'Blue Dream',\n      description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',\n      image: '/api/placeholder/300/400',\n      price: 45,\n      category: 'flower',\n      strain: 'Hybrid',\n      thc: 18,\n      cbd: 1,\n      effects: ['Relaxed', 'Happy', 'Creative'],\n      tags: ['Popular', 'Daytime']\n    },\n    {\n      id: '2',\n      name: 'Live Rosin',\n      description: 'Premium solventless concentrate with full terpene profile.',\n      image: '/api/placeholder/300/300',\n      price: 80,\n      category: 'extract',\n      thc: 75,\n      cbd: 2,\n      effects: ['Euphoric', 'Focused', 'Uplifted'],\n      tags: ['Premium', 'Solventless']\n    },\n    {\n      id: '3',\n      name: 'Gummy Bears',\n      description: 'Delicious fruit-flavored gummies with precise dosing.',\n      image: '/api/placeholder/300/250',\n      price: 25,\n      category: 'edible',\n      thc: 10,\n      effects: ['Relaxed', 'Sleepy', 'Pain Relief'],\n      tags: ['Beginner Friendly', 'Tasty']\n    },\n    {\n      id: '4',\n      name: 'OG Kush',\n      description: 'Classic indica-dominant strain with earthy pine flavors.',\n      image: '/api/placeholder/300/350',\n      price: 50,\n      category: 'flower',\n      strain: 'Indica',\n      thc: 22,\n      cbd: 0.5,\n      effects: ['Relaxed', 'Sleepy', 'Hungry'],\n      tags: ['Classic', 'Evening']\n    },\n    {\n      id: '5',\n      name: 'Shatter',\n      description: 'Glass-like concentrate with high potency and purity.',\n      image: '/api/placeholder/300/280',\n      price: 60,\n      category: 'extract',\n      thc: 85,\n      effects: ['Intense', 'Long-lasting', 'Cerebral'],\n      tags: ['High Potency', 'Experienced']\n    },\n    {\n      id: '6',\n      name: 'Chocolate Bar',\n      description: 'Rich dark chocolate infused with premium cannabis.',\n      image: '/api/placeholder/300/200',\n      price: 35,\n      category: 'edible',\n      thc: 100,\n      effects: ['Euphoric', 'Relaxed', 'Creative'],\n      tags: ['Luxury', 'Microdose']\n    }\n  ];\n\n  const categories = [\n    {\n      id: 'flower',\n      name: 'Flower',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.07 32.91 32.91 0 003.256.508 3.5 3.5 0 006.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.07A11.717 11.717 0 0116 8a6 6 0 00-6-6z\" />\n        </svg>\n      )\n    },\n    {\n      id: 'extract',\n      name: 'Extracts',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M6.5 3c-1.051 0-2.093.04-3.125.117a1 1 0 00-.831.814A6.5 6.5 0 004.5 12H13l2.856-2.856A3.5 3.5 0 0015.5 7a3.5 3.5 0 00-3.5-3.5H6.5z\" />\n        </svg>\n      )\n    },\n    {\n      id: 'edible',\n      name: 'Edibles',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10 2a8 8 0 100 16 8 8 0 000-16zM8 12a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z\" />\n        </svg>\n      )\n    }\n  ];\n\n  const filters = [\n    { id: 'category', name: 'Flower', value: 'flower' },\n    { id: 'category', name: 'Extracts', value: 'extract' },\n    { id: 'category', name: 'Edibles', value: 'edible' },\n    { id: 'price', name: 'Under $25', value: 'under-25' },\n    { id: 'price', name: '$25-$50', value: '25-50' },\n    { id: 'price', name: '$50-$100', value: '50-100' },\n    { id: 'effects', name: 'Relaxed', value: 'Relaxed' },\n    { id: 'effects', name: 'Happy', value: 'Happy' },\n    { id: 'effects', name: 'Creative', value: 'Creative' }\n  ];\n\n  // Sample strains data\n  const sampleStrains: Strain[] = [\n    {\n      id: '1',\n      name: 'Blue Dream',\n      type: 'hybrid',\n      description: 'A balanced hybrid strain with sweet berry aroma and cerebral effects.',\n      image: '/api/placeholder/300/400',\n      thc: 18,\n      cbd: 1,\n      effects: ['Relaxed', 'Happy', 'Creative'],\n      flavors: ['Berry', 'Sweet', 'Earthy'],\n      genetics: 'Blueberry x Haze'\n    },\n    {\n      id: '2',\n      name: 'OG Kush',\n      type: 'indica',\n      description: 'Classic indica-dominant strain with earthy pine flavors.',\n      image: '/api/placeholder/300/350',\n      thc: 22,\n      cbd: 0.5,\n      effects: ['Relaxed', 'Sleepy', 'Hungry'],\n      flavors: ['Pine', 'Earthy', 'Woody'],\n      genetics: 'Chemdawg x Lemon Thai'\n    },\n    {\n      id: '3',\n      name: 'Sour Diesel',\n      type: 'sativa',\n      description: 'Energizing sativa with diesel-like aroma and uplifting effects.',\n      image: '/api/placeholder/300/380',\n      thc: 20,\n      cbd: 1,\n      effects: ['Energetic', 'Creative', 'Uplifted'],\n      flavors: ['Diesel', 'Citrus', 'Pungent'],\n      genetics: 'Chemdawg 91 x Super Skunk'\n    }\n  ];\n\n  // Sample timeline data\n  const timelineItems: TimelineItem[] = [\n    {\n      id: '1',\n      title: 'Seed Selection',\n      description: 'We carefully select premium genetics from trusted breeders worldwide.',\n      date: 'Week 1',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      id: '2',\n      title: 'Germination',\n      description: 'Seeds are germinated in controlled environment with optimal humidity and temperature.',\n      date: 'Week 2',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      id: '3',\n      title: 'Vegetative Growth',\n      description: 'Plants develop strong root systems and healthy foliage under specialized lighting.',\n      date: 'Week 3-8',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      id: '4',\n      title: 'Flowering',\n      description: 'Controlled light cycles trigger flowering, developing potent buds rich in cannabinoids.',\n      date: 'Week 9-16',\n      image: '/api/placeholder/400/300'\n    },\n    {\n      id: '5',\n      title: 'Harvest & Cure',\n      description: 'Careful harvesting and curing process preserves terpenes and maximizes potency.',\n      date: 'Week 17-20',\n      image: '/api/placeholder/400/300'\n    }\n  ];\n\n  // Sample features data\n  const features: FeatureItem[] = [\n    {\n      id: '1',\n      title: 'Lab Tested',\n      description: 'All products undergo rigorous third-party testing for potency and purity.',\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" />\n          <path fillRule=\"evenodd\" d=\"M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6.5A1.5 1.5 0 009.5 13h-3A1.5 1.5 0 005 11.5V5zM7 7a1 1 0 011-1h.01a1 1 0 110 2H8a1 1 0 01-1-1zm1 3a1 1 0 100 2h.01a1 1 0 100-2H8z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '2',\n      title: 'Organic Growing',\n      description: 'Cultivated using sustainable, organic practices without harmful pesticides.',\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '3',\n      title: 'Expert Cultivation',\n      description: 'Grown by master cultivators with decades of cannabis growing experience.',\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      )\n    }\n  ];\n\n  // Sample FAQ data\n  const faqItems: FAQItem[] = [\n    {\n      id: '1',\n      question: 'What is the difference between indica and sativa?',\n      answer: 'Indica strains typically provide relaxing, sedating effects and are often used for evening consumption. Sativa strains tend to be more energizing and uplifting, making them popular for daytime use. Hybrid strains combine characteristics of both.',\n      category: 'strains'\n    },\n    {\n      id: '2',\n      question: 'How should I store my cannabis products?',\n      answer: 'Store cannabis in a cool, dark, dry place away from direct sunlight. Use airtight containers to preserve freshness and potency. Avoid storing in the refrigerator or freezer as this can damage trichomes.',\n      category: 'storage'\n    },\n    {\n      id: '3',\n      question: 'What does lab testing include?',\n      answer: 'Our comprehensive lab testing includes potency analysis (THC, CBD, other cannabinoids), terpene profiles, pesticide screening, heavy metals testing, and microbial analysis to ensure product safety and quality.',\n      category: 'testing'\n    }\n  ];\n\n  return (\n    <div className=\"ui-components-showcase\">\n      {/* Header */}\n      <div className=\"bg-charcoal-800 text-cream-50 py-16\">\n        <div className=\"max-w-6xl mx-auto px-6 text-center\">\n          <GlitchTextEffect\n            text=\"APOTHECARY FARMS\"\n            intensity=\"medium\"\n            trigger=\"continuous\"\n            fontSize=\"4rem\"\n            className=\"mb-4\"\n          />\n          <h2 className=\"text-2xl font-serif mb-4\">UI Components Library</h2>\n          <p className=\"text-xl text-cream-200 max-w-3xl mx-auto\">\n            Production-ready React components for cannabis industry applications\n            featuring modern animations, responsive design, and brand consistency.\n          </p>\n        </div>\n      </div>\n\n      {/* Hero Video Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6 mb-8\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Hero Video with CTA\n          </h3>\n          <p className=\"text-charcoal-600 mb-6\">\n            Immersive video backgrounds with call-to-action overlays and cannabis industry branding.\n          </p>\n        </div>\n        \n        <div className=\"h-96 relative\">\n          <HeroVideoWithCTA\n            videoSrc=\"/api/placeholder/video\"\n            posterSrc=\"/api/placeholder/1920/1080\"\n            headline=\"Premium Cannabis Extracts\"\n            subtitle=\"Award-winning quality, lab-tested purity\"\n            primaryCTA=\"Shop Now\"\n            onPrimaryCTA={() => console.log('Primary CTA clicked')}\n            secondaryCTA=\"Learn More\"\n            onSecondaryCTA={() => console.log('Secondary CTA clicked')}\n            overlayOpacity={0.5}\n          />\n        </div>\n      </section>\n\n      {/* Card Slider Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Animated Card Slider\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Smooth product carousels with auto-advance, touch support, and responsive design.\n          </p>\n          \n          <AnimatedCardSlider\n            products={sampleProducts}\n            visibleCards={3}\n            autoAdvance={5000}\n            touchEnabled={true}\n            showDots={true}\n            showArrows={true}\n            onCardClick={(product) => console.log('Card clicked:', product.name)}\n          />\n        </div>\n      </section>\n\n      {/* Tabbed Product Grid Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Tabbed Product Grid\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Organized product displays with category tabs, pagination, and smooth animations.\n          </p>\n          \n          <TabbedProductGrid\n            products={sampleProducts}\n            categories={categories}\n            columns={3}\n            itemsPerPage={6}\n            showPagination={true}\n            onProductClick={(product) => console.log('Product clicked:', product.name)}\n          />\n        </div>\n      </section>\n\n      {/* Masonry Showcase Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Masonry Product Showcase\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Dynamic masonry layouts with filtering capabilities and responsive design.\n          </p>\n          \n          <MasonryProductShowcase\n            products={sampleProducts}\n            columns={3}\n            gap={20}\n            carouselMode={false}\n            filters={filters}\n            onProductClick={(product) => console.log('Product clicked:', product.name)}\n          />\n        </div>\n      </section>\n\n      {/* Glitch Text Effects Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Glitch Text Effects\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Modern, edgy text effects with customizable intensity and trigger options.\n          </p>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"text-center p-6 bg-charcoal-800 rounded-lg\">\n              <h4 className=\"text-cream-200 mb-4\">Hover Trigger</h4>\n              <GlitchTextEffect\n                text=\"HOVER ME\"\n                intensity=\"medium\"\n                trigger=\"hover\"\n                fontSize=\"1.5rem\"\n                colors={[brandColors.apothecary, '#ff0080', '#00ff80']}\n              />\n            </div>\n            \n            <div className=\"text-center p-6 bg-charcoal-800 rounded-lg\">\n              <h4 className=\"text-cream-200 mb-4\">Continuous</h4>\n              <GlitchTextEffect\n                text=\"ALWAYS ON\"\n                intensity=\"low\"\n                trigger=\"continuous\"\n                fontSize=\"1.5rem\"\n                colors={['#ffd700', '#ff4500', '#00ffff']}\n              />\n            </div>\n            \n            <div className=\"text-center p-6 bg-charcoal-800 rounded-lg\">\n              <h4 className=\"text-cream-200 mb-4\">High Intensity</h4>\n              <GlitchTextEffect\n                text=\"INTENSE\"\n                intensity=\"high\"\n                trigger=\"hover\"\n                fontSize=\"1.5rem\"\n                colors={['#ff0000', '#00ff00', '#0000ff']}\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Filterable Strain Cards Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Filterable Strain Cards\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Advanced strain filtering with search, category filters, and sorting options.\n          </p>\n\n          <FilterableStrainCards\n            strains={sampleStrains}\n            enableSearch={true}\n            filters={[\n              { id: 'type', name: 'Type', options: ['sativa', 'indica', 'hybrid'] },\n              { id: 'thc', name: 'THC Level', options: ['low', 'medium', 'high'] }\n            ]}\n            sortOptions={[\n              { id: 'name', name: 'Name', field: 'name' },\n              { id: 'thc', name: 'THC %', field: 'thc' }\n            ]}\n            cardsPerRow={3}\n            onStrainClick={(strain) => console.log('Strain clicked:', strain.name)}\n          />\n        </div>\n      </section>\n\n      {/* Vertical Timeline Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Vertical Timeline\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Showcase cultivation process, company history, or product journey with animated timeline.\n          </p>\n\n          <VerticalTimeline\n            items={timelineItems}\n            theme=\"cultivation\"\n            alternating={true}\n            showLine={true}\n            animateOnScroll={true}\n          />\n        </div>\n      </section>\n\n      {/* Feature Icons Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Feature Icons with Motion\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Highlight key features with animated icons and smooth scroll triggers.\n          </p>\n\n          <FeatureIconsWithMotion\n            features={features}\n            layout=\"grid\"\n            animationTrigger=\"scroll\"\n            columns={3}\n            iconSize=\"lg\"\n          />\n        </div>\n      </section>\n\n      {/* FAQ Accordion Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            FAQ Accordion\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Interactive FAQ section with search, categories, and smooth animations.\n          </p>\n\n          <FAQAccordion\n            items={faqItems}\n            allowMultiple={false}\n            showCategories={true}\n            enableSearch={true}\n          />\n        </div>\n      </section>\n\n      {/* Announcement Banner Demo */}\n      <section className=\"mb-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-4\">\n            Announcement Banner\n          </h3>\n          <p className=\"text-charcoal-600 mb-8\">\n            Eye-catching banners for deals, announcements, and important notices.\n          </p>\n\n          <div className=\"space-y-4\">\n            <AnnouncementBanner\n              message=\"🌿 New Premium Rosin Collection Available - 20% Off This Week!\"\n              type=\"deal\"\n              dismissible={true}\n              actionText=\"Shop Now\"\n              onAction={() => console.log('Shop now clicked')}\n            />\n\n            <AnnouncementBanner\n              message=\"Lab results now available for all products. View certificates online.\"\n              type=\"info\"\n              dismissible={true}\n              actionText=\"View Results\"\n              onAction={() => console.log('View results clicked')}\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Split Media Layout Demo */}\n      <section className=\"mb-16\">\n        <SplitMediaLayout\n          media={{\n            type: 'image',\n            src: '/api/placeholder/600/400',\n            alt: 'Cannabis extraction process'\n          }}\n          content={{\n            title: 'Our Extraction Process',\n            description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes, ensuring the highest quality concentrates.',\n            features: ['CO2 Extraction', 'Full Spectrum', 'Lab Tested', 'Solvent-Free'],\n            cta: {\n              text: 'Learn More',\n              action: () => console.log('Learn more clicked')\n            }\n          }}\n          mediaPosition=\"left\"\n          splitRatio=\"50-50\"\n          verticalAlign=\"center\"\n        />\n      </section>\n\n      {/* Component Features */}\n      <section className=\"bg-cream-100 py-16\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <h3 className=\"text-3xl font-serif font-bold text-charcoal-800 mb-8 text-center\">\n            Component Features\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <motion.div\n              whileHover={{ y: -5 }}\n              className=\"bg-white p-6 rounded-lg shadow-soft\"\n            >\n              <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-lg font-semibold text-charcoal-800 mb-2\">\n                Cannabis Industry Focused\n              </h4>\n              <p className=\"text-charcoal-600\">\n                Built specifically for cannabis businesses with industry-appropriate styling and terminology.\n              </p>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ y: -5 }}\n              className=\"bg-white p-6 rounded-lg shadow-soft\"\n            >\n              <div className=\"w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-sage-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-lg font-semibold text-charcoal-800 mb-2\">\n                Responsive Design\n              </h4>\n              <p className=\"text-charcoal-600\">\n                Mobile-first approach with seamless adaptation across all device sizes and orientations.\n              </p>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ y: -5 }}\n              className=\"bg-white p-6 rounded-lg shadow-soft\"\n            >\n              <div className=\"w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-gold-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-lg font-semibold text-charcoal-800 mb-2\">\n                Premium Animations\n              </h4>\n              <p className=\"text-charcoal-600\">\n                Smooth Framer Motion animations with performance optimization and accessibility support.\n              </p>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-charcoal-800 text-cream-50 py-8\">\n        <div className=\"max-w-6xl mx-auto px-6 text-center\">\n          <p className=\"text-cream-200\">\n            Built with ❤️ for the cannabis industry using React, Framer Motion, and TailwindCSS\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default UIComponentsShowcase;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAjBA;;;;;;;;;;;;;;;AAmBA;;;;CAIC,GACD,MAAM,uBAAiC;IACrC,wBAAwB;IACxB,MAAM,iBAA4B;QAChC;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAS;aAAW;YACzC,MAAM;gBAAC;gBAAW;aAAU;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAY;gBAAW;aAAW;YAC5C,MAAM;gBAAC;gBAAW;aAAc;QAClC;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAU;aAAc;YAC7C,MAAM;gBAAC;gBAAqB;aAAQ;QACtC;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAU;aAAS;YACxC,MAAM;gBAAC;gBAAW;aAAU;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAgB;aAAW;YAChD,MAAM;gBAAC;gBAAgB;aAAc;QACvC;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;YACL,SAAS;gBAAC;gBAAY;gBAAW;aAAW;YAC5C,MAAM;gBAAC;gBAAU;aAAY;QAC/B;KACD;IAED,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,IAAI;YACJ,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,MAAM,UAAU;QACd;YAAE,IAAI;YAAY,MAAM;YAAU,OAAO;QAAS;QAClD;YAAE,IAAI;YAAY,MAAM;YAAY,OAAO;QAAU;QACrD;YAAE,IAAI;YAAY,MAAM;YAAW,OAAO;QAAS;QACnD;YAAE,IAAI;YAAS,MAAM;YAAa,OAAO;QAAW;QACpD;YAAE,IAAI;YAAS,MAAM;YAAW,OAAO;QAAQ;QAC/C;YAAE,IAAI;YAAS,MAAM;YAAY,OAAO;QAAS;QACjD;YAAE,IAAI;YAAW,MAAM;YAAW,OAAO;QAAU;QACnD;YAAE,IAAI;YAAW,MAAM;YAAS,OAAO;QAAQ;QAC/C;YAAE,IAAI;YAAW,MAAM;YAAY,OAAO;QAAW;KACtD;IAED,sBAAsB;IACtB,MAAM,gBAA0B;QAC9B;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;YACP,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAS;aAAW;YACzC,SAAS;gBAAC;gBAAS;gBAAS;aAAS;YACrC,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;YACP,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAW;gBAAU;aAAS;YACxC,SAAS;gBAAC;gBAAQ;gBAAU;aAAQ;YACpC,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,OAAO;YACP,KAAK;YACL,KAAK;YACL,SAAS;gBAAC;gBAAa;gBAAY;aAAW;YAC9C,SAAS;gBAAC;gBAAU;gBAAU;aAAU;YACxC,UAAU;QACZ;KACD;IAED,uBAAuB;IACvB,MAAM,gBAAgC;QACpC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,uBAAuB;IACvB,MAAM,WAA0B;QAC9B;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;;kCAC/B,6LAAC;wBAAK,GAAE;;;;;;kCACR,6LAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAkL,UAAS;;;;;;;;;;;;QAG5N;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAyL,UAAS;;;;;;;;;;;QAGnO;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,kBAAkB;IAClB,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0JAAA,CAAA,UAAgB;4BACf,MAAK;4BACL,WAAU;4BACV,SAAQ;4BACR,UAAS;4BACT,WAAU;;;;;;sCAEZ,6LAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;;;;;;0BAQ5D,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uJAAA,CAAA,UAAgB;4BACf,UAAS;4BACT,WAAU;4BACV,UAAS;4BACT,UAAS;4BACT,YAAW;4BACX,cAAc,IAAM,QAAQ,GAAG,CAAC;4BAChC,cAAa;4BACb,gBAAgB,IAAM,QAAQ,GAAG,CAAC;4BAClC,gBAAgB;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,0JAAA,CAAA,UAAkB;4BACjB,UAAU;4BACV,cAAc;4BACd,aAAa;4BACb,cAAc;4BACd,UAAU;4BACV,YAAY;4BACZ,aAAa,CAAC,UAAY,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,yJAAA,CAAA,UAAiB;4BAChB,UAAU;4BACV,YAAY;4BACZ,SAAS;4BACT,cAAc;4BACd,gBAAgB;4BAChB,gBAAgB,CAAC,UAAY,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,IAAI;;;;;;;;;;;;;;;;;0BAM/E,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,8JAAA,CAAA,UAAsB;4BACrB,UAAU;4BACV,SAAS;4BACT,KAAK;4BACL,cAAc;4BACd,SAAS;4BACT,gBAAgB,CAAC,UAAY,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,IAAI;;;;;;;;;;;;;;;;;0BAM/E,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC,0JAAA,CAAA,UAAgB;4CACf,MAAK;4CACL,WAAU;4CACV,SAAQ;4CACR,UAAS;4CACT,QAAQ;gDAAC,yHAAA,CAAA,cAAW,CAAC,UAAU;gDAAE;gDAAW;6CAAU;;;;;;;;;;;;8CAI1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC,0JAAA,CAAA,UAAgB;4CACf,MAAK;4CACL,WAAU;4CACV,SAAQ;4CACR,UAAS;4CACT,QAAQ;gDAAC;gDAAW;gDAAW;6CAAU;;;;;;;;;;;;8CAI7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC,0JAAA,CAAA,UAAgB;4CACf,MAAK;4CACL,WAAU;4CACV,SAAQ;4CACR,UAAS;4CACT,QAAQ;gDAAC;gDAAW;gDAAW;6CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,6JAAA,CAAA,UAAqB;4BACpB,SAAS;4BACT,cAAc;4BACd,SAAS;gCACP;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,SAAS;wCAAC;wCAAU;wCAAU;qCAAS;gCAAC;gCACpE;oCAAE,IAAI;oCAAO,MAAM;oCAAa,SAAS;wCAAC;wCAAO;wCAAU;qCAAO;gCAAC;6BACpE;4BACD,aAAa;gCACX;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,OAAO;gCAAO;gCAC1C;oCAAE,IAAI;oCAAO,MAAM;oCAAS,OAAO;gCAAM;6BAC1C;4BACD,aAAa;4BACb,eAAe,CAAC,SAAW,QAAQ,GAAG,CAAC,mBAAmB,OAAO,IAAI;;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,yJAAA,CAAA,UAAgB;4BACf,OAAO;4BACP,OAAM;4BACN,aAAa;4BACb,UAAU;4BACV,iBAAiB;;;;;;;;;;;;;;;;;0BAMvB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,gKAAA,CAAA,UAAsB;4BACrB,UAAU;4BACV,QAAO;4BACP,kBAAiB;4BACjB,SAAS;4BACT,UAAS;;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC,yJAAA,CAAA,UAAY;4BACX,OAAO;4BACP,eAAe;4BACf,gBAAgB;4BAChB,cAAc;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;sCAItC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAkB;oCACjB,SAAQ;oCACR,MAAK;oCACL,aAAa;oCACb,YAAW;oCACX,UAAU,IAAM,QAAQ,GAAG,CAAC;;;;;;8CAG9B,6LAAC,+JAAA,CAAA,UAAkB;oCACjB,SAAQ;oCACR,MAAK;oCACL,aAAa;oCACb,YAAW;oCACX,UAAU,IAAM,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,yJAAA,CAAA,UAAgB;oBACf,OAAO;wBACL,MAAM;wBACN,KAAK;wBACL,KAAK;oBACP;oBACA,SAAS;wBACP,OAAO;wBACP,aAAa;wBACb,UAAU;4BAAC;4BAAkB;4BAAiB;4BAAc;yBAAe;wBAC3E,KAAK;4BACH,MAAM;4BACN,QAAQ,IAAM,QAAQ,GAAG,CAAC;wBAC5B;oBACF;oBACA,eAAc;oBACd,YAAW;oBACX,eAAc;;;;;;;;;;;0BAKlB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAIjF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAe,SAAQ;0DACpE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;sDAAoB;;;;;;;;;;;;8CAKnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;sDAAoB;;;;;;;;;;;;8CAKnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;sDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;;;;;AAOxC;KA9mBM;uCAgnBS", "debugId": null}}]}