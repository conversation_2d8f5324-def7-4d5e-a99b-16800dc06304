/*! For license information please see tsparticles.interaction.particles.collisions.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],o);else{var i="object"==typeof exports?o(require("@tsparticles/engine")):o(e.window);for(var t in i)("object"==typeof exports?exports:e)[t]=i[t]}}(this,(e=>(()=>{var o={303:o=>{o.exports=e}},i={};function t(e){var s=i[e];if(void 0!==s)return s.exports;var n=i[e]={exports:{}};return o[e](n,n.exports,t),n.exports}t.d=(e,o)=>{for(var i in o)t.o(o,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:o[i]})},t.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};t.r(s),t.d(s,{loadParticlesCollisionsInteraction:()=>v});var n=t(303);const r=.5,a=10,c=0;function l(e,o,i,t,s,l){const d=(0,n.clamp)(e.options.collisions.absorb.speed*s.factor/a,c,t);e.size.value+=d*r,i.size.value-=d,t<=l&&(i.size.value=0,i.destroy())}const d=e=>{void 0===e.collisionMaxSpeed&&(e.collisionMaxSpeed=(0,n.getRangeValue)(e.options.collisions.maxSpeed)),e.velocity.length>e.collisionMaxSpeed&&(e.velocity.length=e.collisionMaxSpeed)};function u(e,o){(0,n.circleBounce)((0,n.circleBounceDataFromParticle)(e),(0,n.circleBounceDataFromParticle)(o)),d(e),d(o)}function p(e,o,i,t){switch(e.options.collisions.mode){case n.CollisionMode.absorb:!function(e,o,i,t){const s=e.getRadius(),n=o.getRadius();void 0===s&&void 0!==n?e.destroy():void 0!==s&&void 0===n?o.destroy():void 0!==s&&void 0!==n&&(s>=n?l(e,0,o,n,i,t):l(o,0,e,s,i,t))}(e,o,i,t);break;case n.CollisionMode.bounce:u(e,o);break;case n.CollisionMode.destroy:!function(e,o){e.unbreakable||o.unbreakable||u(e,o),void 0===e.getRadius()&&void 0!==o.getRadius()?e.destroy():void 0!==e.getRadius()&&void 0===o.getRadius()?o.destroy():void 0!==e.getRadius()&&void 0!==o.getRadius()&&(e.getRadius()>=o.getRadius()?o:e).destroy()}(e,o)}}class f extends n.ParticlesInteractorBase{constructor(e){super(e)}clear(){}init(){}interact(e,o){if(e.destroyed||e.spawning)return;const i=this.container,t=e.getPosition(),s=e.getRadius(),r=i.particles.quadTree.queryCircle(t,2*s);for(const a of r){if(e===a||!a.options.collisions.enable||e.options.collisions.mode!==a.options.collisions.mode||a.destroyed||a.spawning)continue;const r=a.getPosition(),c=a.getRadius();if(Math.abs(Math.round(t.z)-Math.round(r.z))>s+c)continue;(0,n.getDistance)(t,r)>s+c||p(e,a,o,i.retina.pixelRatio)}}isEnabled(e){return e.options.collisions.enable}reset(){}}async function v(e,o=!0){e.checkVersion("3.8.1"),await e.addInteractor("particlesCollisions",(e=>Promise.resolve(new f(e))),o)}return s})()));