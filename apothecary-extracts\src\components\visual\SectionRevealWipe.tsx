'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { SectionRevealWipeProps } from '../../types/visual';
import { brandGradients, brandAnimations } from '../../styles/brand';

/**
 * SectionRevealWipe Component
 * 
 * Delivers editorial polish to major page sections with a "curtain wipe" effect.
 * Features side-entrance animations with clip-path and gradient backgrounds.
 * 
 * @example
 * ```tsx
 * <SectionRevealWipe 
 *   direction="left-to-right" 
 *   gradientTheme="cannabis"
 *   addEdgeAccents={true}
 * >
 *   <h2>Hero Section Content</h2>
 * </SectionRevealWipe>
 * ```
 */
const SectionRevealWipe: React.FC<SectionRevealWipeProps> = ({
  children,
  className = '',
  style,
  direction = 'left-to-right',
  duration = 800,
  delay = 0,
  gradientTheme = 'cannabis',
  addEdgeAccents = true,
  trigger = 'scroll',
}) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [isRevealed, setIsRevealed] = useState(trigger === 'load');

  useEffect(() => {
    if (trigger === 'scroll' && inView) {
      setTimeout(() => setIsRevealed(true), delay);
    } else if (trigger === 'load') {
      setTimeout(() => setIsRevealed(true), delay);
    }
  }, [inView, trigger, delay]);

  // Get clip-path values based on direction
  const getClipPath = (revealed: boolean) => {
    if (!revealed) {
      switch (direction) {
        case 'left-to-right':
          return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';
        case 'right-to-left':
          return 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)';
        case 'top-to-bottom':
          return 'polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)';
        case 'bottom-to-top':
          return 'polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)';
        default:
          return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';
      }
    }
    return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';
  };

  // Get background gradient
  const getGradient = () => {
    switch (gradientTheme) {
      case 'cannabis':
        return brandGradients.cannabis;
      case 'gold':
        return brandGradients.gold;
      case 'sage':
        return brandGradients.sage;
      case 'hero':
        return brandGradients.hero;
      default:
        return brandGradients.cannabis;
    }
  };

  return (
    <div
      ref={ref}
      className={`section-reveal-wipe relative overflow-hidden ${className}`}
      style={style}
    >
      {/* Background with gradient */}
      <motion.div
        className="absolute inset-0 z-0"
        style={{
          background: getGradient(),
          clipPath: getClipPath(isRevealed),
        }}
        animate={{
          clipPath: getClipPath(isRevealed),
        }}
        transition={{
          duration: duration / 1000,
          ease: brandAnimations.easing.smooth,
        }}
      />

      {/* Edge accents */}
      {addEdgeAccents && (
        <>
          <motion.div
            className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: isRevealed ? 1 : 0 }}
            transition={{
              duration: duration / 1000,
              delay: (delay + duration * 0.5) / 1000,
              ease: brandAnimations.easing.smooth,
            }}
          />
          <motion.div
            className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: isRevealed ? 1 : 0 }}
            transition={{
              duration: duration / 1000,
              delay: (delay + duration * 0.5) / 1000,
              ease: brandAnimations.easing.smooth,
            }}
          />
        </>
      )}

      {/* Content */}
      <motion.div
        className="relative z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: isRevealed ? 1 : 0 }}
        transition={{
          duration: (duration * 0.6) / 1000,
          delay: (delay + duration * 0.4) / 1000,
          ease: brandAnimations.easing.smooth,
        }}
      >
        {children}
      </motion.div>
    </div>
  );
};

export default SectionRevealWipe;
