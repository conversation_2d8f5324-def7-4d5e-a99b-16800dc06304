// Legacy Components (keeping for backward compatibility)
export { default as TextWithGlitchEffect } from './TextWithGlitchEffect';
export { default as AnnouncementBar } from './AnnouncementBar';
export { default as FeatureIcons, CannabisIcons, ExampleFeatureIcons } from './FeatureIcons';
export { default as Tabs, ExampleTabbedGrid } from './Tabs';
export { default as GridCards, ExampleGridCards } from './GridCards';
export { default as SplitMediaText, ExampleSplitMediaText } from './SplitMediaText';
export { default as FAQAccordion, ExampleFAQAccordion } from './FAQAccordion';
export { default as CardSlider, ExampleCardSlider } from './CardSlider';
export { default as FilterableStrainCards } from './FilterableStrainCards';
export { default as MasonryGrid, ExampleMasonryGrid } from './MasonryGrid';
export { default as CarouselWithThumbnails } from './CarouselWithThumbnails';
export { default as VerticalTimeline, ExampleVerticalTimeline } from './VerticalTimeline';
export { default as HeroVideoLoop, ExampleHeroVideoLoop } from './HeroVideoLoop';
export { default as ParallaxBanner, ScrollFadeIn, ExampleParallaxBanner, ExampleScrollFadeIn, ParallaxSection } from './ParallaxBanner';

// New Enhanced UI Components
// Hero Components
export { default as HeroVideoWithCTA } from './hero/HeroVideoWithCTA';

// Card Components
export { default as AnimatedCardSlider } from './cards/AnimatedCardSlider';

// Grid Components
export { default as TabbedProductGrid } from './grids/TabbedProductGrid';
export { default as MasonryProductShowcase } from './grids/MasonryProductShowcase';

// Effect Components
export { default as GlitchTextEffect } from './effects/GlitchTextEffect';

// Demo Components
export { default as UIComponentsShowcase } from './demos/UIComponentsShowcase';

// Type definitions for common interfaces
export interface BaseComponent {
  className?: string;
}

export interface AnimatedComponent extends BaseComponent {
  animate?: boolean;
  animationDelay?: number;
}

export interface CTAButton {
  text: string;
  href?: string;
  action?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
}

export interface MediaItem {
  id: string;
  title: string;
  description?: string;
  image?: string;
  category?: string;
}
