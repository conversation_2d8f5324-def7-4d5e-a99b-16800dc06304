'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useMemo } from 'react';
import Image from 'next/image';
import { TabbedProductGridProps, Product } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * TabbedProductGrid Component
 * 
 * A tabbed grid system for organizing products by categories (flower, extracts, edibles).
 * Features smooth animations, pagination, and responsive design.
 * 
 * @example
 * ```tsx
 * <TabbedProductGrid
 *   products={products}
 *   categories={[
 *     { id: 'flower', name: 'Flower', icon: <FlowerIcon /> },
 *     { id: 'extract', name: 'Extracts', icon: <ExtractIcon /> }
 *   ]}
 *   columns={3}
 *   itemsPerPage={9}
 *   onProductClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */
const TabbedProductGrid: React.FC<TabbedProductGridProps> = ({
  products,
  categories,
  defaultCategory,
  columns = 3,
  itemsPerPage = 9,
  showPagination = true,
  onProductClick,
  className = '',
  style,
}) => {
  const [activeCategory, setActiveCategory] = useState(
    defaultCategory || categories[0]?.id || 'all'
  );
  const [currentPage, setCurrentPage] = useState(1);

  // Filter products by active category
  const filteredProducts = useMemo(() => {
    if (activeCategory === 'all') return products;
    return products.filter(product => product.category === activeCategory);
  }, [products, activeCategory]);

  // Paginate products
  const paginatedProducts = useMemo(() => {
    if (!showPagination) return filteredProducts;
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredProducts.slice(startIndex, endIndex);
  }, [filteredProducts, currentPage, itemsPerPage, showPagination]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Reset to first page when category changes
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.icon || (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 2L3 7v11h14V7l-7-5z" />
      </svg>
    );
  };

  const tabVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  const gridVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: brandAnimations.easing.smooth
      }
    },
    hover: { y: -8, scale: 1.02 }
  };

  return (
    <div className={`tabbed-product-grid ${className}`} style={style}>
      {/* Tab Navigation */}
      <div className="mb-8">
        <nav className="flex flex-wrap gap-2 justify-center">
          {/* All Products Tab */}
          <motion.button
            variants={tabVariants}
            initial="hidden"
            animate="visible"
            onClick={() => handleCategoryChange('all')}
            className={`px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${
              activeCategory === 'all'
                ? 'bg-primary-600 text-cream-50 border-primary-600'
                : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
            }`}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
            All Products
            <span className="bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full">
              {products.length}
            </span>
          </motion.button>

          {/* Category Tabs */}
          {categories.map((category) => {
            const categoryProducts = products.filter(p => p.category === category.id);
            return (
              <motion.button
                key={category.id}
                variants={tabVariants}
                initial="hidden"
                animate="visible"
                onClick={() => handleCategoryChange(category.id)}
                className={`px-6 py-3 font-medium text-sm rounded-full border transition-all duration-200 flex items-center gap-2 ${
                  activeCategory === category.id
                    ? 'bg-primary-600 text-cream-50 border-primary-600'
                    : 'border-cream-300 hover:border-primary-300 hover:text-primary-600'
                }`}
              >
                {category.icon}
                {category.name}
                <span className="bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full">
                  {categoryProducts.length}
                </span>
              </motion.button>
            );
          })}
        </nav>
      </div>

      {/* Product Grid */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeCategory}
          variants={gridVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className={`grid gap-6 ${
            columns === 1 ? 'grid-cols-1' :
            columns === 2 ? 'grid-cols-1 md:grid-cols-2' :
            columns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
            columns === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' :
            'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
          }`}
        >
          {paginatedProducts.map((product) => (
            <motion.div
              key={product.id}
              variants={cardVariants}
              whileHover="hover"
              className="cursor-pointer"
              onClick={() => onProductClick?.(product)}
            >
              <div 
                className="bg-cream-50 rounded-xl overflow-hidden group"
                style={{ boxShadow: brandShadows.soft }}
              >
                {/* Product Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-sage-100">
                  {product.image ? (
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center">
                        {getCategoryIcon(product.category)}
                      </div>
                    </div>
                  )}

                  {/* Category Badge */}
                  <div className="absolute top-3 right-3">
                    <span 
                      className="text-xs font-semibold px-2 py-1 rounded-full text-cream-50 capitalize"
                      style={{ backgroundColor: brandColors.primary[600] }}
                    >
                      {product.category}
                    </span>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {product.name}
                  </h3>

                  {product.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {product.description}
                    </p>
                  )}

                  {/* Product Details */}
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold text-primary-600">
                      {formatPrice(product.price)}
                    </span>
                    
                    {product.strain && (
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {product.strain}
                      </span>
                    )}
                  </div>

                  {/* THC/CBD Info */}
                  {(product.thc || product.cbd) && (
                    <div className="flex gap-2 mb-4">
                      {product.thc && (
                        <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                          THC: {product.thc}%
                        </span>
                      )}
                      {product.cbd && (
                        <span className="text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded">
                          CBD: {product.cbd}%
                        </span>
                      )}
                    </div>
                  )}

                  {/* Effects Tags */}
                  {product.effects && product.effects.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {product.effects.slice(0, 3).map((effect, idx) => (
                        <span 
                          key={idx}
                          className="text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded"
                        >
                          {effect}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-2 rounded-md border transition-colors ${
                page === currentPage
                  ? 'bg-primary-600 text-cream-50 border-primary-600'
                  : 'border-cream-300 hover:border-primary-300'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-2 rounded-md border border-cream-300 disabled:opacity-50 disabled:cursor-not-allowed hover:border-primary-300 transition-colors"
          >
            Next
          </button>
        </div>
      )}

      {/* Empty State */}
      {paginatedProducts.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-500">Try selecting a different category.</p>
        </div>
      )}
    </div>
  );
};

export default TabbedProductGrid;
