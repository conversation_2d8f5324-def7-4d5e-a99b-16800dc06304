'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface CarouselItem {
  id: string;
  title: string;
  description?: string;
  image: string;
  thumbnail?: string;
  category?: string;
  badge?: string;
  cta?: {
    text: string;
    action: () => void;
  };
}

interface CarouselWithThumbnailsProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showThumbnails?: boolean;
  showDots?: boolean;
  showArrows?: boolean;
  className?: string;
}

export default function CarouselWithThumbnails({
  items,
  autoPlay = true,
  autoPlayInterval = 5000,
  showThumbnails = true,
  showDots = true,
  showArrows = true,
  className = ''
}: CarouselWithThumbnailsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  // Auto-play functionality
  useEffect(() => {
    if (autoPlay && !isHovered && items.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % items.length);
      }, autoPlayInterval);

      return () => clearInterval(interval);
    }
  }, [autoPlay, isHovered, autoPlayInterval, items.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const nextSlide = () => {
    setCurrentIndex(prev => (prev + 1) % items.length);
  };

  const prevSlide = () => {
    setCurrentIndex(prev => (prev - 1 + items.length) % items.length);
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.8
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
      scale: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.8
    })
  };

  const thumbnailVariants = {
    inactive: {
      scale: 0.8,
      opacity: 0.6,
      filter: 'grayscale(100%)'
    },
    active: {
      scale: 1,
      opacity: 1,
      filter: 'grayscale(0%)'
    }
  };

  const currentItem = items[currentIndex];

  return (
    <div
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main Carousel */}
      <div className="relative h-96 md:h-[500px] lg:h-[600px] overflow-hidden rounded-2xl shadow-large">
        <AnimatePresence mode="wait" custom={1}>
          <motion.div
            key={currentIndex}
            custom={1}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: 'spring', stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
              scale: { duration: 0.4 }
            }}
            className="absolute inset-0"
          >
            {/* Background Image */}
            <div className="relative w-full h-full">
              <Image
                src={currentItem.image}
                alt={currentItem.title}
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              />

              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

              {/* Content Overlay */}
              <div className="absolute inset-0 flex items-end">
                <div className="p-8 md:p-12 text-white max-w-2xl">
                  {/* Category Badge */}
                  {currentItem.category && (
                    <motion.span
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="inline-block bg-primary-600 text-cream-50 text-sm font-semibold px-3 py-1 rounded-full mb-4"
                    >
                      {currentItem.category}
                    </motion.span>
                  )}

                  {/* Title */}
                  <motion.h2
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold mb-4 leading-tight"
                  >
                    {currentItem.title}
                  </motion.h2>

                  {/* Description */}
                  {currentItem.description && (
                    <motion.p
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-lg md:text-xl text-gray-200 mb-6 leading-relaxed"
                    >
                      {currentItem.description}
                    </motion.p>
                  )}

                  {/* CTA Button */}
                  {currentItem.cta && (
                    <motion.button
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={currentItem.cta.action}
                      className="bg-gold-500 text-primary-800 px-8 py-3 rounded-lg font-semibold hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-black"
                    >
                      {currentItem.cta.text}
                    </motion.button>
                  )}
                </div>
              </div>

              {/* Badge */}
              {currentItem.badge && (
                <div className="absolute top-6 right-6">
                  <span className="bg-gold-500 text-primary-800 text-sm font-semibold px-3 py-1 rounded-full">
                    {currentItem.badge}
                  </span>
                </div>
              )}
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        {showArrows && items.length > 1 && (
          <>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={prevSlide}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200 z-10"
              aria-label="Previous slide"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={nextSlide}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200 z-10"
              aria-label="Next slide"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </>
        )}

        {/* Progress Bar */}
        {autoPlay && !isHovered && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30">
            <motion.div
              className="h-full bg-gold-500"
              initial={{ width: '0%' }}
              animate={{ width: '100%' }}
              transition={{
                duration: autoPlayInterval / 1000,
                ease: 'linear',
                repeat: Infinity
              }}
            />
          </div>
        )}
      </div>

      {/* Thumbnails */}
      {showThumbnails && items.length > 1 && (
        <div className="flex justify-center mt-6 space-x-3 overflow-x-auto pb-2">
          {items.map((item, index) => (
            <motion.button
              key={item.id}
              variants={thumbnailVariants}
              animate={index === currentIndex ? 'active' : 'inactive'}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => goToSlide(index)}
              className="relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 border-transparent focus:outline-none focus:border-primary-500 transition-all duration-200"
            >
              <Image
                src={item.thumbnail || item.image}
                alt={item.title}
                fill
                className="object-cover"
                sizes="80px"
              />
              
              {/* Active indicator */}
              {index === currentIndex && (
                <motion.div
                  layoutId="activeThumb"
                  className="absolute inset-0 border-2 border-gold-500 rounded-lg"
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          ))}
        </div>
      )}

      {/* Dots Indicator */}
      {showDots && !showThumbnails && items.length > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          {items.map((_, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.8 }}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentIndex
                  ? 'bg-primary-600'
                  : 'bg-cream-300 hover:bg-primary-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
