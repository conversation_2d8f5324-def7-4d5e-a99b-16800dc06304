'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { ParticleCanvasBackgroundProps } from '../../types/visual';
import { terpeneColors } from '../../styles/brand';

/**
 * ParticleCanvasBackground Component
 * 
 * Creates immersive, responsive backgrounds for hero sections.
 * Features floating, interactive particles that react to cursor proximity.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <ParticleCanvasBackground 
 *     particleCount={100}
 *     colors={['#fff9c4', '#d4edda', '#e2d5f1']}
 *     interactive={true}
 *   />
 *   <h1>Hero Content</h1>
 * </div>
 * ```
 */
const ParticleCanvasBackground: React.FC<ParticleCanvasBackgroundProps> = ({
  className = '',
  style,
  particleCount = 80,
  colors = [terpeneColors.particles.yellow, terpeneColors.particles.lime, terpeneColors.particles.lavender],
  sizeRange = [1, 3],
  speed = 0.5,
  connectionDistance = 100,
  interactive = true,
  repulsionDistance = 100,
  attractionStrength = 0.02,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const mouseRef = useRef({ x: 0, y: 0 });
  const particlesRef = useRef<Particle[]>([]);

  // Particle class
  class Particle {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    color: string;
    originalX: number;
    originalY: number;

    constructor(width: number, height: number) {
      this.x = Math.random() * width;
      this.y = Math.random() * height;
      this.originalX = this.x;
      this.originalY = this.y;
      this.vx = (Math.random() - 0.5) * speed;
      this.vy = (Math.random() - 0.5) * speed;
      this.size = Math.random() * (sizeRange[1] - sizeRange[0]) + sizeRange[0];
      this.color = colors[Math.floor(Math.random() * colors.length)];
    }

    update(width: number, height: number, mouseX: number, mouseY: number) {
      // Calculate distance to mouse
      const dx = mouseX - this.x;
      const dy = mouseY - this.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (interactive && distance < repulsionDistance) {
        // Repulsion from mouse
        const force = (repulsionDistance - distance) / repulsionDistance;
        this.vx -= (dx / distance) * force * attractionStrength;
        this.vy -= (dy / distance) * force * attractionStrength;
      }

      // Update position
      this.x += this.vx;
      this.y += this.vy;

      // Boundary collision
      if (this.x < 0 || this.x > width) this.vx *= -1;
      if (this.y < 0 || this.y > height) this.vy *= -1;

      // Keep particles within bounds
      this.x = Math.max(0, Math.min(width, this.x));
      this.y = Math.max(0, Math.min(height, this.y));

      // Apply friction
      this.vx *= 0.99;
      this.vy *= 0.99;
    }

    draw(ctx: CanvasRenderingContext2D) {
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fillStyle = this.color;
      ctx.fill();
    }
  }

  // Initialize particles
  const initParticles = useCallback((width: number, height: number) => {
    particlesRef.current = [];
    for (let i = 0; i < particleCount; i++) {
      particlesRef.current.push(new Particle(width, height));
    }
  }, [particleCount]);

  // Draw connections between nearby particles
  const drawConnections = (ctx: CanvasRenderingContext2D) => {
    const particles = particlesRef.current;
    
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < connectionDistance) {
          const opacity = 1 - (distance / connectionDistance);
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.2})`;
          ctx.lineWidth = 0.5;
          ctx.stroke();
        }
      }
    }
  };

  // Animation loop
  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update and draw particles
    particlesRef.current.forEach(particle => {
      particle.update(canvas.width, canvas.height, mouseRef.current.x, mouseRef.current.y);
      particle.draw(ctx);
    });

    // Draw connections
    drawConnections(ctx);

    animationRef.current = requestAnimationFrame(animate);
  }, []);

  // Handle mouse movement
  const handleMouseMove = useCallback((e: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    mouseRef.current.x = e.clientX - rect.left;
    mouseRef.current.y = e.clientY - rect.top;
  }, []);

  // Setup canvas and start animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initParticles(canvas.width, canvas.height);
    };

    resizeCanvas();
    animate();

    // Event listeners
    window.addEventListener('resize', resizeCanvas);
    if (interactive) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    // Cleanup
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [animate, handleMouseMove, initParticles, interactive]);

  return (
    <canvas
      ref={canvasRef}
      className={`particle-canvas-background absolute inset-0 pointer-events-none ${className}`}
      style={{
        zIndex: 0,
        ...style,
      }}
    />
  );
};

export default ParticleCanvasBackground;
