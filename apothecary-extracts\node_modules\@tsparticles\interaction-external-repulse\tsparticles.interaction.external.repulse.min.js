/*! For license information please see tsparticles.interaction.external.repulse.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var i="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var s in i)("object"==typeof exports?exports:e)[s]=i[s]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},i={};function s(e){var o=i[e];if(void 0!==o)return o.exports;var n=i[e]={exports:{}};return t[e](n,n.exports,s),n.exports}s.d=(e,t)=>{for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};s.r(o),s.d(o,{Repulse:()=>a,RepulseBase:()=>r,RepulseDiv:()=>c,loadExternalRepulseInteraction:()=>d});var n=s(303);class r{constructor(){this.distance=200,this.duration=.4,this.factor=100,this.speed=1,this.maxSpeed=50,this.easing=n.EasingType.easeOutQuad}load(e){(0,n.isNull)(e)||(void 0!==e.distance&&(this.distance=e.distance),void 0!==e.duration&&(this.duration=e.duration),void 0!==e.easing&&(this.easing=e.easing),void 0!==e.factor&&(this.factor=e.factor),void 0!==e.speed&&(this.speed=e.speed),void 0!==e.maxSpeed&&(this.maxSpeed=e.maxSpeed))}}class c extends r{constructor(){super(),this.selectors=[]}load(e){super.load(e),(0,n.isNull)(e)||void 0!==e.selectors&&(this.selectors=e.selectors)}}class a extends r{load(e){super.load(e),(0,n.isNull)(e)||(this.divs=(0,n.executeOnSingleOrMultiple)(e.divs,(e=>{const t=new c;return t.load(e),t})))}}const l="repulse";class p extends n.ExternalInteractorBase{constructor(e,t){super(t),this._clickRepulse=()=>{const e=this.container,t=e.actualOptions.interactivity.modes.repulse;if(!t)return;const i=e.repulse??{particles:[]};if(i.finish||(i.count||(i.count=0),i.count++,i.count===e.particles.count&&(i.finish=!0)),i.clicking){const s=e.retina.repulseModeDistance;if(!s||s<0)return;const o=Math.pow(s/6,3),r=e.interactivity.mouse.clickPosition;if(void 0===r)return;const c=new n.Circle(r.x,r.y,o),a=e.particles.quadTree.query(c,(e=>this.isEnabled(e)));for(const e of a){const{dx:s,dy:c,distance:a}=(0,n.getDistances)(r,e.position),l=a**2,p=-o*t.speed/l;if(l<=o){i.particles.push(e);const t=n.Vector.create(s,c);t.length=p,e.velocity.setTo(t)}}}else if(!1===i.clicking){for(const e of i.particles)e.velocity.setTo(e.initialVelocity);i.particles=[]}},this._hoverRepulse=()=>{const e=this.container,t=e.interactivity.mouse.position,i=e.retina.repulseModeDistance;!i||i<0||!t||this._processRepulse(t,i,new n.Circle(t.x,t.y,i))},this._processRepulse=(e,t,i,s)=>{const o=this.container,r=o.particles.quadTree.query(i,(e=>this.isEnabled(e))),c=o.actualOptions.interactivity.modes.repulse;if(!c)return;const{easing:a,speed:l,factor:p,maxSpeed:d}=c,u=this._engine.getEasing(a),f=(s?.speed??l)*p;for(const i of r){const{dx:s,dy:o,distance:r}=(0,n.getDistances)(i.position,e),c=(0,n.clamp)(u(1-r/t)*f,0,d),a=n.Vector.create(r?s/r*c:f,r?o/r*c:f);i.position.addTo(a)}},this._singleSelectorRepulse=(e,t)=>{const i=this.container,s=i.actualOptions.interactivity.modes.repulse;if(!s)return;const o=document.querySelectorAll(e);o.length&&o.forEach((e=>{const o=e,r=i.retina.pixelRatio,c={x:(o.offsetLeft+.5*o.offsetWidth)*r,y:(o.offsetTop+.5*o.offsetHeight)*r},a=.5*o.offsetWidth*r,l=t.type===n.DivType.circle?new n.Circle(c.x,c.y,a):new n.Rectangle(o.offsetLeft*r,o.offsetTop*r,o.offsetWidth*r,o.offsetHeight*r),p=s.divs,d=(0,n.divMode)(p,o);this._processRepulse(c,a,l,d)}))},this._engine=e,t.repulse||(t.repulse={particles:[]}),this.handleClickMode=e=>{const i=this.container.actualOptions.interactivity.modes.repulse;if(!i||e!==l)return;t.repulse||(t.repulse={particles:[]});const s=t.repulse;s.clicking=!0,s.count=0;for(const e of t.repulse.particles)this.isEnabled(e)&&e.velocity.setTo(e.initialVelocity);s.particles=[],s.finish=!1,setTimeout((()=>{t.destroyed||(s.clicking=!1)}),i.duration*n.millisecondsToSeconds)}}clear(){}init(){const e=this.container,t=e.actualOptions.interactivity.modes.repulse;t&&(e.retina.repulseModeDistance=t.distance*e.retina.pixelRatio)}interact(){const e=this.container,t=e.actualOptions,i=e.interactivity.status===n.mouseMoveEvent,s=t.interactivity.events,o=s.onHover,r=o.enable,c=o.mode,a=s.onClick,p=a.enable,d=a.mode,u=s.onDiv;i&&r&&(0,n.isInArray)(l,c)?this._hoverRepulse():p&&(0,n.isInArray)(l,d)?this._clickRepulse():(0,n.divModeExecute)(l,u,((e,t)=>this._singleSelectorRepulse(e,t)))}isEnabled(e){const t=this.container,i=t.actualOptions,s=t.interactivity.mouse,o=(e?.interactivity??i.interactivity).events,r=o.onDiv,c=o.onHover,a=o.onClick,p=(0,n.isDivModeEnabled)(l,r);if(!(p||c.enable&&s.position||a.enable&&s.clickPosition))return!1;const d=c.mode,u=a.mode;return(0,n.isInArray)(l,d)||(0,n.isInArray)(l,u)||p}loadModeOptions(e,...t){e.repulse||(e.repulse=new a);for(const i of t)e.repulse.load(i?.repulse)}reset(){}}async function d(e,t=!0){e.checkVersion("3.8.1"),await e.addInteractor("externalRepulse",(t=>Promise.resolve(new p(e,t))),t)}return o})()));