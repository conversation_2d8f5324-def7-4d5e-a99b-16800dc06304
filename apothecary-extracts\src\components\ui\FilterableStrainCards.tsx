'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useMemo } from 'react';
import Image from 'next/image';

interface Strain {
  id: string;
  name: string;
  type: 'indica' | 'sativa' | 'hybrid';
  thc: number;
  cbd: number;
  price: number;
  image?: string;
  description: string;
  effects: string[];
  flavors: string[];
  genetics?: string;
  flowering?: string;
  yield?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  inStock: boolean;
  featured?: boolean;
}

interface FilterableStrainCardsProps {
  strains: Strain[];
  showFilters?: boolean;
  defaultFilter?: string;
  sortBy?: 'name' | 'thc' | 'price' | 'type';
  className?: string;
}

export default function FilterableStrainCards({
  strains,
  showFilters = true,
  defaultFilter = 'all',
  sortBy = 'name',
  className = ''
}: FilterableStrainCardsProps) {
  const [activeFilter, setActiveFilter] = useState(defaultFilter);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState(sortBy);

  const filters = [
    { id: 'all', label: 'All Strains', count: strains.length },
    { id: 'indica', label: 'Indica', count: strains.filter(s => s.type === 'indica').length },
    { id: 'sativa', label: 'Sativa', count: strains.filter(s => s.type === 'sativa').length },
    { id: 'hybrid', label: 'Hybrid', count: strains.filter(s => s.type === 'hybrid').length },
    { id: 'featured', label: 'Featured', count: strains.filter(s => s.featured).length },
    { id: 'in-stock', label: 'In Stock', count: strains.filter(s => s.inStock).length }
  ];

  const filteredAndSortedStrains = useMemo(() => {
    let filtered = strains.filter(strain => {
      // Filter by category
      const categoryMatch = 
        activeFilter === 'all' ||
        (activeFilter === 'featured' && strain.featured) ||
        (activeFilter === 'in-stock' && strain.inStock) ||
        strain.type === activeFilter;

      // Filter by search term
      const searchMatch = 
        strain.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        strain.effects.some(effect => effect.toLowerCase().includes(searchTerm.toLowerCase())) ||
        strain.flavors.some(flavor => flavor.toLowerCase().includes(searchTerm.toLowerCase()));

      return categoryMatch && searchMatch;
    });

    // Sort
    filtered.sort((a, b) => {
      switch (sortOption) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'thc':
          return b.thc - a.thc;
        case 'price':
          return a.price - b.price;
        case 'type':
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });

    return filtered;
  }, [strains, activeFilter, searchTerm, sortOption]);

  const typeColors = {
    indica: 'from-purple-500 to-purple-600',
    sativa: 'from-green-500 to-green-600',
    hybrid: 'from-blue-500 to-blue-600'
  };

  const typeIcons = {
    indica: '🌙',
    sativa: '☀️',
    hybrid: '⚖️'
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.9 },
    visible: { opacity: 1, y: 0, scale: 1 },
    hover: { y: -8, scale: 1.02 },
    exit: { opacity: 0, y: -20, scale: 0.9 }
  };

  return (
    <div className={className}>
      {/* Filters and Search */}
      {showFilters && (
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <input
              type="text"
              placeholder="Search strains, effects, or flavors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
            />
            <svg className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>

          {/* Filter Tabs */}
          <div className="flex flex-wrap justify-center gap-2">
            {filters.map((filter) => (
              <motion.button
                key={filter.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveFilter(filter.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeFilter === filter.id
                    ? 'bg-primary-600 text-cream-50'
                    : 'bg-cream-100 text-gray-700 hover:bg-primary-100'
                }`}
              >
                {filter.label} ({filter.count})
              </motion.button>
            ))}
          </div>

          {/* Sort Options */}
          <div className="flex justify-center">
            <select
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value as any)}
              className="px-4 py-2 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="name">Sort by Name</option>
              <option value="thc">Sort by THC %</option>
              <option value="price">Sort by Price</option>
              <option value="type">Sort by Type</option>
            </select>
          </div>
        </div>
      )}

      {/* Results Count */}
      <div className="text-center mb-6">
        <p className="text-gray-600">
          Showing {filteredAndSortedStrains.length} of {strains.length} strains
        </p>
      </div>

      {/* Strain Cards Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <AnimatePresence mode="popLayout">
          {filteredAndSortedStrains.map((strain) => (
            <motion.div
              key={strain.id}
              variants={cardVariants}
              whileHover="hover"
              exit="exit"
              layout
              className="group"
            >
              <div className="bg-cream-50 rounded-xl shadow-soft hover:shadow-large transition-shadow duration-300 overflow-hidden">
                {/* Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-sage-100">
                  {strain.image ? (
                    <Image
                      src={strain.image}
                      alt={strain.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <span className="text-6xl">{typeIcons[strain.type]}</span>
                    </div>
                  )}

                  {/* Type Badge */}
                  <div className="absolute top-3 left-3">
                    <span className={`bg-gradient-to-r ${typeColors[strain.type]} text-white text-xs font-semibold px-2 py-1 rounded-full`}>
                      {strain.type.toUpperCase()}
                    </span>
                  </div>

                  {/* Stock Status */}
                  <div className="absolute top-3 right-3">
                    <span className={`text-xs font-semibold px-2 py-1 rounded-full ${
                      strain.inStock 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {strain.inStock ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>

                  {/* Featured Badge */}
                  {strain.featured && (
                    <div className="absolute bottom-3 left-3">
                      <span className="bg-gold-500 text-primary-800 text-xs font-semibold px-2 py-1 rounded-full">
                        ⭐ Featured
                      </span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Name and Price */}
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-foreground group-hover:text-primary-600 transition-colors duration-200">
                      {strain.name}
                    </h3>
                    <span className="text-lg font-bold text-primary-600">
                      ${strain.price}
                    </span>
                  </div>

                  {/* THC/CBD */}
                  <div className="flex space-x-4 mb-3">
                    <div className="text-center">
                      <div className="text-xs text-gray-500">THC</div>
                      <div className="font-semibold text-primary-600">{strain.thc}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500">CBD</div>
                      <div className="font-semibold text-sage-600">{strain.cbd}%</div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {strain.description}
                  </p>

                  {/* Effects */}
                  <div className="mb-4">
                    <div className="text-xs text-gray-500 mb-1">Effects</div>
                    <div className="flex flex-wrap gap-1">
                      {strain.effects.slice(0, 3).map((effect, index) => (
                        <span key={index} className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                          {effect}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    disabled={!strain.inStock}
                    className={`w-full py-2 px-4 rounded-md font-medium transition-colors duration-200 ${
                      strain.inStock
                        ? 'bg-primary-600 text-cream-50 hover:bg-primary-700'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {strain.inStock ? 'Add to Cart' : 'Out of Stock'}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* No Results */}
      {filteredAndSortedStrains.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-12"
        >
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No strains found</h3>
          <p className="text-gray-500">Try adjusting your filters or search terms</p>
        </motion.div>
      )}
    </div>
  );
}
