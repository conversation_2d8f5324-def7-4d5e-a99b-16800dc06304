/**
 * TypeScript types for Apothecary Farms Visual Components
 */

import { ReactNode, CSSProperties } from 'react';
import { MotionProps } from 'framer-motion';

// Base component props
export interface BaseVisualProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
}

// Animation trigger types
export type AnimationTrigger = 'hover' | 'scroll' | 'click' | 'load' | 'manual';

// Terpene theme types
export type TerpeneTheme = 'pineapple' | 'gmo' | 'rosin' | 'flower' | 'extract';

// Direction types
export type Direction = 'left' | 'right' | 'up' | 'down';
export type WipeDirection = 'left-to-right' | 'right-to-left' | 'top-to-bottom' | 'bottom-to-top';

// ScrollTriggeredFadeIn Props
export interface ScrollTriggeredFadeInProps extends BaseVisualProps {
  /** Delay before animation starts (in ms) */
  delay?: number;
  /** Animation duration (in ms) */
  duration?: number;
  /** Distance to translate from (in px) */
  translateY?: number;
  /** Threshold for intersection observer (0-1) */
  threshold?: number;
  /** Whether to animate only once or every time it enters viewport */
  once?: boolean;
  /** Custom animation variants */
  variants?: MotionProps['variants'];
}

// ImageZoomOnHover Props
export interface ImageZoomOnHoverProps extends BaseVisualProps {
  /** Image source URL */
  src: string;
  /** Image alt text */
  alt: string;
  /** Scale factor on hover (default: 1.07) */
  scale?: number;
  /** Brightness factor on hover (default: 1.1) */
  brightness?: number;
  /** Animation duration (in ms) */
  duration?: number;
  /** Whether to add brand shadow */
  addBrandShadow?: boolean;
  /** Shadow color theme */
  shadowTheme?: 'apothecary' | 'gold' | 'sage';
}

// SectionRevealWipe Props
export interface SectionRevealWipeProps extends BaseVisualProps {
  /** Direction of the wipe animation */
  direction?: WipeDirection;
  /** Animation duration (in ms) */
  duration?: number;
  /** Delay before animation starts (in ms) */
  delay?: number;
  /** Background gradient theme */
  gradientTheme?: 'cannabis' | 'gold' | 'sage' | 'hero';
  /** Whether to add edge accents */
  addEdgeAccents?: boolean;
  /** Trigger for the animation */
  trigger?: AnimationTrigger;
}

// NoiseOverlayGrain Props
export interface NoiseOverlayGrainProps extends BaseVisualProps {
  /** Opacity of the grain effect (0-1) */
  opacity?: number;
  /** Blend mode for the overlay */
  blendMode?: 'overlay' | 'multiply' | 'screen' | 'soft-light';
  /** Animation speed (in ms) */
  animationSpeed?: number;
  /** Tint color for the grain */
  tintColor?: string;
  /** Size of the grain pattern */
  grainSize?: 'small' | 'medium' | 'large';
}

// DynamicGradientText Props
export interface DynamicGradientTextProps extends BaseVisualProps {
  /** Text content */
  text: string;
  /** Terpene theme for gradient colors */
  theme?: TerpeneTheme;
  /** Custom gradient colors */
  customGradient?: string;
  /** Animation speed (in ms) */
  animationSpeed?: number;
  /** Font size */
  fontSize?: string;
  /** Font weight */
  fontWeight?: number;
  /** Whether to animate continuously */
  animate?: boolean;
}

// Tilt3DCardEffect Props
export interface Tilt3DCardEffectProps extends BaseVisualProps {
  /** Maximum tilt angle in degrees */
  maxTilt?: number;
  /** Perspective value for 3D effect */
  perspective?: number;
  /** Scale factor on hover */
  scale?: number;
  /** Animation speed (in ms) */
  speed?: number;
  /** Whether to add dynamic shadows */
  addDynamicShadow?: boolean;
  /** Reset animation on mouse leave */
  reset?: boolean;
}

// TypewriterIntroText Props
export interface TypewriterIntroTextProps extends BaseVisualProps {
  /** Text to type out */
  text: string | string[];
  /** Delay between characters (in ms) */
  charDelay?: number;
  /** Delay between words (in ms) */
  wordDelay?: number;
  /** Cursor character or element */
  cursor?: string | ReactNode;
  /** Cursor blink speed (in ms) */
  cursorBlinkSpeed?: number;
  /** Whether to loop the animation */
  loop?: boolean;
  /** Delay before starting (in ms) */
  startDelay?: number;
  /** Font family */
  fontFamily?: 'sans' | 'serif';
}

// ParticleCanvasBackground Props
export interface ParticleCanvasBackgroundProps extends BaseVisualProps {
  /** Number of particles */
  particleCount?: number;
  /** Particle colors */
  colors?: string[];
  /** Particle size range */
  sizeRange?: [number, number];
  /** Movement speed */
  speed?: number;
  /** Connection distance for lines */
  connectionDistance?: number;
  /** Whether particles react to mouse */
  interactive?: boolean;
  /** Repulsion distance from cursor */
  repulsionDistance?: number;
  /** Attraction strength */
  attractionStrength?: number;
}

// LottieIconAnimations Props
export interface LottieIconAnimationsProps extends BaseVisualProps {
  /** Lottie animation data or URL */
  animationData: object | string;
  /** Animation trigger */
  trigger?: AnimationTrigger;
  /** Whether to loop the animation */
  loop?: boolean;
  /** Animation speed multiplier */
  speed?: number;
  /** Width of the animation */
  width?: number;
  /** Height of the animation */
  height?: number;
  /** Icon category for styling */
  category?: 'beaker' | 'trichome' | 'flame' | 'extraction' | 'flower' | 'product';
}

// MorphingBlobHero Props
export interface MorphingBlobHeroProps extends BaseVisualProps {
  /** Number of blob shapes to morph between */
  shapeCount?: number;
  /** Animation duration for each morph (in ms) */
  morphDuration?: number;
  /** Delay between morphs (in ms) */
  morphDelay?: number;
  /** Blob size (width and height) */
  size?: number;
  /** Gradient theme for the blob */
  gradientTheme?: TerpeneTheme;
  /** Custom gradient colors */
  customGradient?: string;
  /** Whether to animate continuously */
  animate?: boolean;
  /** Blur effect intensity */
  blurIntensity?: number;
  /** Position relative to container */
  position?: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

// Demo component props
export interface ComponentDemoProps {
  /** Component name */
  name: string;
  /** Component description */
  description: string;
  /** Demo component */
  component: ReactNode;
  /** Code example */
  codeExample?: string;
  /** Props documentation */
  propsDoc?: Record<string, any>;
}

// Particle system types
export interface ParticleConfig {
  count: number;
  color: string[];
  size: {
    min: number;
    max: number;
  };
  speed: number;
  connections: {
    distance: number;
    opacity: number;
  };
  move: {
    attract: {
      enable: boolean;
      distance: number;
    };
    repulse: {
      enable: boolean;
      distance: number;
    };
  };
}

// SVG Blob path types
export interface BlobPath {
  id: string;
  path: string;
  viewBox?: string;
}

// Animation state types
export type AnimationState = 'idle' | 'running' | 'paused' | 'completed';

// Intersection observer options
export interface IntersectionOptions {
  threshold?: number | number[];
  rootMargin?: string;
  triggerOnce?: boolean;
}

// Export all types as a namespace
export namespace VisualComponents {
  export type ScrollTriggeredFadeIn = ScrollTriggeredFadeInProps;
  export type ImageZoomOnHover = ImageZoomOnHoverProps;
  export type SectionRevealWipe = SectionRevealWipeProps;
  export type NoiseOverlayGrain = NoiseOverlayGrainProps;
  export type DynamicGradientText = DynamicGradientTextProps;
  export type Tilt3DCardEffect = Tilt3DCardEffectProps;
  export type TypewriterIntroText = TypewriterIntroTextProps;
  export type ParticleCanvasBackground = ParticleCanvasBackgroundProps;
  export type LottieIconAnimations = LottieIconAnimationsProps;
  export type MorphingBlobHero = MorphingBlobHeroProps;
}
