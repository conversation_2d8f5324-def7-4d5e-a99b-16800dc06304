# Apothecary Farms UI Components Library

A comprehensive collection of React UI components specifically designed for cannabis industry applications. Built with modern technologies including React 19, Framer Motion, and TailwindCSS v4.

## 🌿 Overview

This UI component library provides production-ready React components that follow cannabis industry best practices and design patterns. Each component is built with accessibility, performance, and brand consistency in mind.

## 🚀 Quick Start

### Installation

The components are already integrated into the Apothecary Farms project. To use them:

```tsx
import { 
  HeroVideoWithCTA, 
  AnimatedCardSlider,
  TabbedProductGrid,
  GlitchTextEffect 
} from '@/components/ui';
```

### Basic Usage

```tsx
// Hero section with video background
<HeroVideoWithCTA
  videoSrc="/videos/hero-cannabis.mp4"
  headline="Premium Cannabis Extracts"
  subtitle="Award-winning quality, lab-tested purity"
  primaryCTA="Shop Now"
  onPrimaryCTA={() => router.push('/products')}
/>

// Product carousel
<AnimatedCardSlider
  products={products}
  visibleCards={3}
  autoAdvance={5000}
  onCardClick={(product) => router.push(`/products/${product.id}`)}
/>

// Tabbed product grid
<TabbedProductGrid
  products={products}
  categories={categories}
  columns={3}
  onProductClick={(product) => handleProductClick(product)}
/>
```

## 📦 Components

### Hero Components

#### HeroVideoWithCTA
Full-screen video hero with call-to-action overlays.

**Features:**
- Auto-playing video backgrounds
- Customizable overlay opacity
- Primary and secondary CTA buttons
- Responsive design with mobile optimization
- Video controls and accessibility features

**Props:**
- `videoSrc` - Video source URL
- `posterSrc` - Poster image for video
- `headline` - Main headline text
- `subtitle` - Optional subtitle
- `primaryCTA` - Primary button text
- `onPrimaryCTA` - Primary button action
- `secondaryCTA` - Secondary button text (optional)
- `onSecondaryCTA` - Secondary button action (optional)
- `autoplay` - Auto-play video (default: true)
- `overlayOpacity` - Overlay opacity (default: 0.4)

### Card Components

#### AnimatedCardSlider
Responsive product carousel with smooth animations.

**Features:**
- Auto-advance functionality
- Touch/swipe support
- Navigation dots and arrows
- Responsive grid layout
- Product information display (price, THC/CBD, effects)

**Props:**
- `products` - Array of product objects
- `visibleCards` - Number of cards visible at once (default: 3)
- `autoAdvance` - Auto-advance interval in ms (default: 4000)
- `touchEnabled` - Enable touch/swipe navigation (default: true)
- `showDots` - Show navigation dots (default: true)
- `showArrows` - Show arrow navigation (default: true)
- `onCardClick` - Card click handler

### Grid Components

#### TabbedProductGrid
Organized product display with category tabs.

**Features:**
- Category-based filtering
- Smooth tab transitions
- Pagination support
- Responsive grid layout
- Product count badges

**Props:**
- `products` - Array of product objects
- `categories` - Array of category objects with icons
- `defaultCategory` - Default active category
- `columns` - Grid columns (default: 3)
- `itemsPerPage` - Products per page (default: 9)
- `showPagination` - Enable pagination (default: true)
- `onProductClick` - Product click handler

#### MasonryProductShowcase
Dynamic masonry layout with filtering.

**Features:**
- Masonry or carousel modes
- Advanced filtering system
- Responsive column layout
- Random height variation for visual interest
- Empty state handling

**Props:**
- `products` - Array of product objects
- `columns` - Number of columns (default: 3)
- `gap` - Gap between items (default: 20)
- `carouselMode` - Enable carousel mode (default: false)
- `filters` - Array of filter options
- `onProductClick` - Product click handler

### Effect Components

#### GlitchTextEffect
Modern glitch text effects for branding.

**Features:**
- Multiple intensity levels (low, medium, high)
- Various trigger options (hover, continuous, scroll)
- Customizable colors and corruption patterns
- Performance optimized animations
- Accessibility considerations

**Props:**
- `text` - Text to apply effect to
- `intensity` - Effect intensity: 'low' | 'medium' | 'high'
- `trigger` - Animation trigger: 'hover' | 'continuous' | 'scroll'
- `colors` - Array of glitch colors
- `fontSize` - Font size (default: '2rem')
- `fontWeight` - Font weight (default: 700)

## 🎨 Design System Integration

### Brand Colors
Components automatically use the Apothecary Farms brand color system:

```tsx
import { brandColors } from '@/styles/brand';

// Primary brand colors
brandColors.primary[500] // Main green: #359a5a
brandColors.apothecary   // Mint green: #2FB886
brandColors.gold[400]    // Gold accent
brandColors.cream[50]    // Light cream
```

### Typography
Consistent typography using brand fonts:

```tsx
import { brandTypography } from '@/styles/brand';

// Font families
brandTypography.fonts.sans  // Inter
brandTypography.fonts.serif // Playfair Display
```

### Animations
Smooth animations using brand-consistent easing:

```tsx
import { brandAnimations } from '@/styles/brand';

// Easing functions
brandAnimations.easing.smooth // cubic-bezier(0.4, 0, 0.2, 1)
brandAnimations.duration.normal // 300ms
```

## 🛠 Development

### Demo Pages
- `/ui-components` - Interactive showcase of all UI components
- `/visual-components` - Visual effects and animation components

### File Structure
```
src/components/ui/
├── hero/
│   └── HeroVideoWithCTA.tsx
├── cards/
│   └── AnimatedCardSlider.tsx
├── grids/
│   ├── TabbedProductGrid.tsx
│   └── MasonryProductShowcase.tsx
├── effects/
│   └── GlitchTextEffect.tsx
├── demos/
│   └── UIComponentsShowcase.tsx
└── index.ts
```

### TypeScript Support
Full TypeScript support with comprehensive type definitions:

```tsx
import type { 
  Product, 
  Strain, 
  HeroVideoWithCTAProps,
  AnimatedCardSliderProps 
} from '@/types/ui';
```

## 📱 Responsive Design

All components are built mobile-first with responsive breakpoints:

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px - 1440px
- **Large Desktop**: 1440px+

### Responsive Features
- Adaptive grid layouts
- Touch-friendly interactions
- Optimized image loading
- Performance considerations for mobile devices

## ♿ Accessibility

Components follow WCAG 2.1 guidelines:

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Meets AA standards
- **Reduced Motion**: Respects user preferences
- **Focus Management**: Clear focus indicators

## 🚀 Performance

### Optimization Features
- **Lazy Loading**: Images and heavy components
- **Code Splitting**: Dynamic imports for large components
- **Animation Performance**: GPU-accelerated animations
- **Bundle Size**: Tree-shaking friendly exports
- **Memory Management**: Proper cleanup of intervals and listeners

### Performance Best Practices
```tsx
// Lazy load heavy components
const MasonryProductShowcase = lazy(() => import('@/components/ui/grids/MasonryProductShowcase'));

// Use React.memo for expensive renders
const ProductCard = React.memo(({ product }) => {
  // Component implementation
});

// Optimize images
<Image
  src={product.image}
  alt={product.name}
  fill
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  priority={index < 3} // Prioritize above-the-fold images
/>
```

## 🧪 Testing

### Component Testing
```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AnimatedCardSlider } from '@/components/ui';

test('renders product cards correctly', () => {
  render(<AnimatedCardSlider products={mockProducts} />);
  expect(screen.getByText('Blue Dream')).toBeInTheDocument();
});

test('handles card click events', () => {
  const handleClick = jest.fn();
  render(<AnimatedCardSlider products={mockProducts} onCardClick={handleClick} />);
  
  fireEvent.click(screen.getByText('Blue Dream'));
  expect(handleClick).toHaveBeenCalledWith(mockProducts[0]);
});
```

## 🔧 Customization

### Theming
Components can be customized through CSS custom properties:

```css
:root {
  --color-primary-500: #your-brand-color;
  --font-sans: 'Your Font', system-ui, sans-serif;
  --shadow-soft: your-custom-shadow;
}
```

### Component Variants
Many components support variant props for different styles:

```tsx
<TabbedProductGrid
  variant="pills" // or "default", "soft", "underline"
  orientation="horizontal" // or "vertical"
/>
```

## 📄 License

Part of the Apothecary Farms project. All rights reserved.

---

Built with ❤️ for the cannabis industry using React, Framer Motion, and TailwindCSS.
