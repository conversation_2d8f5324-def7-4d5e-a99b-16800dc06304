'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface Feature {
  icon: ReactNode;
  title: string;
  description: string;
  color?: string;
}

interface FeatureIconsProps {
  features: Feature[];
  layout?: 'grid' | 'horizontal' | 'vertical';
  animationDelay?: number;
  hoverEffect?: boolean;
  className?: string;
}

export default function FeatureIcons({
  features,
  layout = 'grid',
  animationDelay = 0.1,
  hoverEffect = true,
  className = ''
}: FeatureIconsProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: animationDelay,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  const hoverVariants = hoverEffect ? {
    hover: {
      scale: 1.05,
      y: -5,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    }
  } : {};

  const layoutClasses = {
    grid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
    horizontal: 'flex flex-wrap justify-center gap-6',
    vertical: 'flex flex-col space-y-6'
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`${layoutClasses[layout]} ${className}`}
    >
      {features.map((feature, index) => (
        <motion.div
          key={index}
          variants={itemVariants}
          whileHover="hover"
          className="group"
        >
          <motion.div
            variants={hoverVariants}
            className="flex flex-col items-center text-center p-6 bg-cream-50 rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300"
          >
            {/* Icon Container */}
            <motion.div
              className={`flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
                feature.color || 'bg-primary-100 text-primary-600'
              }`}
              whileHover={{
                rotate: [0, -10, 10, -10, 0],
                transition: { duration: 0.5 }
              }}
            >
              <div className="text-2xl">
                {feature.icon}
              </div>
            </motion.div>

            {/* Title */}
            <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
              {feature.title}
            </h3>

            {/* Description */}
            <p className="text-sm text-gray-600 leading-relaxed">
              {feature.description}
            </p>

            {/* Animated underline */}
            <motion.div
              className="w-0 h-0.5 bg-primary-500 mt-3 group-hover:w-12 transition-all duration-300"
              initial={{ width: 0 }}
              whileHover={{ width: 48 }}
            />
          </motion.div>
        </motion.div>
      ))}
    </motion.div>
  );
}

// Predefined icon components for common cannabis/apothecary features
export const CannabisIcons = {
  Quality: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
    </svg>
  ),
  Lab: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M9 2v6l-4 4v8a2 2 0 002 2h10a2 2 0 002-2v-8l-4-4V2H9zm2 2h2v5.5l4 4V20H7v-6.5l4-4V4z" />
    </svg>
  ),
  Organic: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
    </svg>
  ),
  Security: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
    </svg>
  ),
  Fast: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M13 1v8h8l-8 14v-8H5l8-14z" />
    </svg>
  ),
  Support: (
    <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
    </svg>
  )
};

// Example usage component
export function ExampleFeatureIcons() {
  const features = [
    {
      icon: CannabisIcons.Quality,
      title: 'Premium Quality',
      description: 'Lab-tested products ensuring the highest standards of purity and potency.',
      color: 'bg-primary-100 text-primary-600'
    },
    {
      icon: CannabisIcons.Lab,
      title: 'Lab Tested',
      description: 'Every product undergoes rigorous testing for safety and consistency.',
      color: 'bg-sage-100 text-sage-600'
    },
    {
      icon: CannabisIcons.Organic,
      title: 'Organic Grown',
      description: 'Sustainably cultivated using organic farming practices.',
      color: 'bg-gold-100 text-gold-600'
    },
    {
      icon: CannabisIcons.Security,
      title: 'Secure & Legal',
      description: 'Fully compliant with state regulations and secure transactions.',
      color: 'bg-cream-200 text-charcoal-600'
    }
  ];

  return <FeatureIcons features={features} />;
}
