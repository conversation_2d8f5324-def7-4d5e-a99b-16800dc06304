import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Apothecary Extracts - Premium Cannabis Dispensary | Colorado Springs",
  description: "Discover Colorado's finest selection of premium cannabis products at Apothecary Extracts. Quality flower, concentrates, edibles, and topicals. Medical and recreational cannabis dispensary in Colorado Springs.",
  keywords: "cannabis dispensary, marijuana, Colorado Springs, premium cannabis, flower, concentrates, edibles, topicals, medical marijuana, recreational cannabis",
  openGraph: {
    title: "Apothecary Extracts - Premium Cannabis Dispensary",
    description: "Colorado's finest selection of premium cannabis products. Quality, potency, and purity guaranteed.",
    type: "website",
    locale: "en_US",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${playfairDisplay.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
