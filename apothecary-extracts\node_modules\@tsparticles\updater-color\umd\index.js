(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./ColorUpdater.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadColorUpdater = loadColorUpdater;
    const ColorUpdater_js_1 = require("./ColorUpdater.js");
    async function loadColorUpdater(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await engine.addParticleUpdater("color", container => {
            return Promise.resolve(new ColorUpdater_js_1.ColorUpdater(container, engine));
        }, refresh);
    }
});
