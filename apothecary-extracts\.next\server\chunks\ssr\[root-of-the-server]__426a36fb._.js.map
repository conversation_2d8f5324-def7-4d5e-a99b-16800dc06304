{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/styles/brand.ts"], "sourcesContent": ["/**\n * Apothecary Farms Brand Utility System\n * Centralized brand tokens for colors, gradients, shadows, and animations\n */\n\n// Brand Colors\nexport const brandColors = {\n  // Primary Green Palette\n  primary: {\n    50: '#f0f9f4',\n    100: '#dcf2e4',\n    200: '#bce5cd',\n    300: '#8dd1a8',\n    400: '#57b67c',\n    500: '#359a5a', // Main brand green\n    600: '#267d47',\n    700: '#1f633a',\n    800: '#1b4332',\n    900: '#163a2b',\n  },\n  \n  // Apothecary Green (from memory - the mint green mentioned)\n  apothecary: '#2FB886',\n  \n  // Cream/Neutral Palette\n  cream: {\n    50: '#fefefe',\n    100: '#f8f6f0',\n    200: '#f4f1e8',\n    300: '#ede8db',\n    400: '#e4dcc8',\n    500: '#d8cdb0',\n  },\n  \n  // Gold/Amber Palette (for extracts/rosin)\n  gold: {\n    50: '#fefcf7',\n    100: '#fdf8ed',\n    200: '#f9eed5',\n    300: '#f4e4bc',\n    400: '#edd5a3',\n    500: '#d4a574',\n  },\n  \n  // Charcoal/<PERSON> Palette\n  charcoal: {\n    50: '#f8f9fa',\n    100: '#e9ecef',\n    200: '#dee2e6',\n    300: '#ced4da',\n    400: '#adb5bd',\n    500: '#6c757d',\n    600: '#495057',\n    700: '#343a40',\n    800: '#2d3436',\n    900: '#212529',\n  },\n  \n  // Sage Green Palette\n  sage: {\n    50: '#f7f9f8',\n    100: '#eef2f0',\n    200: '#dde5e1',\n    300: '#c4d2ca',\n    400: '#a5b8ad',\n    500: '#95a99c',\n  },\n} as const;\n\n// Terpene-Inspired Color Palettes\nexport const terpeneColors = {\n  // Pineapple Express - Orange to Yellow\n  pineapple: {\n    from: '#ff8c00',\n    to: '#ffd700',\n    gradient: 'linear-gradient(135deg, #ff8c00 0%, #ffd700 100%)',\n  },\n  \n  // GMO - Blue Frost\n  gmo: {\n    from: '#4a90e2',\n    to: '#87ceeb',\n    gradient: 'linear-gradient(135deg, #4a90e2 0%, #87ceeb 100%)',\n  },\n  \n  // Rosin - Gold to Amber\n  rosin: {\n    from: '#d4a574',\n    to: '#ff8c00',\n    gradient: 'linear-gradient(135deg, #d4a574 0%, #ff8c00 100%)',\n  },\n  \n  // Flower - Green to Yellow\n  flower: {\n    from: '#359a5a',\n    to: '#ffd700',\n    gradient: 'linear-gradient(135deg, #359a5a 0%, #ffd700 100%)',\n  },\n  \n  // Extract - Amber to Purple\n  extract: {\n    from: '#ff8c00',\n    to: '#9370db',\n    gradient: 'linear-gradient(135deg, #ff8c00 0%, #9370db 100%)',\n  },\n  \n  // Particle Palette - Soft colors for backgrounds\n  particles: {\n    yellow: '#fff9c4',\n    lime: '#d4edda',\n    lavender: '#e2d5f1',\n  },\n} as const;\n\n// Brand Gradients\nexport const brandGradients = {\n  // Main brand gradients\n  cannabis: 'linear-gradient(135deg, #1b4332 0%, #267d47 50%, #95a99c 100%)',\n  gold: 'linear-gradient(135deg, #d4a574 0%, #edd5a3 100%)',\n  sage: 'linear-gradient(135deg, #95a99c 0%, #c4d2ca 100%)',\n  \n  // Context-based gradients\n  hero: 'linear-gradient(135deg, #1b4332 0%, #163a2b 100%)',\n  card: 'linear-gradient(135deg, #f8f6f0 0%, #ede8db 100%)',\n  \n  // Animated gradients for text\n  dynamicText: {\n    pineapple: 'linear-gradient(45deg, #ff8c00, #ffd700, #ff8c00)',\n    gmo: 'linear-gradient(45deg, #4a90e2, #87ceeb, #4a90e2)',\n    rosin: 'linear-gradient(45deg, #d4a574, #ff8c00, #d4a574)',\n  },\n} as const;\n\n// Brand Shadows\nexport const brandShadows = {\n  // Soft shadows\n  soft: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n  medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n  large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n  \n  // Brand-specific glows\n  apothecaryGlow: '0 0 20px rgba(47, 184, 134, 0.3)',\n  goldGlow: '0 0 20px rgba(212, 165, 116, 0.3)',\n  \n  // Dynamic shadows for tilt effects\n  tiltShadow: (direction: 'left' | 'right' | 'up' | 'down') => {\n    const shadows = {\n      left: '-5px 5px 15px rgba(0, 0, 0, 0.2)',\n      right: '5px 5px 15px rgba(0, 0, 0, 0.2)',\n      up: '0 -5px 15px rgba(0, 0, 0, 0.2)',\n      down: '0 5px 15px rgba(0, 0, 0, 0.2)',\n    };\n    return shadows[direction];\n  },\n} as const;\n\n// Animation Presets\nexport const brandAnimations = {\n  // Easing functions (Framer Motion format)\n  easing: {\n    smooth: [0.4, 0, 0.2, 1],\n    bounce: [0.68, -0.55, 0.265, 1.55],\n    elastic: [0.175, 0.885, 0.32, 1.275],\n  },\n\n  // CSS easing functions (for CSS transitions)\n  cssEasing: {\n    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n    elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n  },\n  \n  // Duration presets\n  duration: {\n    fast: 200,\n    normal: 300,\n    slow: 500,\n    verySlow: 800,\n  },\n  \n  // Common animation variants for Framer Motion\n  fadeInUp: {\n    initial: { opacity: 0, y: 40 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.5, ease: 'easeOut' },\n  },\n  \n  scaleOnHover: {\n    whileHover: { scale: 1.07, brightness: 1.1 },\n    transition: { duration: 0.2 },\n  },\n  \n  tiltEffect: {\n    perspective: 1000,\n    rotateRange: 10,\n  },\n  \n  typewriter: {\n    charDelay: 50,\n    cursorBlink: 1000,\n  },\n} as const;\n\n// Typography Scale\nexport const brandTypography = {\n  fonts: {\n    sans: 'var(--font-inter), Inter, system-ui, sans-serif',\n    serif: 'var(--font-playfair), Playfair Display, Georgia, serif',\n  },\n  \n  sizes: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem',\n    '4xl': '2.25rem',\n    '5xl': '3rem',\n    '6xl': '3.75rem',\n  },\n  \n  weights: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700,\n  },\n} as const;\n\n// Spacing Scale\nexport const brandSpacing = {\n  xs: '0.25rem',\n  sm: '0.5rem',\n  md: '1rem',\n  lg: '1.5rem',\n  xl: '2rem',\n  '2xl': '3rem',\n  '3xl': '4rem',\n  '4xl': '6rem',\n  '5xl': '8rem',\n} as const;\n\n// Utility Functions\nexport const brandUtils = {\n  // Get color with opacity\n  withOpacity: (color: string, opacity: number) => {\n    if (color.startsWith('#')) {\n      const hex = color.slice(1);\n      const r = parseInt(hex.slice(0, 2), 16);\n      const g = parseInt(hex.slice(2, 4), 16);\n      const b = parseInt(hex.slice(4, 6), 16);\n      return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n    }\n    return color;\n  },\n  \n  // Generate random terpene gradient\n  randomTerpeneGradient: () => {\n    const terpenes = Object.keys(terpeneColors);\n    const randomTerpene = terpenes[Math.floor(Math.random() * terpenes.length)];\n    return terpeneColors[randomTerpene as keyof typeof terpeneColors].gradient;\n  },\n  \n  // Get appropriate text color for background\n  getTextColor: (backgroundColor: string) => {\n    // Simple light/dark detection - in production, use a proper contrast calculation\n    const darkColors = ['#1b4332', '#163a2b', '#267d47', '#2d3436', '#212529'];\n    return darkColors.includes(backgroundColor) ? brandColors.cream[50] : brandColors.charcoal[800];\n  },\n} as const;\n\nexport default {\n  colors: brandColors,\n  terpenes: terpeneColors,\n  gradients: brandGradients,\n  shadows: brandShadows,\n  animations: brandAnimations,\n  typography: brandTypography,\n  spacing: brandSpacing,\n  utils: brandUtils,\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,eAAe;;;;;;;;;;;;AACR,MAAM,cAAc;IACzB,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,4DAA4D;IAC5D,YAAY;IAEZ,wBAAwB;IACxB,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,0CAA0C;IAC1C,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,wBAAwB;IACxB,UAAU;QACR,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IAEA,qBAAqB;IACrB,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,gBAAgB;IAC3B,uCAAuC;IACvC,WAAW;QACT,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,mBAAmB;IACnB,KAAK;QACH,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,wBAAwB;IACxB,OAAO;QACL,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,2BAA2B;IAC3B,QAAQ;QACN,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,4BAA4B;IAC5B,SAAS;QACP,MAAM;QACN,IAAI;QACJ,UAAU;IACZ;IAEA,iDAAiD;IACjD,WAAW;QACT,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,MAAM,iBAAiB;IAC5B,uBAAuB;IACvB,UAAU;IACV,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,MAAM;IACN,MAAM;IAEN,8BAA8B;IAC9B,aAAa;QACX,WAAW;QACX,KAAK;QACL,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,eAAe;IACf,MAAM;IACN,QAAQ;IACR,OAAO;IAEP,uBAAuB;IACvB,gBAAgB;IAChB,UAAU;IAEV,mCAAmC;IACnC,YAAY,CAAC;QACX,MAAM,UAAU;YACd,MAAM;YACN,OAAO;YACP,IAAI;YACJ,MAAM;QACR;QACA,OAAO,OAAO,CAAC,UAAU;IAC3B;AACF;AAGO,MAAM,kBAAkB;IAC7B,0CAA0C;IAC1C,QAAQ;QACN,QAAQ;YAAC;YAAK;YAAG;YAAK;SAAE;QACxB,QAAQ;YAAC;YAAM,CAAC;YAAM;YAAO;SAAK;QAClC,SAAS;YAAC;YAAO;YAAO;YAAM;SAAM;IACtC;IAEA,6CAA6C;IAC7C,WAAW;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,mBAAmB;IACnB,UAAU;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IAEA,8CAA8C;IAC9C,UAAU;QACR,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;IAEA,cAAc;QACZ,YAAY;YAAE,OAAO;YAAM,YAAY;QAAI;QAC3C,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,YAAY;QACV,aAAa;QACb,aAAa;IACf;IAEA,YAAY;QACV,WAAW;QACX,aAAa;IACf;AACF;AAGO,MAAM,kBAAkB;IAC7B,OAAO;QACL,MAAM;QACN,OAAO;IACT;IAEA,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,SAAS;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAGO,MAAM,aAAa;IACxB,yBAAyB;IACzB,aAAa,CAAC,OAAe;QAC3B,IAAI,MAAM,UAAU,CAAC,MAAM;YACzB,MAAM,MAAM,MAAM,KAAK,CAAC;YACxB,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACpC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC7C;QACA,OAAO;IACT;IAEA,mCAAmC;IACnC,uBAAuB;QACrB,MAAM,WAAW,OAAO,IAAI,CAAC;QAC7B,MAAM,gBAAgB,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QAC3E,OAAO,aAAa,CAAC,cAA4C,CAAC,QAAQ;IAC5E;IAEA,4CAA4C;IAC5C,cAAc,CAAC;QACb,iFAAiF;QACjF,MAAM,aAAa;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;QAC1E,OAAO,WAAW,QAAQ,CAAC,mBAAmB,YAAY,KAAK,CAAC,GAAG,GAAG,YAAY,QAAQ,CAAC,IAAI;IACjG;AACF;uCAEe;IACb,QAAQ;IACR,UAAU;IACV,WAAW;IACX,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/demos/ComponentDemo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ComponentDemoProps } from '../../../types/visual';\nimport { brandColors, brandShadows } from '../../../styles/brand';\n\n/**\n * ComponentDemo Wrapper\n * \n * A reusable demo wrapper for showcasing visual components\n * with code examples and interactive controls.\n */\nconst ComponentDemo: React.FC<ComponentDemoProps> = ({\n  name,\n  description,\n  component,\n  codeExample,\n  propsDoc,\n}) => {\n  const [showCode, setShowCode] = useState(false);\n  const [showProps, setShowProps] = useState(false);\n\n  return (\n    <div className=\"component-demo bg-cream-100 rounded-lg p-6 mb-8 border border-sage-200\">\n      {/* Header */}\n      <div className=\"demo-header mb-6\">\n        <h3 className=\"text-2xl font-serif font-bold text-charcoal-800 mb-2\">\n          {name}\n        </h3>\n        <p className=\"text-charcoal-600 leading-relaxed\">\n          {description}\n        </p>\n      </div>\n\n      {/* Demo Area */}\n      <div \n        className=\"demo-area bg-white rounded-lg p-8 mb-4 relative overflow-hidden\"\n        style={{\n          minHeight: '200px',\n          boxShadow: brandShadows.medium,\n        }}\n      >\n        {component}\n      </div>\n\n      {/* Controls */}\n      <div className=\"demo-controls flex gap-4 mb-4\">\n        <button\n          onClick={() => setShowCode(!showCode)}\n          className=\"btn-secondary text-sm\"\n          style={{\n            backgroundColor: showCode ? brandColors.primary[600] : 'transparent',\n            color: showCode ? brandColors.cream[50] : brandColors.primary[600],\n            border: `2px solid ${brandColors.primary[600]}`,\n            padding: '0.5rem 1rem',\n            borderRadius: '0.375rem',\n            fontWeight: 600,\n            transition: 'all 0.2s ease-in-out',\n          }}\n        >\n          {showCode ? 'Hide Code' : 'Show Code'}\n        </button>\n\n        {propsDoc && (\n          <button\n            onClick={() => setShowProps(!showProps)}\n            className=\"btn-secondary text-sm\"\n            style={{\n              backgroundColor: showProps ? brandColors.sage[500] : 'transparent',\n              color: showProps ? brandColors.cream[50] : brandColors.sage[500],\n              border: `2px solid ${brandColors.sage[500]}`,\n              padding: '0.5rem 1rem',\n              borderRadius: '0.375rem',\n              fontWeight: 600,\n              transition: 'all 0.2s ease-in-out',\n            }}\n          >\n            {showProps ? 'Hide Props' : 'Show Props'}\n          </button>\n        )}\n      </div>\n\n      {/* Code Example */}\n      {showCode && codeExample && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          className=\"code-example bg-charcoal-800 rounded-lg p-4 mb-4\"\n        >\n          <pre className=\"text-cream-100 text-sm overflow-x-auto\">\n            <code>{codeExample}</code>\n          </pre>\n        </motion.div>\n      )}\n\n      {/* Props Documentation */}\n      {showProps && propsDoc && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          className=\"props-doc bg-sage-50 rounded-lg p-4\"\n        >\n          <h4 className=\"font-semibold text-charcoal-800 mb-3\">Props</h4>\n          <div className=\"space-y-2\">\n            {Object.entries(propsDoc).map(([prop, info]) => (\n              <div key={prop} className=\"prop-item\">\n                <span className=\"font-mono text-sm text-primary-600 font-semibold\">\n                  {prop}\n                </span>\n                <span className=\"text-charcoal-600 ml-2\">\n                  {typeof info === 'string' ? info : JSON.stringify(info)}\n                </span>\n              </div>\n            ))}\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default ComponentDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA;;;;;CAKC,GACD,MAAM,gBAA8C,CAAC,EACnD,IAAI,EACJ,WAAW,EACX,SAAS,EACT,WAAW,EACX,QAAQ,EACT;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKL,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,WAAW,sHAAA,CAAA,eAAY,CAAC,MAAM;gBAChC;0BAEC;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;wBACV,OAAO;4BACL,iBAAiB,WAAW,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI,GAAG;4BACvD,OAAO,WAAW,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;4BAClE,QAAQ,CAAC,UAAU,EAAE,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI,EAAE;4BAC/C,SAAS;4BACT,cAAc;4BACd,YAAY;4BACZ,YAAY;wBACd;kCAEC,WAAW,cAAc;;;;;;oBAG3B,0BACC,8OAAC;wBACC,SAAS,IAAM,aAAa,CAAC;wBAC7B,WAAU;wBACV,OAAO;4BACL,iBAAiB,YAAY,sHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI,GAAG;4BACrD,OAAO,YAAY,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,sHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;4BAChE,QAAQ,CAAC,UAAU,EAAE,sHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI,EAAE;4BAC5C,SAAS;4BACT,cAAc;4BACd,YAAY;4BACZ,YAAY;wBACd;kCAEC,YAAY,eAAe;;;;;;;;;;;;YAMjC,YAAY,6BACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAM;;;;;;;;;;;;;;;;YAMZ,aAAa,0BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBACzC,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC;wCAAK,WAAU;kDACb;;;;;;kDAEH,8OAAC;wCAAK,WAAU;kDACb,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC;;;;;;;+BAL5C;;;;;;;;;;;;;;;;;;;;;;AAcxB;uCAEe", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/ScrollTriggeredFadeIn.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { ScrollTriggeredFadeInProps } from '../../types/visual';\nimport { brandColors, brandShadows, brandAnimations } from '../../styles/brand';\n\n/**\n * ScrollTriggeredFadeIn Component\n * \n * Animates cards, text blocks, or images to \"rise and fade in\" when they enter the viewport.\n * Features Apothecary Farms brand aesthetics with mint-green glow effects.\n * \n * @example\n * ```tsx\n * <ScrollTriggeredFadeIn>\n *   <div className=\"card\">Content that fades in on scroll</div>\n * </ScrollTriggeredFadeIn>\n * ```\n */\nconst ScrollTriggeredFadeIn: React.FC<ScrollTriggeredFadeInProps> = ({\n  children,\n  className = '',\n  style,\n  delay = 0,\n  duration = 500,\n  translateY = 40,\n  threshold = 0.1,\n  once = true,\n  variants,\n}) => {\n  const [ref, inView] = useInView({\n    threshold,\n    triggerOnce: once,\n  });\n\n  // Default animation variants\n  const defaultVariants = {\n    hidden: {\n      opacity: 0,\n      y: translateY,\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: duration / 1000,\n        delay: delay / 1000,\n        ease: brandAnimations.easing.smooth,\n      },\n    },\n  };\n\n  // Use custom variants if provided, otherwise use defaults\n  const animationVariants = variants || defaultVariants;\n\n  return (\n    <motion.div\n      ref={ref}\n      initial=\"hidden\"\n      animate={inView ? 'visible' : 'hidden'}\n      variants={animationVariants}\n      className={`scroll-fade-in ${className}`}\n      style={{\n        ...style,\n        // Add mint-green glow effect when visible\n        boxShadow: inView ? brandShadows.apothecaryGlow : 'none',\n        transition: 'box-shadow 0.3s ease-in-out',\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport default ScrollTriggeredFadeIn;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA;;;;;;;;;;;;CAYC,GACD,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,aAAa,EAAE,EACf,YAAY,GAAG,EACf,OAAO,IAAI,EACX,QAAQ,EACT;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B;QACA,aAAa;IACf;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU,WAAW;gBACrB,OAAO,QAAQ;gBACf,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;YACrC;QACF;IACF;IAEA,0DAA0D;IAC1D,MAAM,oBAAoB,YAAY;IAEtC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS,SAAS,YAAY;QAC9B,UAAU;QACV,WAAW,CAAC,eAAe,EAAE,WAAW;QACxC,OAAO;YACL,GAAG,KAAK;YACR,0CAA0C;YAC1C,WAAW,SAAS,sHAAA,CAAA,eAAY,CAAC,cAAc,GAAG;YAClD,YAAY;QACd;kBAEC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/ImageZoomOnHover.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { ImageZoomOnHoverProps } from '../../types/visual';\nimport { brandShadows, brandAnimations } from '../../styles/brand';\n\n/**\n * ImageZoomOnHover Component\n * \n * Makes product imagery feel tactile and reactive to mouse interaction.\n * Features scale and brightness effects with Apothecary Green shadow.\n * \n * @example\n * ```tsx\n * <ImageZoomOnHover \n *   src=\"/products/rosin.jpg\" \n *   alt=\"Premium Rosin Extract\"\n *   addBrandShadow={true}\n *   shadowTheme=\"apothecary\"\n * />\n * ```\n */\nconst ImageZoomOnHover: React.FC<ImageZoomOnHoverProps> = ({\n  src,\n  alt,\n  className = '',\n  style,\n  scale = 1.07,\n  brightness = 1.1,\n  duration = 200,\n  addBrandShadow = true,\n  shadowTheme = 'apothecary',\n}) => {\n  // Get the appropriate shadow based on theme\n  const getShadow = (theme: 'apothecary' | 'gold' | 'sage') => {\n    switch (theme) {\n      case 'apothecary':\n        return brandShadows.apothecaryGlow;\n      case 'gold':\n        return brandShadows.goldGlow;\n      case 'sage':\n        return brandShadows.medium;\n      default:\n        return brandShadows.apothecaryGlow;\n    }\n  };\n\n  const hoverShadow = addBrandShadow ? getShadow(shadowTheme) : brandShadows.medium;\n\n  return (\n    <motion.div\n      className={`image-zoom-container relative overflow-hidden rounded-lg ${className}`}\n      style={style}\n      whileHover={{\n        scale,\n        filter: `brightness(${brightness})`,\n        boxShadow: hoverShadow,\n      }}\n      transition={{\n        duration: duration / 1000,\n        ease: brandAnimations.easing.smooth,\n      }}\n    >\n      <Image\n        src={src}\n        alt={alt}\n        fill\n        className=\"object-cover\"\n        sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n      />\n    </motion.div>\n  );\n};\n\nexport default ImageZoomOnHover;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,mBAAoD,CAAC,EACzD,GAAG,EACH,GAAG,EACH,YAAY,EAAE,EACd,KAAK,EACL,QAAQ,IAAI,EACZ,aAAa,GAAG,EAChB,WAAW,GAAG,EACd,iBAAiB,IAAI,EACrB,cAAc,YAAY,EAC3B;IACC,4CAA4C;IAC5C,MAAM,YAAY,CAAC;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO,sHAAA,CAAA,eAAY,CAAC,cAAc;YACpC,KAAK;gBACH,OAAO,sHAAA,CAAA,eAAY,CAAC,QAAQ;YAC9B,KAAK;gBACH,OAAO,sHAAA,CAAA,eAAY,CAAC,MAAM;YAC5B;gBACE,OAAO,sHAAA,CAAA,eAAY,CAAC,cAAc;QACtC;IACF;IAEA,MAAM,cAAc,iBAAiB,UAAU,eAAe,sHAAA,CAAA,eAAY,CAAC,MAAM;IAEjF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,yDAAyD,EAAE,WAAW;QAClF,OAAO;QACP,YAAY;YACV;YACA,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACnC,WAAW;QACb;QACA,YAAY;YACV,UAAU,WAAW;YACrB,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;QACrC;kBAEA,cAAA,8OAAC,6HAAA,CAAA,UAAK;YACJ,KAAK;YACL,KAAK;YACL,IAAI;YACJ,WAAU;YACV,OAAM;;;;;;;;;;;AAId;uCAEe", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/SectionRevealWipe.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { SectionRevealWipeProps } from '../../types/visual';\nimport { brandGradients, brandAnimations } from '../../styles/brand';\n\n/**\n * SectionRevealWipe Component\n * \n * Delivers editorial polish to major page sections with a \"curtain wipe\" effect.\n * Features side-entrance animations with clip-path and gradient backgrounds.\n * \n * @example\n * ```tsx\n * <SectionRevealWipe \n *   direction=\"left-to-right\" \n *   gradientTheme=\"cannabis\"\n *   addEdgeAccents={true}\n * >\n *   <h2>Hero Section Content</h2>\n * </SectionRevealWipe>\n * ```\n */\nconst SectionRevealWipe: React.FC<SectionRevealWipeProps> = ({\n  children,\n  className = '',\n  style,\n  direction = 'left-to-right',\n  duration = 800,\n  delay = 0,\n  gradientTheme = 'cannabis',\n  addEdgeAccents = true,\n  trigger = 'scroll',\n}) => {\n  const [ref, inView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true,\n  });\n\n  const [isRevealed, setIsRevealed] = useState(trigger === 'load');\n\n  useEffect(() => {\n    if (trigger === 'scroll' && inView) {\n      setTimeout(() => setIsRevealed(true), delay);\n    } else if (trigger === 'load') {\n      setTimeout(() => setIsRevealed(true), delay);\n    }\n  }, [inView, trigger, delay]);\n\n  // Get clip-path values based on direction\n  const getClipPath = (revealed: boolean) => {\n    if (!revealed) {\n      switch (direction) {\n        case 'left-to-right':\n          return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';\n        case 'right-to-left':\n          return 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)';\n        case 'top-to-bottom':\n          return 'polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)';\n        case 'bottom-to-top':\n          return 'polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)';\n        default:\n          return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';\n      }\n    }\n    return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';\n  };\n\n  // Get background gradient\n  const getGradient = () => {\n    switch (gradientTheme) {\n      case 'cannabis':\n        return brandGradients.cannabis;\n      case 'gold':\n        return brandGradients.gold;\n      case 'sage':\n        return brandGradients.sage;\n      case 'hero':\n        return brandGradients.hero;\n      default:\n        return brandGradients.cannabis;\n    }\n  };\n\n  return (\n    <div\n      ref={ref}\n      className={`section-reveal-wipe relative overflow-hidden ${className}`}\n      style={style}\n    >\n      {/* Background with gradient */}\n      <motion.div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: getGradient(),\n          clipPath: getClipPath(isRevealed),\n        }}\n        animate={{\n          clipPath: getClipPath(isRevealed),\n        }}\n        transition={{\n          duration: duration / 1000,\n          ease: brandAnimations.easing.smooth,\n        }}\n      />\n\n      {/* Edge accents */}\n      {addEdgeAccents && (\n        <>\n          <motion.div\n            className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30\"\n            initial={{ scaleX: 0 }}\n            animate={{ scaleX: isRevealed ? 1 : 0 }}\n            transition={{\n              duration: duration / 1000,\n              delay: (delay + duration * 0.5) / 1000,\n              ease: brandAnimations.easing.smooth,\n            }}\n          />\n          <motion.div\n            className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30\"\n            initial={{ scaleX: 0 }}\n            animate={{ scaleX: isRevealed ? 1 : 0 }}\n            transition={{\n              duration: duration / 1000,\n              delay: (delay + duration * 0.5) / 1000,\n              ease: brandAnimations.easing.smooth,\n            }}\n          />\n        </>\n      )}\n\n      {/* Content */}\n      <motion.div\n        className=\"relative z-10\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: isRevealed ? 1 : 0 }}\n        transition={{\n          duration: (duration * 0.6) / 1000,\n          delay: (delay + duration * 0.4) / 1000,\n          ease: brandAnimations.easing.smooth,\n        }}\n      >\n        {children}\n      </motion.div>\n    </div>\n  );\n};\n\nexport default SectionRevealWipe;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,YAAY,eAAe,EAC3B,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,gBAAgB,UAAU,EAC1B,iBAAiB,IAAI,EACrB,UAAU,QAAQ,EACnB;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa;IACf;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,YAAY,QAAQ;YAClC,WAAW,IAAM,cAAc,OAAO;QACxC,OAAO,IAAI,YAAY,QAAQ;YAC7B,WAAW,IAAM,cAAc,OAAO;QACxC;IACF,GAAG;QAAC;QAAQ;QAAS;KAAM;IAE3B,0CAA0C;IAC1C,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,UAAU;YACb,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QACA,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO,sHAAA,CAAA,iBAAc,CAAC,QAAQ;YAChC,KAAK;gBACH,OAAO,sHAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,KAAK;gBACH,OAAO,sHAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B,KAAK;gBACH,OAAO,sHAAA,CAAA,iBAAc,CAAC,IAAI;YAC5B;gBACE,OAAO,sHAAA,CAAA,iBAAc,CAAC,QAAQ;QAClC;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6CAA6C,EAAE,WAAW;QACtE,OAAO;;0BAGP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,UAAU,YAAY;gBACxB;gBACA,SAAS;oBACP,UAAU,YAAY;gBACxB;gBACA,YAAY;oBACV,UAAU,WAAW;oBACrB,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACrC;;;;;;YAID,gCACC;;kCACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAE;wBACrB,SAAS;4BAAE,QAAQ,aAAa,IAAI;wBAAE;wBACtC,YAAY;4BACV,UAAU,WAAW;4BACrB,OAAO,CAAC,QAAQ,WAAW,GAAG,IAAI;4BAClC,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;wBACrC;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAE;wBACrB,SAAS;4BAAE,QAAQ,aAAa,IAAI;wBAAE;wBACtC,YAAY;4BACV,UAAU,WAAW;4BACrB,OAAO,CAAC,QAAQ,WAAW,GAAG,IAAI;4BAClC,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;wBACrC;;;;;;;;0BAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS,aAAa,IAAI;gBAAE;gBACvC,YAAY;oBACV,UAAU,AAAC,WAAW,MAAO;oBAC7B,OAAO,CAAC,QAAQ,WAAW,GAAG,IAAI;oBAClC,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;gBACrC;0BAEC;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/NoiseOverlayGrain.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { NoiseOverlayGrainProps } from '../../types/visual';\nimport { brandColors } from '../../styles/brand';\n\n/**\n * NoiseOverlayGrain Component\n * \n * Adds subtle texture and analog warmth to full-page backgrounds.\n * Features animated film-grain texture with mint-green tint.\n * \n * @example\n * ```tsx\n * <div className=\"hero-section relative\">\n *   <NoiseOverlayGrain opacity={0.05} tintColor=\"#2FB886\" />\n *   <h1>Hero Content</h1>\n * </div>\n * ```\n */\nconst NoiseOverlayGrain: React.FC<NoiseOverlayGrainProps> = ({\n  className = '',\n  style,\n  opacity = 0.04,\n  blendMode = 'overlay',\n  animationSpeed = 3000,\n  tintColor = brandColors.apothecary,\n  grainSize = 'medium',\n}) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number>();\n  const [isClient, setIsClient] = useState(false);\n\n  // Ensure we're on the client side to prevent hydration mismatches\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Generate noise pattern\n  const generateNoise = (ctx: CanvasRenderingContext2D, width: number, height: number) => {\n    const imageData = ctx.createImageData(width, height);\n    const data = imageData.data;\n\n    // Convert tint color to RGB\n    const tintRGB = hexToRgb(tintColor);\n\n    for (let i = 0; i < data.length; i += 4) {\n      const noise = Math.random() * 255;\n      \n      // Apply tint color\n      data[i] = Math.min(255, noise + tintRGB.r * 0.1);     // Red\n      data[i + 1] = Math.min(255, noise + tintRGB.g * 0.1); // Green\n      data[i + 2] = Math.min(255, noise + tintRGB.b * 0.1); // Blue\n      data[i + 3] = noise * opacity * 255;                  // Alpha\n    }\n\n    ctx.putImageData(imageData, 0, 0);\n  };\n\n  // Convert hex color to RGB\n  const hexToRgb = (hex: string) => {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : { r: 47, g: 184, b: 134 }; // Default to apothecary green\n  };\n\n  // Get grain size multiplier\n  const getGrainSizeMultiplier = () => {\n    switch (grainSize) {\n      case 'small':\n        return 0.5;\n      case 'medium':\n        return 1;\n      case 'large':\n        return 2;\n      default:\n        return 1;\n    }\n  };\n\n  // Animation loop\n  const animate = () => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Clear canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Generate new noise pattern\n    generateNoise(ctx, canvas.width, canvas.height);\n\n    // Schedule next frame\n    animationRef.current = setTimeout(() => {\n      requestAnimationFrame(animate);\n    }, animationSpeed);\n  };\n\n  // Setup canvas and start animation\n  useEffect(() => {\n    if (!isClient) return; // Only run on client side\n\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    // Set canvas size based on grain size\n    const sizeMultiplier = getGrainSizeMultiplier();\n    canvas.width = Math.floor(window.innerWidth * sizeMultiplier);\n    canvas.height = Math.floor(window.innerHeight * sizeMultiplier);\n\n    // Start animation\n    animate();\n\n    // Handle window resize\n    const handleResize = () => {\n      canvas.width = Math.floor(window.innerWidth * sizeMultiplier);\n      canvas.height = Math.floor(window.innerHeight * sizeMultiplier);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    // Cleanup\n    return () => {\n      if (animationRef.current) {\n        clearTimeout(animationRef.current);\n      }\n      window.removeEventListener('resize', handleResize);\n    };\n  }, [isClient, animationSpeed, grainSize, tintColor, opacity]);\n\n  // Don't render anything on server side to prevent hydration mismatch\n  if (!isClient) {\n    return null;\n  }\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`noise-overlay-grain absolute inset-0 pointer-events-none ${className}`}\n      style={{\n        mixBlendMode: blendMode,\n        opacity,\n        zIndex: 1,\n        ...style,\n      }}\n    />\n  );\n};\n\nexport default NoiseOverlayGrain;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMA;;;;;;;;;;;;;CAaC,GACD,MAAM,oBAAsD,CAAC,EAC3D,YAAY,EAAE,EACd,KAAK,EACL,UAAU,IAAI,EACd,YAAY,SAAS,EACrB,iBAAiB,IAAI,EACrB,YAAY,sHAAA,CAAA,cAAW,CAAC,UAAU,EAClC,YAAY,QAAQ,EACrB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,gBAAgB,CAAC,KAA+B,OAAe;QACnE,MAAM,YAAY,IAAI,eAAe,CAAC,OAAO;QAC7C,MAAM,OAAO,UAAU,IAAI;QAE3B,4BAA4B;QAC5B,MAAM,UAAU,SAAS;QAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,QAAQ,KAAK,MAAM,KAAK;YAE9B,mBAAmB;YACnB,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG,MAAU,MAAM;YAC5D,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG,MAAM,QAAQ;YAC9D,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG,MAAM,OAAO;YAC7D,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,UAAU,KAAsB,QAAQ;QAChE;QAEA,IAAI,YAAY,CAAC,WAAW,GAAG;IACjC;IAEA,2BAA2B;IAC3B,MAAM,WAAW,CAAC;QAChB,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SAAS;YACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI;YAAE,GAAG;YAAI,GAAG;YAAK,GAAG;QAAI,GAAG,8BAA8B;IAC/D;IAEA,4BAA4B;IAC5B,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,iBAAiB;IACjB,MAAM,UAAU;QACd,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,eAAe;QACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,6BAA6B;QAC7B,cAAc,KAAK,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,sBAAsB;QACtB,aAAa,OAAO,GAAG,WAAW;YAChC,sBAAsB;QACxB,GAAG;IACL;IAEA,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,0BAA0B;QAEjD,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,sCAAsC;QACtC,MAAM,iBAAiB;QACvB,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;QAC9C,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,OAAO,WAAW,GAAG;QAEhD,kBAAkB;QAClB;QAEA,uBAAuB;QACvB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;YAC9C,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,OAAO,WAAW,GAAG;QAClD;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,aAAa,OAAO;YACnC;YACA,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;QAAU;QAAgB;QAAW;QAAW;KAAQ;IAE5D,qEAAqE;IACrE,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,WAAW;QAClF,OAAO;YACL,cAAc;YACd;YACA,QAAQ;YACR,GAAG,KAAK;QACV;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/DynamicGradientText.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { DynamicGradientTextProps } from '../../types/visual';\nimport { terpeneColors, brandTypography, brandAnimations } from '../../styles/brand';\n\n/**\n * DynamicGradientText Component\n * \n * Draws attention to headlines using animated gradient fills.\n * Features terpene-themed gradients with background-clip: text.\n * \n * @example\n * ```tsx\n * <DynamicGradientText \n *   text=\"Premium Cannabis Extracts\" \n *   theme=\"rosin\"\n *   fontSize=\"3rem\"\n *   animate={true}\n * />\n * ```\n */\nconst DynamicGradientText: React.FC<DynamicGradientTextProps> = ({\n  text,\n  className = '',\n  style,\n  theme = 'pineapple',\n  customGradient,\n  animationSpeed = 3000,\n  fontSize = '2rem',\n  fontWeight = 700,\n  animate = true,\n}) => {\n  const [gradientPosition, setGradientPosition] = useState(0);\n\n  // Get gradient based on theme or custom gradient\n  const getGradient = () => {\n    if (customGradient) return customGradient;\n    \n    const terpene = terpeneColors[theme];\n    if (!terpene) return terpeneColors.pineapple.gradient;\n    \n    return terpene.gradient;\n  };\n\n  // Create animated gradient with moving position\n  const getAnimatedGradient = () => {\n    if (!animate) return getGradient();\n    \n    const baseGradient = getGradient();\n    \n    // Extract colors from gradient and create animated version\n    if (theme === 'pineapple') {\n      return `linear-gradient(45deg, \n        #ff8c00 ${gradientPosition - 50}%, \n        #ffd700 ${gradientPosition}%, \n        #ff8c00 ${gradientPosition + 50}%)`;\n    } else if (theme === 'gmo') {\n      return `linear-gradient(45deg, \n        #4a90e2 ${gradientPosition - 50}%, \n        #87ceeb ${gradientPosition}%, \n        #4a90e2 ${gradientPosition + 50}%)`;\n    } else if (theme === 'rosin') {\n      return `linear-gradient(45deg, \n        #d4a574 ${gradientPosition - 50}%, \n        #ff8c00 ${gradientPosition}%, \n        #d4a574 ${gradientPosition + 50}%)`;\n    } else if (theme === 'flower') {\n      return `linear-gradient(45deg, \n        #359a5a ${gradientPosition - 50}%, \n        #ffd700 ${gradientPosition}%, \n        #359a5a ${gradientPosition + 50}%)`;\n    } else if (theme === 'extract') {\n      return `linear-gradient(45deg, \n        #ff8c00 ${gradientPosition - 50}%, \n        #9370db ${gradientPosition}%, \n        #ff8c00 ${gradientPosition + 50}%)`;\n    }\n    \n    return baseGradient;\n  };\n\n  // Animate gradient position\n  useEffect(() => {\n    if (!animate) return;\n\n    const interval = setInterval(() => {\n      setGradientPosition(prev => (prev + 1) % 200);\n    }, animationSpeed / 200);\n\n    return () => clearInterval(interval);\n  }, [animate, animationSpeed]);\n\n  return (\n    <motion.h1\n      className={`dynamic-gradient-text ${className}`}\n      style={{\n        backgroundImage: getAnimatedGradient(),\n        backgroundClip: 'text',\n        WebkitBackgroundClip: 'text',\n        color: 'transparent',\n        fontSize,\n        fontWeight,\n        fontFamily: brandTypography.fonts.serif,\n        backgroundSize: '200% 200%',\n        display: 'inline-block',\n        lineHeight: 1.2,\n        ...style,\n      }}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.6, ease: brandAnimations.easing.smooth }}\n    >\n      {text}\n    </motion.h1>\n  );\n};\n\nexport default DynamicGradientText;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,YAAY,EAAE,EACd,KAAK,EACL,QAAQ,WAAW,EACnB,cAAc,EACd,iBAAiB,IAAI,EACrB,WAAW,MAAM,EACjB,aAAa,GAAG,EAChB,UAAU,IAAI,EACf;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,iDAAiD;IACjD,MAAM,cAAc;QAClB,IAAI,gBAAgB,OAAO;QAE3B,MAAM,UAAU,sHAAA,CAAA,gBAAa,CAAC,MAAM;QACpC,IAAI,CAAC,SAAS,OAAO,sHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,QAAQ;QAErD,OAAO,QAAQ,QAAQ;IACzB;IAEA,gDAAgD;IAChD,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,eAAe;QAErB,2DAA2D;QAC3D,IAAI,UAAU,aAAa;YACzB,OAAO,CAAC;gBACE,EAAE,mBAAmB,GAAG;gBACxB,EAAE,iBAAiB;gBACnB,EAAE,mBAAmB,GAAG,EAAE,CAAC;QACvC,OAAO,IAAI,UAAU,OAAO;YAC1B,OAAO,CAAC;gBACE,EAAE,mBAAmB,GAAG;gBACxB,EAAE,iBAAiB;gBACnB,EAAE,mBAAmB,GAAG,EAAE,CAAC;QACvC,OAAO,IAAI,UAAU,SAAS;YAC5B,OAAO,CAAC;gBACE,EAAE,mBAAmB,GAAG;gBACxB,EAAE,iBAAiB;gBACnB,EAAE,mBAAmB,GAAG,EAAE,CAAC;QACvC,OAAO,IAAI,UAAU,UAAU;YAC7B,OAAO,CAAC;gBACE,EAAE,mBAAmB,GAAG;gBACxB,EAAE,iBAAiB;gBACnB,EAAE,mBAAmB,GAAG,EAAE,CAAC;QACvC,OAAO,IAAI,UAAU,WAAW;YAC9B,OAAO,CAAC;gBACE,EAAE,mBAAmB,GAAG;gBACxB,EAAE,iBAAiB;gBACnB,EAAE,mBAAmB,GAAG,EAAE,CAAC;QACvC;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,WAAW,YAAY;YAC3B,oBAAoB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;QAC3C,GAAG,iBAAiB;QAEpB,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAS;KAAe;IAE5B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;QACR,WAAW,CAAC,sBAAsB,EAAE,WAAW;QAC/C,OAAO;YACL,iBAAiB;YACjB,gBAAgB;YAChB,sBAAsB;YACtB,OAAO;YACP;YACA;YACA,YAAY,sHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,KAAK;YACvC,gBAAgB;YAChB,SAAS;YACT,YAAY;YACZ,GAAG,KAAK;QACV;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;QAAC;kBAEhE;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/Tilt3DCardEffect.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tilt3DCardEffectProps } from '../../types/visual';\nimport { brandShadows, brandAnimations } from '../../styles/brand';\n\n/**\n * Tilt3DCardEffect Component\n * \n * Increases interactivity and realism for cards or strain showcases.\n * Features 3D tilt effects based on mouse position with dynamic shadows.\n * \n * @example\n * ```tsx\n * <Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>\n *   <div className=\"strain-card\">\n *     <h3>Blue Dream</h3>\n *     <p>Premium Sativa Hybrid</p>\n *   </div>\n * </Tilt3DCardEffect>\n * ```\n */\nconst Tilt3DCardEffect: React.FC<Tilt3DCardEffectProps> = ({\n  children,\n  className = '',\n  style,\n  maxTilt = 10,\n  perspective = 1000,\n  scale = 1.02,\n  speed = 300,\n  addDynamicShadow = true,\n  reset = true,\n}) => {\n  const cardRef = useRef<HTMLDivElement>(null);\n  const [tiltValues, setTiltValues] = useState({ rotateX: 0, rotateY: 0 });\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Calculate tilt based on mouse position\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    if (!cardRef.current) return;\n\n    const card = cardRef.current;\n    const rect = card.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n\n    // Calculate mouse position relative to card center\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Calculate tilt angles (inverted for natural feel)\n    const rotateY = (mouseX / (rect.width / 2)) * maxTilt;\n    const rotateX = -(mouseY / (rect.height / 2)) * maxTilt;\n\n    setTiltValues({ rotateX, rotateY });\n  };\n\n  // Reset tilt on mouse leave\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n    if (reset) {\n      setTiltValues({ rotateX: 0, rotateY: 0 });\n    }\n  };\n\n  // Set hover state on mouse enter\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n  };\n\n  // Get dynamic shadow based on tilt direction\n  const getDynamicShadow = () => {\n    if (!addDynamicShadow || !isHovered) return brandShadows.medium;\n\n    const { rotateX, rotateY } = tiltValues;\n    \n    // Calculate shadow offset based on tilt\n    const shadowX = rotateY * 0.5;\n    const shadowY = rotateX * 0.5;\n    const shadowBlur = Math.abs(rotateX) + Math.abs(rotateY) + 10;\n    \n    return `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, 0.2)`;\n  };\n\n  return (\n    <motion.div\n      ref={cardRef}\n      className={`tilt-3d-card ${className}`}\n      style={{\n        perspective: `${perspective}px`,\n        transformStyle: 'preserve-3d',\n        ...style,\n      }}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      animate={{\n        rotateX: tiltValues.rotateX,\n        rotateY: tiltValues.rotateY,\n        scale: isHovered ? scale : 1,\n        boxShadow: getDynamicShadow(),\n      }}\n      transition={{\n        duration: speed / 1000,\n        ease: brandAnimations.easing.smooth,\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport default Tilt3DCardEffect;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,UAAU,EAAE,EACZ,cAAc,IAAI,EAClB,QAAQ,IAAI,EACZ,QAAQ,GAAG,EACX,mBAAmB,IAAI,EACvB,QAAQ,IAAI,EACb;IACC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,SAAS;QAAG,SAAS;IAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yCAAyC;IACzC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,OAAO,QAAQ,OAAO;QAC5B,MAAM,OAAO,KAAK,qBAAqB;QACvC,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,mDAAmD;QACnD,MAAM,SAAS,EAAE,OAAO,GAAG;QAC3B,MAAM,SAAS,EAAE,OAAO,GAAG;QAE3B,oDAAoD;QACpD,MAAM,UAAU,AAAC,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;QAC9C,MAAM,UAAU,CAAC,CAAC,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI;QAEhD,cAAc;YAAE;YAAS;QAAQ;IACnC;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,aAAa;QACb,IAAI,OAAO;YACT,cAAc;gBAAE,SAAS;gBAAG,SAAS;YAAE;QACzC;IACF;IAEA,iCAAiC;IACjC,MAAM,mBAAmB;QACvB,aAAa;IACf;IAEA,6CAA6C;IAC7C,MAAM,mBAAmB;QACvB,IAAI,CAAC,oBAAoB,CAAC,WAAW,OAAO,sHAAA,CAAA,eAAY,CAAC,MAAM;QAE/D,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,wCAAwC;QACxC,MAAM,UAAU,UAAU;QAC1B,MAAM,UAAU,UAAU;QAC1B,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW;QAE3D,OAAO,GAAG,QAAQ,GAAG,EAAE,QAAQ,GAAG,EAAE,WAAW,qBAAqB,CAAC;IACvE;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAC,aAAa,EAAE,WAAW;QACtC,OAAO;YACL,aAAa,GAAG,YAAY,EAAE,CAAC;YAC/B,gBAAgB;YAChB,GAAG,KAAK;QACV;QACA,aAAa;QACb,cAAc;QACd,cAAc;QACd,SAAS;YACP,SAAS,WAAW,OAAO;YAC3B,SAAS,WAAW,OAAO;YAC3B,OAAO,YAAY,QAAQ;YAC3B,WAAW;QACb;QACA,YAAY;YACV,UAAU,QAAQ;YAClB,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;QACrC;kBAEC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/TypewriterIntroText.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TypewriterIntroTextProps } from '../../types/visual';\nimport { brandColors, brandTypography, brandAnimations } from '../../styles/brand';\n\n/**\n * TypewriterIntroText Component\n * \n * Adds nostalgic, cinematic intros to landing pages.\n * Features letter-by-letter typing with blinking cursor.\n * \n * @example\n * ```tsx\n * <TypewriterIntroText \n *   text={[\"Welcome to\", \"Apothecary Farms\", \"Premium Cannabis Extracts\"]}\n *   charDelay={50}\n *   cursor=\"✨\"\n *   loop={false}\n * />\n * ```\n */\nconst TypewriterIntroText: React.FC<TypewriterIntroTextProps> = ({\n  text,\n  className = '',\n  style,\n  charDelay = 50,\n  wordDelay = 1000,\n  cursor = '|',\n  cursorBlinkSpeed = 1000,\n  loop = false,\n  startDelay = 0,\n  fontFamily = 'serif',\n}) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [currentTextIndex, setCurrentTextIndex] = useState(0);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showCursor, setShowCursor] = useState(true);\n  const [isClient, setIsClient] = useState(false);\n\n  // Ensure we're on the client side to prevent hydration mismatches\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Convert text to array if it's a string\n  const textArray = Array.isArray(text) ? text : [text];\n\n  // Start typing animation\n  useEffect(() => {\n    if (!isClient) return; // Only run on client side\n\n    const startTyping = setTimeout(() => {\n      setIsTyping(true);\n    }, startDelay);\n\n    return () => clearTimeout(startTyping);\n  }, [isClient, startDelay]);\n\n  // Typing animation logic\n  useEffect(() => {\n    if (!isTyping) return;\n\n    const currentText = textArray[currentTextIndex];\n    \n    if (currentIndex < currentText.length) {\n      // Type next character\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => prev + currentText[currentIndex]);\n        setCurrentIndex(prev => prev + 1);\n      }, charDelay);\n\n      return () => clearTimeout(timeout);\n    } else {\n      // Finished typing current text\n      if (currentTextIndex < textArray.length - 1) {\n        // Move to next text after word delay\n        const timeout = setTimeout(() => {\n          setDisplayText('');\n          setCurrentIndex(0);\n          setCurrentTextIndex(prev => prev + 1);\n        }, wordDelay);\n\n        return () => clearTimeout(timeout);\n      } else if (loop) {\n        // Restart from beginning if looping\n        const timeout = setTimeout(() => {\n          setDisplayText('');\n          setCurrentIndex(0);\n          setCurrentTextIndex(0);\n        }, wordDelay);\n\n        return () => clearTimeout(timeout);\n      } else {\n        // Stop typing\n        setIsTyping(false);\n      }\n    }\n  }, [currentIndex, currentTextIndex, isTyping, textArray, charDelay, wordDelay, loop]);\n\n  // Cursor blinking animation\n  useEffect(() => {\n    if (!isClient) return; // Only run on client side\n\n    const interval = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, cursorBlinkSpeed);\n\n    return () => clearInterval(interval);\n  }, [isClient, cursorBlinkSpeed]);\n\n  // Get font family\n  const getFontFamily = () => {\n    return fontFamily === 'serif' ? brandTypography.fonts.serif : brandTypography.fonts.sans;\n  };\n\n  // Don't render anything on server side to prevent hydration mismatch\n  if (!isClient) {\n    return (\n      <div\n        className={`typewriter-intro-text ${className}`}\n        style={{\n          fontFamily: getFontFamily(),\n          ...style,\n        }}\n      >\n        {/* Show static text on server side */}\n        <span>{textArray[0]}</span>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={`typewriter-intro-text ${className}`}\n      style={{\n        fontFamily: getFontFamily(),\n        ...style,\n      }}\n    >\n      <motion.span\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        {displayText}\n      </motion.span>\n\n      <AnimatePresence>\n        {(isTyping || showCursor) && (\n          <motion.span\n            className=\"typewriter-cursor\"\n            style={{\n              color: brandColors.apothecary,\n              marginLeft: '2px',\n            }}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: showCursor ? 1 : 0 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.1 }}\n          >\n            {cursor}\n          </motion.span>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default TypewriterIntroText;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AALA;;;;;AAOA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,YAAY,EAAE,EACd,KAAK,EACL,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,SAAS,GAAG,EACZ,mBAAmB,IAAI,EACvB,OAAO,KAAK,EACZ,aAAa,CAAC,EACd,aAAa,OAAO,EACrB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK;IAErD,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,0BAA0B;QAEjD,MAAM,cAAc,WAAW;YAC7B,YAAY;QACd,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAW;IAEzB,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,cAAc,SAAS,CAAC,iBAAiB;QAE/C,IAAI,eAAe,YAAY,MAAM,EAAE;YACrC,sBAAsB;YACtB,MAAM,UAAU,WAAW;gBACzB,eAAe,CAAA,OAAQ,OAAO,WAAW,CAAC,aAAa;gBACvD,gBAAgB,CAAA,OAAQ,OAAO;YACjC,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO;YACL,+BAA+B;YAC/B,IAAI,mBAAmB,UAAU,MAAM,GAAG,GAAG;gBAC3C,qCAAqC;gBACrC,MAAM,UAAU,WAAW;oBACzB,eAAe;oBACf,gBAAgB;oBAChB,oBAAoB,CAAA,OAAQ,OAAO;gBACrC,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B,OAAO,IAAI,MAAM;gBACf,oCAAoC;gBACpC,MAAM,UAAU,WAAW;oBACzB,eAAe;oBACf,gBAAgB;oBAChB,oBAAoB;gBACtB,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B,OAAO;gBACL,cAAc;gBACd,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAc;QAAkB;QAAU;QAAW;QAAW;QAAW;KAAK;IAEpF,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,0BAA0B;QAEjD,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAA,OAAQ,CAAC;QACzB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAU;KAAiB;IAE/B,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,OAAO,eAAe,UAAU,sHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,KAAK,GAAG,sHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,IAAI;IAC1F;IAEA,qEAAqE;IACrE,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YACC,WAAW,CAAC,sBAAsB,EAAE,WAAW;YAC/C,OAAO;gBACL,YAAY;gBACZ,GAAG,KAAK;YACV;sBAGA,cAAA,8OAAC;0BAAM,SAAS,CAAC,EAAE;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,sBAAsB,EAAE,WAAW;QAC/C,OAAO;YACL,YAAY;YACZ,GAAG,KAAK;QACV;;0BAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE3B;;;;;;0BAGH,8OAAC,yLAAA,CAAA,kBAAe;0BACb,CAAC,YAAY,UAAU,mBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,OAAO;wBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,UAAU;wBAC7B,YAAY;oBACd;oBACA,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS,aAAa,IAAI;oBAAE;oBACvC,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE3B;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 1427, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/ParticleCanvasBackground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useCallback, useState } from 'react';\nimport { ParticleCanvasBackgroundProps } from '../../types/visual';\nimport { terpeneColors } from '../../styles/brand';\n\n/**\n * ParticleCanvasBackground Component\n * \n * Creates immersive, responsive backgrounds for hero sections.\n * Features floating, interactive particles that react to cursor proximity.\n * \n * @example\n * ```tsx\n * <div className=\"hero-section relative\">\n *   <ParticleCanvasBackground \n *     particleCount={100}\n *     colors={['#fff9c4', '#d4edda', '#e2d5f1']}\n *     interactive={true}\n *   />\n *   <h1>Hero Content</h1>\n * </div>\n * ```\n */\nconst ParticleCanvasBackground: React.FC<ParticleCanvasBackgroundProps> = ({\n  className = '',\n  style,\n  particleCount = 80,\n  colors = [terpeneColors.particles.yellow, terpeneColors.particles.lime, terpeneColors.particles.lavender],\n  sizeRange = [1, 3],\n  speed = 0.5,\n  connectionDistance = 100,\n  interactive = true,\n  repulsionDistance = 100,\n  attractionStrength = 0.02,\n}) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number>();\n  const mouseRef = useRef({ x: 0, y: 0 });\n  const particlesRef = useRef<Particle[]>([]);\n  const [isClient, setIsClient] = useState(false);\n\n  // Ensure we're on the client side to prevent hydration mismatches\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Particle class\n  class Particle {\n    x: number;\n    y: number;\n    vx: number;\n    vy: number;\n    size: number;\n    color: string;\n    originalX: number;\n    originalY: number;\n\n    constructor(width: number, height: number) {\n      this.x = Math.random() * width;\n      this.y = Math.random() * height;\n      this.originalX = this.x;\n      this.originalY = this.y;\n      this.vx = (Math.random() - 0.5) * speed;\n      this.vy = (Math.random() - 0.5) * speed;\n      this.size = Math.random() * (sizeRange[1] - sizeRange[0]) + sizeRange[0];\n      this.color = colors[Math.floor(Math.random() * colors.length)];\n    }\n\n    update(width: number, height: number, mouseX: number, mouseY: number) {\n      // Calculate distance to mouse\n      const dx = mouseX - this.x;\n      const dy = mouseY - this.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n\n      if (interactive && distance < repulsionDistance) {\n        // Repulsion from mouse\n        const force = (repulsionDistance - distance) / repulsionDistance;\n        this.vx -= (dx / distance) * force * attractionStrength;\n        this.vy -= (dy / distance) * force * attractionStrength;\n      }\n\n      // Update position\n      this.x += this.vx;\n      this.y += this.vy;\n\n      // Boundary collision\n      if (this.x < 0 || this.x > width) this.vx *= -1;\n      if (this.y < 0 || this.y > height) this.vy *= -1;\n\n      // Keep particles within bounds\n      this.x = Math.max(0, Math.min(width, this.x));\n      this.y = Math.max(0, Math.min(height, this.y));\n\n      // Apply friction\n      this.vx *= 0.99;\n      this.vy *= 0.99;\n    }\n\n    draw(ctx: CanvasRenderingContext2D) {\n      ctx.beginPath();\n      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n      ctx.fillStyle = this.color;\n      ctx.fill();\n    }\n  }\n\n  // Initialize particles\n  const initParticles = useCallback((width: number, height: number) => {\n    particlesRef.current = [];\n    for (let i = 0; i < particleCount; i++) {\n      particlesRef.current.push(new Particle(width, height));\n    }\n  }, [particleCount]);\n\n  // Draw connections between nearby particles\n  const drawConnections = (ctx: CanvasRenderingContext2D) => {\n    const particles = particlesRef.current;\n    \n    for (let i = 0; i < particles.length; i++) {\n      for (let j = i + 1; j < particles.length; j++) {\n        const dx = particles[i].x - particles[j].x;\n        const dy = particles[i].y - particles[j].y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n\n        if (distance < connectionDistance) {\n          const opacity = 1 - (distance / connectionDistance);\n          ctx.beginPath();\n          ctx.moveTo(particles[i].x, particles[i].y);\n          ctx.lineTo(particles[j].x, particles[j].y);\n          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.2})`;\n          ctx.lineWidth = 0.5;\n          ctx.stroke();\n        }\n      }\n    }\n  };\n\n  // Animation loop\n  const animate = useCallback(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Clear canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Update and draw particles\n    particlesRef.current.forEach(particle => {\n      particle.update(canvas.width, canvas.height, mouseRef.current.x, mouseRef.current.y);\n      particle.draw(ctx);\n    });\n\n    // Draw connections\n    drawConnections(ctx);\n\n    animationRef.current = requestAnimationFrame(animate);\n  }, []);\n\n  // Handle mouse movement\n  const handleMouseMove = useCallback((e: MouseEvent) => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const rect = canvas.getBoundingClientRect();\n    mouseRef.current.x = e.clientX - rect.left;\n    mouseRef.current.y = e.clientY - rect.top;\n  }, []);\n\n  // Setup canvas and start animation\n  useEffect(() => {\n    if (!isClient) return; // Only run on client side\n\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n      initParticles(canvas.width, canvas.height);\n    };\n\n    resizeCanvas();\n    animate();\n\n    // Event listeners\n    window.addEventListener('resize', resizeCanvas);\n    if (interactive) {\n      window.addEventListener('mousemove', handleMouseMove);\n    }\n\n    // Cleanup\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      window.removeEventListener('resize', resizeCanvas);\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [isClient, animate, handleMouseMove, initParticles, interactive]);\n\n  // Don't render anything on server side to prevent hydration mismatch\n  if (!isClient) {\n    return null;\n  }\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`particle-canvas-background absolute inset-0 pointer-events-none ${className}`}\n      style={{\n        zIndex: 0,\n        ...style,\n      }}\n    />\n  );\n};\n\nexport default ParticleCanvasBackground;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMA;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,2BAAoE,CAAC,EACzE,YAAY,EAAE,EACd,KAAK,EACL,gBAAgB,EAAE,EAClB,SAAS;IAAC,sHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM;IAAE,sHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI;IAAE,sHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,QAAQ;CAAC,EACzG,YAAY;IAAC;IAAG;CAAE,EAClB,QAAQ,GAAG,EACX,qBAAqB,GAAG,EACxB,cAAc,IAAI,EAClB,oBAAoB,GAAG,EACvB,qBAAqB,IAAI,EAC1B;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM;QACJ,EAAU;QACV,EAAU;QACV,GAAW;QACX,GAAW;QACX,KAAa;QACb,MAAc;QACd,UAAkB;QAClB,UAAkB;QAElB,YAAY,KAAa,EAAE,MAAc,CAAE;YACzC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK;YACzB,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK;YACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAClC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAClC,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;YACxE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;QAChE;QAEA,OAAO,KAAa,EAAE,MAAc,EAAE,MAAc,EAAE,MAAc,EAAE;YACpE,8BAA8B;YAC9B,MAAM,KAAK,SAAS,IAAI,CAAC,CAAC;YAC1B,MAAM,KAAK,SAAS,IAAI,CAAC,CAAC;YAC1B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;YAE1C,IAAI,eAAe,WAAW,mBAAmB;gBAC/C,uBAAuB;gBACvB,MAAM,QAAQ,CAAC,oBAAoB,QAAQ,IAAI;gBAC/C,IAAI,CAAC,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;gBACrC,IAAI,CAAC,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;YACvC;YAEA,kBAAkB;YAClB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;YAEjB,qBAAqB;YACrB,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC;YAC9C,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC;YAE/C,+BAA+B;YAC/B,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC;YAE5C,iBAAiB;YACjB,IAAI,CAAC,EAAE,IAAI;YACX,IAAI,CAAC,EAAE,IAAI;QACb;QAEA,KAAK,GAA6B,EAAE;YAClC,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;YAChD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK;YAC1B,IAAI,IAAI;QACV;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAChD,aAAa,OAAO,GAAG,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,aAAa,OAAO,CAAC,IAAI,CAAC,IAAI,SAAS,OAAO;QAChD;IACF,GAAG;QAAC;KAAc;IAElB,4CAA4C;IAC5C,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,aAAa,OAAO;QAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBAC7C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC1C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC1C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;gBAE1C,IAAI,WAAW,oBAAoB;oBACjC,MAAM,UAAU,IAAK,WAAW;oBAChC,IAAI,SAAS;oBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;oBACzC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;oBACzC,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,UAAU,IAAI,CAAC,CAAC;oBACzD,IAAI,SAAS,GAAG;oBAChB,IAAI,MAAM;gBACZ;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,eAAe;QACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,4BAA4B;QAC5B,aAAa,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3B,SAAS,MAAM,CAAC,OAAO,KAAK,EAAE,OAAO,MAAM,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,CAAC;YACnF,SAAS,IAAI,CAAC;QAChB;QAEA,mBAAmB;QACnB,gBAAgB;QAEhB,aAAa,OAAO,GAAG,sBAAsB;IAC/C,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,OAAO,OAAO,qBAAqB;QACzC,SAAS,OAAO,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,KAAK,IAAI;QAC1C,SAAS,OAAO,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,KAAK,GAAG;IAC3C,GAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,0BAA0B;QAEjD,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,kBAAkB;QAClB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;YAClC,cAAc,OAAO,KAAK,EAAE,OAAO,MAAM;QAC3C;QAEA;QACA;QAEA,kBAAkB;QAClB,OAAO,gBAAgB,CAAC,UAAU;QAClC,IAAI,aAAa;YACf,OAAO,gBAAgB,CAAC,aAAa;QACvC;QAEA,UAAU;QACV,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;YACA,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,aAAa;QAC1C;IACF,GAAG;QAAC;QAAU;QAAS;QAAiB;QAAe;KAAY;IAEnE,qEAAqE;IACrE,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,gEAAgE,EAAE,WAAW;QACzF,OAAO;YACL,QAAQ;YACR,GAAG,KAAK;QACV;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/LottieIconAnimations.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { LottieIconAnimationsProps } from '../../types/visual';\nimport { brandColors, brandShadows } from '../../styles/brand';\n\n/**\n * LottieIconAnimations Component\n * \n * Makes key icons come alive via smooth, relevant microinteractions.\n * Features animated icons for extraction tech, flower types, and product categories.\n * \n * Note: This is a placeholder implementation. In production, you would use:\n * import Lottie from 'react-lottie-player'\n * \n * @example\n * ```tsx\n * <LottieIconAnimations \n *   animationData={beakerAnimation}\n *   trigger=\"hover\"\n *   category=\"beaker\"\n *   width={64}\n *   height={64}\n * />\n * ```\n */\nconst LottieIconAnimations: React.FC<LottieIconAnimationsProps> = ({\n  animationData,\n  className = '',\n  style,\n  trigger = 'hover',\n  loop = true,\n  speed = 1,\n  width = 48,\n  height = 48,\n  category = 'beaker',\n}) => {\n  const [isPlaying, setIsPlaying] = useState(trigger === 'load');\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Get category-specific styling\n  const getCategoryStyles = () => {\n    switch (category) {\n      case 'beaker':\n        return {\n          color: brandColors.primary[600],\n          glow: brandShadows.apothecaryGlow,\n        };\n      case 'trichome':\n        return {\n          color: brandColors.gold[400],\n          glow: brandShadows.goldGlow,\n        };\n      case 'flame':\n        return {\n          color: '#ff8c00',\n          glow: '0 0 20px rgba(255, 140, 0, 0.3)',\n        };\n      case 'extraction':\n        return {\n          color: brandColors.sage[500],\n          glow: brandShadows.medium,\n        };\n      case 'flower':\n        return {\n          color: brandColors.primary[500],\n          glow: brandShadows.apothecaryGlow,\n        };\n      case 'product':\n        return {\n          color: brandColors.charcoal[600],\n          glow: brandShadows.medium,\n        };\n      default:\n        return {\n          color: brandColors.primary[600],\n          glow: brandShadows.apothecaryGlow,\n        };\n    }\n  };\n\n  const categoryStyles = getCategoryStyles();\n\n  // Handle animation triggers\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    if (trigger === 'hover') {\n      setIsPlaying(true);\n    }\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n    if (trigger === 'hover' && !loop) {\n      setIsPlaying(false);\n    }\n  };\n\n  const handleClick = () => {\n    if (trigger === 'click') {\n      setIsPlaying(!isPlaying);\n    }\n  };\n\n  // Placeholder SVG icons for different categories\n  const getPlaceholderIcon = () => {\n    switch (category) {\n      case 'beaker':\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <path d=\"M9 2v6.5L4.5 14c-.83 1.24-.5 2.91.74 3.74.45.3.96.45 1.51.45h9.5c.55 0 1.05-.15 1.5-.45 1.24-.83 1.57-2.5.74-3.74L13 8.5V2h-4zm2 2h2v5.5l4.5 5.5H6.5L11 9.5V4z\"/>\n          </svg>\n        );\n      case 'trichome':\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <circle cx=\"12\" cy=\"12\" r=\"3\"/>\n            <circle cx=\"6\" cy=\"6\" r=\"2\"/>\n            <circle cx=\"18\" cy=\"6\" r=\"2\"/>\n            <circle cx=\"6\" cy=\"18\" r=\"2\"/>\n            <circle cx=\"18\" cy=\"18\" r=\"2\"/>\n            <line x1=\"12\" y1=\"9\" x2=\"12\" y2=\"15\"/>\n            <line x1=\"9\" y1=\"12\" x2=\"15\" y2=\"12\"/>\n          </svg>\n        );\n      case 'flame':\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <path d=\"M12 2c1.5 3 4 6 4 10a4 4 0 0 1-8 0c0-4 2.5-7 4-10z\"/>\n            <path d=\"M12 7c.5 1.5 2 3 2 5a2 2 0 1 1-4 0c0-2 1.5-3.5 2-5z\"/>\n          </svg>\n        );\n      case 'extraction':\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <path d=\"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"/>\n            <path d=\"M9 12l2 2 4-4\"/>\n          </svg>\n        );\n      case 'flower':\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <path d=\"M12 2a3 3 0 0 0-3 3c0 1.12.61 2.1 1.5 2.61L9 9.5l-2.5-1.5C5.61 7.1 5 6.12 5 5a3 3 0 0 0-6 0c0 1.31.84 2.41 2 2.83v2.34C.84 10.59 0 11.69 0 13a3 3 0 0 0 6 0c0-1.12-.61-2.1-1.5-2.61L7 8.5l2.5 1.5c.89-.51 1.5-1.49 1.5-2.61a3 3 0 0 0-6 0z\"/>\n          </svg>\n        );\n      default:\n        return (\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" width={width} height={height}>\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <path d=\"M8 12l2 2 4-4\"/>\n          </svg>\n        );\n    }\n  };\n\n  return (\n    <motion.div\n      className={`lottie-icon-animations inline-block cursor-pointer ${className}`}\n      style={{\n        color: categoryStyles.color,\n        ...style,\n      }}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      onClick={handleClick}\n      whileHover={{\n        scale: 1.1,\n        filter: 'brightness(1.2)',\n        boxShadow: isHovered ? categoryStyles.glow : 'none',\n      }}\n      whileTap={{ scale: 0.95 }}\n      animate={{\n        rotate: isPlaying ? [0, 360] : 0,\n      }}\n      transition={{\n        rotate: {\n          duration: 2 / speed,\n          repeat: loop && isPlaying ? Infinity : 0,\n          ease: 'linear',\n        },\n        scale: { duration: 0.2 },\n        filter: { duration: 0.2 },\n      }}\n    >\n      {/* \n        In production, replace this with:\n        <Lottie\n          animationData={animationData}\n          play={isPlaying}\n          loop={loop}\n          speed={speed}\n          style={{ width, height }}\n        />\n      */}\n      {getPlaceholderIcon()}\n    </motion.div>\n  );\n};\n\nexport default LottieIconAnimations;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,uBAA4D,CAAC,EACjE,aAAa,EACb,YAAY,EAAE,EACd,KAAK,EACL,UAAU,OAAO,EACjB,OAAO,IAAI,EACX,QAAQ,CAAC,EACT,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,WAAW,QAAQ,EACpB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,gCAAgC;IAChC,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBAC/B,MAAM,sHAAA,CAAA,eAAY,CAAC,cAAc;gBACnC;YACF,KAAK;gBACH,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAC5B,MAAM,sHAAA,CAAA,eAAY,CAAC,QAAQ;gBAC7B;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,IAAI;oBAC5B,MAAM,sHAAA,CAAA,eAAY,CAAC,MAAM;gBAC3B;YACF,KAAK;gBACH,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBAC/B,MAAM,sHAAA,CAAA,eAAY,CAAC,cAAc;gBACnC;YACF,KAAK;gBACH,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,IAAI;oBAChC,MAAM,sHAAA,CAAA,eAAY,CAAC,MAAM;gBAC3B;YACF;gBACE,OAAO;oBACL,OAAO,sHAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI;oBAC/B,MAAM,sHAAA,CAAA,eAAY,CAAC,cAAc;gBACnC;QACJ;IACF;IAEA,MAAM,iBAAiB;IAEvB,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,aAAa;QACb,IAAI,YAAY,SAAS;YACvB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,IAAI,YAAY,WAAW,CAAC,MAAM;YAChC,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY,SAAS;YACvB,aAAa,CAAC;QAChB;IACF;IAEA,iDAAiD;IACjD,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;8BACjE,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;;sCACjE,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,8OAAC;4BAAO,IAAG;4BAAI,IAAG;4BAAI,GAAE;;;;;;sCACxB,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAI,GAAE;;;;;;sCACzB,8OAAC;4BAAO,IAAG;4BAAI,IAAG;4BAAK,GAAE;;;;;;sCACzB,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,8OAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAI,IAAG;4BAAK,IAAG;;;;;;sCAChC,8OAAC;4BAAK,IAAG;4BAAI,IAAG;4BAAK,IAAG;4BAAK,IAAG;;;;;;;;;;;;YAGtC,KAAK;gBACH,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;;sCACjE,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;;sCACjE,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;8BACjE,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;gBACE,qBACE,8OAAC;oBAAI,SAAQ;oBAAY,MAAK;oBAAe,OAAO;oBAAO,QAAQ;;sCACjE,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,8OAAC;4BAAK,GAAE;;;;;;;;;;;;QAGhB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mDAAmD,EAAE,WAAW;QAC5E,OAAO;YACL,OAAO,eAAe,KAAK;YAC3B,GAAG,KAAK;QACV;QACA,cAAc;QACd,cAAc;QACd,SAAS;QACT,YAAY;YACV,OAAO;YACP,QAAQ;YACR,WAAW,YAAY,eAAe,IAAI,GAAG;QAC/C;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YACP,QAAQ,YAAY;gBAAC;gBAAG;aAAI,GAAG;QACjC;QACA,YAAY;YACV,QAAQ;gBACN,UAAU,IAAI;gBACd,QAAQ,QAAQ,YAAY,WAAW;gBACvC,MAAM;YACR;YACA,OAAO;gBAAE,UAAU;YAAI;YACvB,QAAQ;gBAAE,UAAU;YAAI;QAC1B;kBAYC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/MorphingBlobHero.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { MorphingBlobHeroProps } from '../../types/visual';\nimport { terpeneColors, brandAnimations } from '../../styles/brand';\n\n/**\n * MorphingBlobHero Component\n * \n * Adds fluid, organic motion behind content in hero sections.\n * Features smooth SVG blob morphing with terpene-themed gradients.\n * \n * @example\n * ```tsx\n * <div className=\"hero-section relative\">\n *   <MorphingBlobHero \n *     gradientTheme=\"rosin\"\n *     size={400}\n *     position=\"center\"\n *     animate={true}\n *   />\n *   <h1 className=\"relative z-10\">Hero Content</h1>\n * </div>\n * ```\n */\nconst MorphingBlobHero: React.FC<MorphingBlobHeroProps> = ({\n  className = '',\n  style,\n  shapeCount = 4,\n  morphDuration = 3000,\n  morphDelay = 500,\n  size = 300,\n  gradientTheme = 'flower',\n  customGradient,\n  animate = true,\n  blurIntensity = 20,\n  position = 'center',\n}) => {\n  const [currentShapeIndex, setCurrentShapeIndex] = useState(0);\n\n  // Predefined blob shapes for morphing\n  const blobShapes = [\n    // Shape 1 - Organic blob\n    \"M60,-60C80,-40,100,-20,100,0C100,20,80,40,60,60C40,80,20,100,0,100C-20,100,-40,80,-60,60C-80,40,-100,20,-100,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-100C20,-100,40,-80,60,-60Z\",\n    \n    // Shape 2 - Rounded square-ish\n    \"M50,-50C70,-50,90,-30,90,-10C90,10,70,30,50,50C30,70,10,90,-10,90C-30,90,-50,70,-50,50C-70,30,-90,10,-90,-10C-90,-30,-70,-50,-50,-50C-30,-70,-10,-90,10,-90C30,-90,50,-70,50,-50Z\",\n    \n    // Shape 3 - Flowing organic\n    \"M70,-30C90,-10,90,30,70,50C50,70,10,70,-10,50C-30,30,-30,-10,-10,-30C10,-50,50,-50,70,-30Z\",\n    \n    // Shape 4 - Irregular blob\n    \"M40,-60C60,-40,80,-20,80,20C80,60,40,80,0,80C-40,80,-80,60,-80,20C-80,-20,-60,-40,-40,-60C-20,-80,20,-80,40,-60Z\",\n  ];\n\n  // Get gradient based on theme\n  const getGradient = () => {\n    if (customGradient) return customGradient;\n    \n    const terpene = terpeneColors[gradientTheme];\n    if (!terpene) return terpeneColors.flower.gradient;\n    \n    return terpene.gradient;\n  };\n\n  // Get position styles\n  const getPositionStyles = () => {\n    const baseStyles = {\n      position: 'absolute' as const,\n      zIndex: 0,\n    };\n\n    switch (position) {\n      case 'center':\n        return {\n          ...baseStyles,\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n        };\n      case 'top-left':\n        return {\n          ...baseStyles,\n          top: '10%',\n          left: '10%',\n        };\n      case 'top-right':\n        return {\n          ...baseStyles,\n          top: '10%',\n          right: '10%',\n        };\n      case 'bottom-left':\n        return {\n          ...baseStyles,\n          bottom: '10%',\n          left: '10%',\n        };\n      case 'bottom-right':\n        return {\n          ...baseStyles,\n          bottom: '10%',\n          right: '10%',\n        };\n      default:\n        return {\n          ...baseStyles,\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n        };\n    }\n  };\n\n  // Morphing animation\n  useEffect(() => {\n    if (!animate) return;\n\n    const interval = setInterval(() => {\n      setCurrentShapeIndex(prev => (prev + 1) % shapeCount);\n    }, morphDuration + morphDelay);\n\n    return () => clearInterval(interval);\n  }, [animate, morphDuration, morphDelay, shapeCount]);\n\n  const gradientId = `blob-gradient-${Math.random().toString(36).substr(2, 9)}`;\n\n  return (\n    <motion.div\n      className={`morphing-blob-hero ${className}`}\n      style={{\n        ...getPositionStyles(),\n        filter: `blur(${blurIntensity}px)`,\n        ...style,\n      }}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 1, ease: brandAnimations.easing.smooth }}\n    >\n      <svg\n        width={size}\n        height={size}\n        viewBox=\"-100 -100 200 200\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <defs>\n          <linearGradient id={gradientId} x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor={terpeneColors[gradientTheme]?.from || '#359a5a'} />\n            <stop offset=\"100%\" stopColor={terpeneColors[gradientTheme]?.to || '#ffd700'} />\n          </linearGradient>\n        </defs>\n        \n        <motion.path\n          d={blobShapes[currentShapeIndex]}\n          fill={`url(#${gradientId})`}\n          animate={{\n            d: blobShapes[currentShapeIndex],\n          }}\n          transition={{\n            duration: morphDuration / 1000,\n            ease: brandAnimations.easing.smooth,\n          }}\n        />\n      </svg>\n    </motion.div>\n  );\n};\n\nexport default MorphingBlobHero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA;;;;;;;;;;;;;;;;;;CAkBC,GACD,MAAM,mBAAoD,CAAC,EACzD,YAAY,EAAE,EACd,KAAK,EACL,aAAa,CAAC,EACd,gBAAgB,IAAI,EACpB,aAAa,GAAG,EAChB,OAAO,GAAG,EACV,gBAAgB,QAAQ,EACxB,cAAc,EACd,UAAU,IAAI,EACd,gBAAgB,EAAE,EAClB,WAAW,QAAQ,EACpB;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,sCAAsC;IACtC,MAAM,aAAa;QACjB,yBAAyB;QACzB;QAEA,+BAA+B;QAC/B;QAEA,4BAA4B;QAC5B;QAEA,2BAA2B;QAC3B;KACD;IAED,8BAA8B;IAC9B,MAAM,cAAc;QAClB,IAAI,gBAAgB,OAAO;QAE3B,MAAM,UAAU,sHAAA,CAAA,gBAAa,CAAC,cAAc;QAC5C,IAAI,CAAC,SAAS,OAAO,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,QAAQ;QAElD,OAAO,QAAQ,QAAQ;IACzB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,MAAM,aAAa;YACjB,UAAU;YACV,QAAQ;QACV;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,KAAK;oBACL,MAAM;oBACN,WAAW;gBACb;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,KAAK;oBACL,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,KAAK;oBACL,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,QAAQ;oBACR,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,QAAQ;oBACR,OAAO;gBACT;YACF;gBACE,OAAO;oBACL,GAAG,UAAU;oBACb,KAAK;oBACL,MAAM;oBACN,WAAW;gBACb;QACJ;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;QAC5C,GAAG,gBAAgB;QAEnB,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAS;QAAe;QAAY;KAAW;IAEnD,MAAM,aAAa,CAAC,cAAc,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE7E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mBAAmB,EAAE,WAAW;QAC5C,OAAO;YACL,GAAG,mBAAmB;YACtB,QAAQ,CAAC,KAAK,EAAE,cAAc,GAAG,CAAC;YAClC,GAAG,KAAK;QACV;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAG,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;QAAC;kBAE/D,cAAA,8OAAC;YACC,OAAO;YACP,QAAQ;YACR,SAAQ;YACR,OAAM;;8BAEN,8OAAC;8BACC,cAAA,8OAAC;wBAAe,IAAI;wBAAY,IAAG;wBAAK,IAAG;wBAAK,IAAG;wBAAO,IAAG;;0CAC3D,8OAAC;gCAAK,QAAO;gCAAK,WAAW,sHAAA,CAAA,gBAAa,CAAC,cAAc,EAAE,QAAQ;;;;;;0CACnE,8OAAC;gCAAK,QAAO;gCAAO,WAAW,sHAAA,CAAA,gBAAa,CAAC,cAAc,EAAE,MAAM;;;;;;;;;;;;;;;;;8BAIvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAG,UAAU,CAAC,kBAAkB;oBAChC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;oBAC3B,SAAS;wBACP,GAAG,UAAU,CAAC,kBAAkB;oBAClC;oBACA,YAAY;wBACV,UAAU,gBAAgB;wBAC1B,MAAM,sHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM;oBACrC;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/ClientWrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface ClientWrapperProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\n/**\n * ClientWrapper Component\n * \n * Prevents hydration mismatches by only rendering children on the client side.\n * Useful for components that use browser APIs or have dynamic content.\n */\nexport default function ClientWrapper({ children, fallback = null }: ClientWrapperProps) {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS,cAAc,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAsB;IACrF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/visual/demos/VisualComponentsShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport ComponentDemo from './ComponentDemo';\nimport ScrollTriggeredFadeIn from '../ScrollTriggeredFadeIn';\nimport ImageZoomOnHover from '../ImageZoomOnHover';\nimport SectionRevealWipe from '../SectionRevealWipe';\nimport NoiseOverlayGrain from '../NoiseOverlayGrain';\nimport DynamicGradientText from '../DynamicGradientText';\nimport Tilt3DCardEffect from '../Tilt3DCardEffect';\nimport TypewriterIntroText from '../TypewriterIntroText';\nimport ParticleCanvasBackground from '../ParticleCanvasBackground';\nimport LottieIconAnimations from '../LottieIconAnimations';\nimport MorphingBlobHero from '../MorphingBlobHero';\nimport ClientWrapper from '../ClientWrapper';\n\n/**\n * Visual Components Showcase\n * \n * Comprehensive demo page showcasing all Apothecary Farms visual components\n */\nconst VisualComponentsShowcase: React.FC = () => {\n  return (\n    <div className=\"visual-components-showcase max-w-6xl mx-auto p-8\">\n      {/* Header */}\n      <div className=\"showcase-header text-center mb-12\">\n        <h1 className=\"text-5xl font-serif font-bold text-charcoal-800 mb-4\">\n          Apothecary Farms Visual Components\n        </h1>\n        <p className=\"text-xl text-charcoal-600 max-w-3xl mx-auto\">\n          A production-ready component library featuring immersive animations and interactions \n          infused with cannabis industry aesthetics and modern web technologies.\n        </p>\n      </div>\n\n      {/* ScrollTriggeredFadeIn Demo */}\n      <ComponentDemo\n        name=\"ScrollTriggeredFadeIn\"\n        description=\"Animates elements to rise and fade in when they enter the viewport with mint-green glow effects.\"\n        component={\n          <ScrollTriggeredFadeIn>\n            <div className=\"bg-primary-100 p-6 rounded-lg text-center\">\n              <h4 className=\"text-lg font-semibold text-primary-800\">\n                Scroll-triggered content\n              </h4>\n              <p className=\"text-primary-600\">This card fades in with a gentle rise animation</p>\n            </div>\n          </ScrollTriggeredFadeIn>\n        }\n        codeExample={`<ScrollTriggeredFadeIn>\n  <div className=\"card\">Content that fades in on scroll</div>\n</ScrollTriggeredFadeIn>`}\n        propsDoc={{\n          delay: 'Animation delay in ms (default: 0)',\n          duration: 'Animation duration in ms (default: 500)',\n          translateY: 'Distance to translate from in px (default: 40)',\n          threshold: 'Intersection observer threshold (default: 0.1)',\n          once: 'Animate only once (default: true)',\n        }}\n      />\n\n      {/* ImageZoomOnHover Demo */}\n      <ComponentDemo\n        name=\"ImageZoomOnHover\"\n        description=\"Makes product imagery feel tactile and reactive with scale and brightness effects on hover.\"\n        component={\n          <div className=\"flex justify-center\">\n            <div className=\"w-48 h-48 relative\">\n              <ImageZoomOnHover\n                src=\"/api/placeholder/300/300\"\n                alt=\"Cannabis Product\"\n                addBrandShadow={true}\n                shadowTheme=\"apothecary\"\n              />\n            </div>\n          </div>\n        }\n        codeExample={`<ImageZoomOnHover \n  src=\"/products/rosin.jpg\" \n  alt=\"Premium Rosin Extract\"\n  addBrandShadow={true}\n  shadowTheme=\"apothecary\"\n/>`}\n        propsDoc={{\n          scale: 'Scale factor on hover (default: 1.07)',\n          brightness: 'Brightness factor on hover (default: 1.1)',\n          addBrandShadow: 'Add brand-themed shadow (default: true)',\n          shadowTheme: 'Shadow color theme: apothecary | gold | sage',\n        }}\n      />\n\n      {/* SectionRevealWipe Demo */}\n      <ComponentDemo\n        name=\"SectionRevealWipe\"\n        description=\"Delivers editorial polish with side-entrance curtain wipe effects using clip-path animations.\"\n        component={\n          <SectionRevealWipe \n            direction=\"left-to-right\" \n            gradientTheme=\"cannabis\"\n            addEdgeAccents={true}\n          >\n            <div className=\"text-center py-8 text-white\">\n              <h4 className=\"text-2xl font-bold mb-2\">Hero Section</h4>\n              <p>Content revealed with curtain wipe effect</p>\n            </div>\n          </SectionRevealWipe>\n        }\n        codeExample={`<SectionRevealWipe \n  direction=\"left-to-right\" \n  gradientTheme=\"cannabis\"\n  addEdgeAccents={true}\n>\n  <h2>Hero Section Content</h2>\n</SectionRevealWipe>`}\n        propsDoc={{\n          direction: 'Wipe direction: left-to-right | right-to-left | top-to-bottom | bottom-to-top',\n          gradientTheme: 'Background gradient: cannabis | gold | sage | hero',\n          addEdgeAccents: 'Add light edge accents (default: true)',\n          trigger: 'Animation trigger: scroll | load | click | manual',\n        }}\n      />\n\n      {/* NoiseOverlayGrain Demo */}\n      <ComponentDemo\n        name=\"NoiseOverlayGrain\"\n        description=\"Adds subtle texture and analog warmth to backgrounds with animated film-grain effects.\"\n        component={\n          <div className=\"relative bg-primary-800 rounded-lg h-32 overflow-hidden\">\n            <ClientWrapper>\n              <NoiseOverlayGrain opacity={0.1} tintColor=\"#2FB886\" />\n            </ClientWrapper>\n            <div className=\"relative z-10 flex items-center justify-center h-full text-white\">\n              <p>Background with animated grain texture</p>\n            </div>\n          </div>\n        }\n        codeExample={`<div className=\"hero-section relative\">\n  <NoiseOverlayGrain opacity={0.05} tintColor=\"#2FB886\" />\n  <h1>Hero Content</h1>\n</div>`}\n        propsDoc={{\n          opacity: 'Grain opacity 0-1 (default: 0.04)',\n          blendMode: 'CSS blend mode (default: overlay)',\n          tintColor: 'Grain tint color (default: #2FB886)',\n          grainSize: 'Grain pattern size: small | medium | large',\n        }}\n      />\n\n      {/* DynamicGradientText Demo */}\n      <ComponentDemo\n        name=\"DynamicGradientText\"\n        description=\"Draws attention to headlines using animated gradient fills with terpene-themed colors.\"\n        component={\n          <div className=\"text-center\">\n            <DynamicGradientText \n              text=\"Premium Cannabis Extracts\" \n              theme=\"rosin\"\n              fontSize=\"2.5rem\"\n              animate={true}\n            />\n          </div>\n        }\n        codeExample={`<DynamicGradientText \n  text=\"Premium Cannabis Extracts\" \n  theme=\"rosin\"\n  fontSize=\"3rem\"\n  animate={true}\n/>`}\n        propsDoc={{\n          theme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',\n          customGradient: 'Custom CSS gradient string',\n          animationSpeed: 'Animation speed in ms (default: 3000)',\n          animate: 'Enable gradient animation (default: true)',\n        }}\n      />\n\n      {/* Tilt3DCardEffect Demo */}\n      <ComponentDemo\n        name=\"Tilt3DCardEffect\"\n        description=\"Increases interactivity and realism for cards with 3D tilt effects based on mouse position.\"\n        component={\n          <div className=\"flex justify-center\">\n            <Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>\n              <div className=\"bg-gradient-to-br from-gold-200 to-gold-400 p-6 rounded-lg w-64 text-center\">\n                <h4 className=\"text-lg font-semibold text-charcoal-800 mb-2\">\n                  Blue Dream\n                </h4>\n                <p className=\"text-charcoal-600\">Premium Sativa Hybrid</p>\n                <div className=\"mt-4 text-2xl\">🌿</div>\n              </div>\n            </Tilt3DCardEffect>\n          </div>\n        }\n        codeExample={`<Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>\n  <div className=\"strain-card\">\n    <h3>Blue Dream</h3>\n    <p>Premium Sativa Hybrid</p>\n  </div>\n</Tilt3DCardEffect>`}\n        propsDoc={{\n          maxTilt: 'Maximum tilt angle in degrees (default: 10)',\n          perspective: 'CSS perspective value (default: 1000)',\n          scale: 'Scale factor on hover (default: 1.02)',\n          addDynamicShadow: 'Add dynamic shadows (default: true)',\n        }}\n      />\n\n      {/* TypewriterIntroText Demo */}\n      <ComponentDemo\n        name=\"TypewriterIntroText\"\n        description=\"Adds nostalgic, cinematic intros with letter-by-letter typing and blinking cursor.\"\n        component={\n          <div className=\"text-center\">\n            <ClientWrapper fallback={<div className=\"text-2xl font-serif\">Welcome to Apothecary Farms</div>}>\n              <TypewriterIntroText\n                text={[\"Welcome to\", \"Apothecary Farms\", \"Premium Cannabis Extracts\"]}\n                charDelay={50}\n                cursor=\"✨\"\n                fontFamily=\"serif\"\n              />\n            </ClientWrapper>\n          </div>\n        }\n        codeExample={`<TypewriterIntroText \n  text={[\"Welcome to\", \"Apothecary Farms\", \"Premium Cannabis Extracts\"]}\n  charDelay={50}\n  cursor=\"✨\"\n  loop={false}\n/>`}\n        propsDoc={{\n          text: 'String or array of strings to type',\n          charDelay: 'Delay between characters in ms (default: 50)',\n          wordDelay: 'Delay between words in ms (default: 1000)',\n          cursor: 'Cursor character or element (default: |)',\n          loop: 'Loop the animation (default: false)',\n        }}\n      />\n\n      {/* ParticleCanvasBackground Demo */}\n      <ComponentDemo\n        name=\"ParticleCanvasBackground\"\n        description=\"Creates immersive backgrounds with floating, interactive particles that react to cursor proximity.\"\n        component={\n          <div className=\"relative bg-charcoal-800 rounded-lg h-64 overflow-hidden\">\n            <ClientWrapper>\n              <ParticleCanvasBackground\n                particleCount={50}\n                colors={['#fff9c4', '#d4edda', '#e2d5f1']}\n                interactive={true}\n              />\n            </ClientWrapper>\n            <div className=\"relative z-10 flex items-center justify-center h-full text-white\">\n              <p>Move your mouse to interact with particles</p>\n            </div>\n          </div>\n        }\n        codeExample={`<div className=\"hero-section relative\">\n  <ParticleCanvasBackground \n    particleCount={100}\n    colors={['#fff9c4', '#d4edda', '#e2d5f1']}\n    interactive={true}\n  />\n  <h1>Hero Content</h1>\n</div>`}\n        propsDoc={{\n          particleCount: 'Number of particles (default: 80)',\n          colors: 'Array of particle colors',\n          interactive: 'React to mouse movement (default: true)',\n          connectionDistance: 'Distance for particle connections (default: 100)',\n        }}\n      />\n\n      {/* LottieIconAnimations Demo */}\n      <ComponentDemo\n        name=\"LottieIconAnimations\"\n        description=\"Makes key icons come alive with smooth microinteractions for extraction tech and product categories.\"\n        component={\n          <div className=\"flex justify-center gap-8\">\n            <LottieIconAnimations \n              animationData={{}}\n              trigger=\"hover\"\n              category=\"beaker\"\n              width={64}\n              height={64}\n            />\n            <LottieIconAnimations \n              animationData={{}}\n              trigger=\"hover\"\n              category=\"trichome\"\n              width={64}\n              height={64}\n            />\n            <LottieIconAnimations \n              animationData={{}}\n              trigger=\"hover\"\n              category=\"flame\"\n              width={64}\n              height={64}\n            />\n          </div>\n        }\n        codeExample={`<LottieIconAnimations \n  animationData={beakerAnimation}\n  trigger=\"hover\"\n  category=\"beaker\"\n  width={64}\n  height={64}\n/>`}\n        propsDoc={{\n          animationData: 'Lottie animation data or URL',\n          trigger: 'Animation trigger: hover | click | load | manual',\n          category: 'Icon category: beaker | trichome | flame | extraction | flower | product',\n          loop: 'Loop animation (default: true)',\n        }}\n      />\n\n      {/* MorphingBlobHero Demo */}\n      <ComponentDemo\n        name=\"MorphingBlobHero\"\n        description=\"Adds fluid, organic SVG blob animations that morph behind content in hero sections.\"\n        component={\n          <div className=\"relative h-64 bg-charcoal-100 rounded-lg overflow-hidden\">\n            <MorphingBlobHero \n              gradientTheme=\"rosin\"\n              size={200}\n              position=\"center\"\n              animate={true}\n            />\n            <div className=\"relative z-10 flex items-center justify-center h-full\">\n              <h4 className=\"text-2xl font-bold text-charcoal-800\">\n                Hero Content\n              </h4>\n            </div>\n          </div>\n        }\n        codeExample={`<div className=\"hero-section relative\">\n  <MorphingBlobHero \n    gradientTheme=\"rosin\"\n    size={400}\n    position=\"center\"\n    animate={true}\n  />\n  <h1 className=\"relative z-10\">Hero Content</h1>\n</div>`}\n        propsDoc={{\n          gradientTheme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',\n          size: 'Blob size in pixels (default: 300)',\n          position: 'Position: center | top-left | top-right | bottom-left | bottom-right',\n          animate: 'Enable morphing animation (default: true)',\n        }}\n      />\n    </div>\n  );\n};\n\nexport default VisualComponentsShowcase;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAgBA;;;;CAIC,GACD,MAAM,2BAAqC;IACzC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAA8C;;;;;;;;;;;;0BAO7D,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC,qJAAA,CAAA,UAAqB;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;;;;;;;;;;;;gBAItC,aAAa,CAAC;;wBAEE,CAAC;gBACjB,UAAU;oBACR,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,WAAW;oBACX,MAAM;gBACR;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;4BACf,KAAI;4BACJ,KAAI;4BACJ,gBAAgB;4BAChB,aAAY;;;;;;;;;;;;;;;;gBAKpB,aAAa,CAAC;;;;;EAKpB,CAAC;gBACK,UAAU;oBACR,OAAO;oBACP,YAAY;oBACZ,gBAAgB;oBAChB,aAAa;gBACf;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC,iJAAA,CAAA,UAAiB;oBAChB,WAAU;oBACV,eAAc;oBACd,gBAAgB;8BAEhB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;gBAIT,aAAa,CAAC;;;;;;oBAMF,CAAC;gBACb,UAAU;oBACR,WAAW;oBACX,eAAe;oBACf,gBAAgB;oBAChB,SAAS;gBACX;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6IAAA,CAAA,UAAa;sCACZ,cAAA,8OAAC,iJAAA,CAAA,UAAiB;gCAAC,SAAS;gCAAK,WAAU;;;;;;;;;;;sCAE7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;gBAIT,aAAa,CAAC;;;MAGhB,CAAC;gBACC,UAAU;oBACR,SAAS;oBACT,WAAW;oBACX,WAAW;oBACX,WAAW;gBACb;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mJAAA,CAAA,UAAmB;wBAClB,MAAK;wBACL,OAAM;wBACN,UAAS;wBACT,SAAS;;;;;;;;;;;gBAIf,aAAa,CAAC;;;;;EAKpB,CAAC;gBACK,UAAU;oBACR,OAAO;oBACP,gBAAgB;oBAChB,gBAAgB;oBAChB,SAAS;gBACX;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;wBAAC,SAAS;wBAAI,kBAAkB;kCAC/C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,8OAAC;oCAAE,WAAU;8CAAoB;;;;;;8CACjC,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;gBAKvC,aAAa,CAAC;;;;;mBAKH,CAAC;gBACZ,UAAU;oBACR,SAAS;oBACT,aAAa;oBACb,OAAO;oBACP,kBAAkB;gBACpB;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6IAAA,CAAA,UAAa;wBAAC,wBAAU,8OAAC;4BAAI,WAAU;sCAAsB;;;;;;kCAC5D,cAAA,8OAAC,mJAAA,CAAA,UAAmB;4BAClB,MAAM;gCAAC;gCAAc;gCAAoB;6BAA4B;4BACrE,WAAW;4BACX,QAAO;4BACP,YAAW;;;;;;;;;;;;;;;;gBAKnB,aAAa,CAAC;;;;;EAKpB,CAAC;gBACK,UAAU;oBACR,MAAM;oBACN,WAAW;oBACX,WAAW;oBACX,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6IAAA,CAAA,UAAa;sCACZ,cAAA,8OAAC,wJAAA,CAAA,UAAwB;gCACvB,eAAe;gCACf,QAAQ;oCAAC;oCAAW;oCAAW;iCAAU;gCACzC,aAAa;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;gBAIT,aAAa,CAAC;;;;;;;MAOhB,CAAC;gBACC,UAAU;oBACR,eAAe;oBACf,QAAQ;oBACR,aAAa;oBACb,oBAAoB;gBACtB;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oJAAA,CAAA,UAAoB;4BACnB,eAAe,CAAC;4BAChB,SAAQ;4BACR,UAAS;4BACT,OAAO;4BACP,QAAQ;;;;;;sCAEV,8OAAC,oJAAA,CAAA,UAAoB;4BACnB,eAAe,CAAC;4BAChB,SAAQ;4BACR,UAAS;4BACT,OAAO;4BACP,QAAQ;;;;;;sCAEV,8OAAC,oJAAA,CAAA,UAAoB;4BACnB,eAAe,CAAC;4BAChB,SAAQ;4BACR,UAAS;4BACT,OAAO;4BACP,QAAQ;;;;;;;;;;;;gBAId,aAAa,CAAC;;;;;;EAMpB,CAAC;gBACK,UAAU;oBACR,eAAe;oBACf,SAAS;oBACT,UAAU;oBACV,MAAM;gBACR;;;;;;0BAIF,8OAAC,sJAAA,CAAA,UAAa;gBACZ,MAAK;gBACL,aAAY;gBACZ,yBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gJAAA,CAAA,UAAgB;4BACf,eAAc;4BACd,MAAM;4BACN,UAAS;4BACT,SAAS;;;;;;sCAEX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;;;;;;gBAM3D,aAAa,CAAC;;;;;;;;MAQhB,CAAC;gBACC,UAAU;oBACR,eAAe;oBACf,MAAM;oBACN,UAAU;oBACV,SAAS;gBACX;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}]}