import Head from 'next/head';

interface SEOHeadProps {
  title: string;
  description: string;
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
}

export default function SEOHead({ 
  title, 
  description, 
  canonical,
  ogImage = '/og-image.jpg',
  noindex = false 
}: SEOHeadProps) {
  const fullTitle = title.includes('Apothecary Extracts') ? title : `${title} | Apothecary Extracts`;
  
  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Robots */}
      {noindex && <meta name="robots" content="noindex,nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="website" />
      <meta property="og:image" content={ogImage} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Cannabis Industry Specific */}
      <meta name="age-restriction" content="21+" />
      <meta name="content-rating" content="mature" />
      
      {/* Local Business Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Store",
            "name": "Apothecary Extracts",
            "description": description,
            "url": canonical || "https://apothecaryextracts.com",
            "logo": "https://apothecaryextracts.com/logo.png",
            "address": {
              "@type": "PostalAddress",
              "addressLocality": "Colorado Springs",
              "addressRegion": "CO",
              "addressCountry": "US"
            },
            "openingHours": "Mo-Su 09:00-21:00",
            "priceRange": "$$",
            "servesCuisine": "Cannabis Products",
            "hasOfferCatalog": {
              "@type": "OfferCatalog",
              "name": "Cannabis Products",
              "itemListElement": [
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Product",
                    "name": "Premium Cannabis Flower"
                  }
                },
                {
                  "@type": "Offer", 
                  "itemOffered": {
                    "@type": "Product",
                    "name": "Cannabis Concentrates"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Product", 
                    "name": "Cannabis Edibles"
                  }
                }
              ]
            }
          })
        }}
      />
    </Head>
  );
}
