[![banner](https://particles.js.org/images/banner2.png)](https://particles.js.org)

# tsParticles Wobble Updater

[![jsDelivr](https://data.jsdelivr.com/v1/package/npm/@tsparticles/updater-wobble/badge)](https://www.jsdelivr.com/package/npm/@tsparticles/updater-wobble)
[![npmjs](https://badge.fury.io/js/@tsparticles/updater-wobble.svg)](https://www.npmjs.com/package/@tsparticles/updater-wobble)
[![npmjs](https://img.shields.io/npm/dt/@tsparticles/updater-wobble)](https://www.npmjs.com/package/@tsparticles/updater-wobble) [![GitHub Sponsors](https://img.shields.io/github/sponsors/matteobruni)](https://github.com/sponsors/matteobruni)

[tsParticles](https://github.com/tsparticles/tsparticles) updater plugin for wobble animations.

## How to use it

### CDN / Vanilla JS / jQuery

The CDN/Vanilla version JS has one required file in vanilla configuration:

Including the `tsparticles.updater.wobble.min.js` file will export the function to load the updater plugin:

```javascript
loadWobbleUpdater;
```

### Usage

Once the scripts are loaded you can set up `tsParticles` and the updater plugin like this:

```javascript
(async () => {
  await loadWobbleUpdater(tsParticles);

  await tsParticles.load({
    id: "tsparticles",
    options: {
      /* options */
    },
  });
})();
```

### ESM / CommonJS

This package is compatible also with ES or CommonJS modules, firstly this needs to be installed, like this:

```shell
$ npm install @tsparticles/updater-wobble
```

or

```shell
$ yarn add @tsparticles/updater-wobble
```

Then you need to import it in the app, like this:

```javascript
const { tsParticles } = require("@tsparticles/engine");
const { loadWobbleUpdater } = require("@tsparticles/updater-wobble");

(async () => {
  await loadWobbleUpdater(tsParticles);
})();
```

or

```javascript
import { tsParticles } from "@tsparticles/engine";
import { loadWobbleUpdater } from "@tsparticles/updater-wobble";

(async () => {
  await loadWobbleUpdater(tsParticles);
})();
```
