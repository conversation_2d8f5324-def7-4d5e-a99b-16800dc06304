module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/styles/brand.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Apothecary Farms Brand Utility System
 * Centralized brand tokens for colors, gradients, shadows, and animations
 */ // Brand Colors
__turbopack_context__.s({
    "brandAnimations": (()=>brandAnimations),
    "brandColors": (()=>brandColors),
    "brandGradients": (()=>brandGradients),
    "brandShadows": (()=>brandShadows),
    "brandSpacing": (()=>brandSpacing),
    "brandTypography": (()=>brandTypography),
    "brandUtils": (()=>brandUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "terpeneColors": (()=>terpeneColors)
});
const brandColors = {
    // Primary Green Palette
    primary: {
        50: '#f0f9f4',
        100: '#dcf2e4',
        200: '#bce5cd',
        300: '#8dd1a8',
        400: '#57b67c',
        500: '#359a5a',
        600: '#267d47',
        700: '#1f633a',
        800: '#1b4332',
        900: '#163a2b'
    },
    // Apothecary Green (from memory - the mint green mentioned)
    apothecary: '#2FB886',
    // Cream/Neutral Palette
    cream: {
        50: '#fefefe',
        100: '#f8f6f0',
        200: '#f4f1e8',
        300: '#ede8db',
        400: '#e4dcc8',
        500: '#d8cdb0'
    },
    // Gold/Amber Palette (for extracts/rosin)
    gold: {
        50: '#fefcf7',
        100: '#fdf8ed',
        200: '#f9eed5',
        300: '#f4e4bc',
        400: '#edd5a3',
        500: '#d4a574'
    },
    // Charcoal/Gray Palette
    charcoal: {
        50: '#f8f9fa',
        100: '#e9ecef',
        200: '#dee2e6',
        300: '#ced4da',
        400: '#adb5bd',
        500: '#6c757d',
        600: '#495057',
        700: '#343a40',
        800: '#2d3436',
        900: '#212529'
    },
    // Sage Green Palette
    sage: {
        50: '#f7f9f8',
        100: '#eef2f0',
        200: '#dde5e1',
        300: '#c4d2ca',
        400: '#a5b8ad',
        500: '#95a99c'
    }
};
const terpeneColors = {
    // Pineapple Express - Orange to Yellow
    pineapple: {
        from: '#ff8c00',
        to: '#ffd700',
        gradient: 'linear-gradient(135deg, #ff8c00 0%, #ffd700 100%)'
    },
    // GMO - Blue Frost
    gmo: {
        from: '#4a90e2',
        to: '#87ceeb',
        gradient: 'linear-gradient(135deg, #4a90e2 0%, #87ceeb 100%)'
    },
    // Rosin - Gold to Amber
    rosin: {
        from: '#d4a574',
        to: '#ff8c00',
        gradient: 'linear-gradient(135deg, #d4a574 0%, #ff8c00 100%)'
    },
    // Flower - Green to Yellow
    flower: {
        from: '#359a5a',
        to: '#ffd700',
        gradient: 'linear-gradient(135deg, #359a5a 0%, #ffd700 100%)'
    },
    // Extract - Amber to Purple
    extract: {
        from: '#ff8c00',
        to: '#9370db',
        gradient: 'linear-gradient(135deg, #ff8c00 0%, #9370db 100%)'
    },
    // Particle Palette - Soft colors for backgrounds
    particles: {
        yellow: '#fff9c4',
        lime: '#d4edda',
        lavender: '#e2d5f1'
    }
};
const brandGradients = {
    // Main brand gradients
    cannabis: 'linear-gradient(135deg, #1b4332 0%, #267d47 50%, #95a99c 100%)',
    gold: 'linear-gradient(135deg, #d4a574 0%, #edd5a3 100%)',
    sage: 'linear-gradient(135deg, #95a99c 0%, #c4d2ca 100%)',
    // Context-based gradients
    hero: 'linear-gradient(135deg, #1b4332 0%, #163a2b 100%)',
    card: 'linear-gradient(135deg, #f8f6f0 0%, #ede8db 100%)',
    // Animated gradients for text
    dynamicText: {
        pineapple: 'linear-gradient(45deg, #ff8c00, #ffd700, #ff8c00)',
        gmo: 'linear-gradient(45deg, #4a90e2, #87ceeb, #4a90e2)',
        rosin: 'linear-gradient(45deg, #d4a574, #ff8c00, #d4a574)'
    }
};
const brandShadows = {
    // Soft shadows
    soft: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    // Brand-specific glows
    apothecaryGlow: '0 0 20px rgba(47, 184, 134, 0.3)',
    goldGlow: '0 0 20px rgba(212, 165, 116, 0.3)',
    // Dynamic shadows for tilt effects
    tiltShadow: (direction)=>{
        const shadows = {
            left: '-5px 5px 15px rgba(0, 0, 0, 0.2)',
            right: '5px 5px 15px rgba(0, 0, 0, 0.2)',
            up: '0 -5px 15px rgba(0, 0, 0, 0.2)',
            down: '0 5px 15px rgba(0, 0, 0, 0.2)'
        };
        return shadows[direction];
    }
};
const brandAnimations = {
    // Easing functions (Framer Motion format)
    easing: {
        smooth: [
            0.4,
            0,
            0.2,
            1
        ],
        bounce: [
            0.68,
            -0.55,
            0.265,
            1.55
        ],
        elastic: [
            0.175,
            0.885,
            0.32,
            1.275
        ]
    },
    // CSS easing functions (for CSS transitions)
    cssEasing: {
        smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    },
    // Duration presets
    duration: {
        fast: 200,
        normal: 300,
        slow: 500,
        verySlow: 800
    },
    // Common animation variants for Framer Motion
    fadeInUp: {
        initial: {
            opacity: 0,
            y: 40
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        }
    },
    scaleOnHover: {
        whileHover: {
            scale: 1.07,
            brightness: 1.1
        },
        transition: {
            duration: 0.2
        }
    },
    tiltEffect: {
        perspective: 1000,
        rotateRange: 10
    },
    typewriter: {
        charDelay: 50,
        cursorBlink: 1000
    }
};
const brandTypography = {
    fonts: {
        sans: 'var(--font-inter), Inter, system-ui, sans-serif',
        serif: 'var(--font-playfair), Playfair Display, Georgia, serif'
    },
    sizes: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem'
    },
    weights: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
    }
};
const brandSpacing = {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
    '5xl': '8rem'
};
const brandUtils = {
    // Get color with opacity
    withOpacity: (color, opacity)=>{
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.slice(0, 2), 16);
            const g = parseInt(hex.slice(2, 4), 16);
            const b = parseInt(hex.slice(4, 6), 16);
            return `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }
        return color;
    },
    // Generate random terpene gradient
    randomTerpeneGradient: ()=>{
        const terpenes = Object.keys(terpeneColors);
        const randomTerpene = terpenes[Math.floor(Math.random() * terpenes.length)];
        return terpeneColors[randomTerpene].gradient;
    },
    // Get appropriate text color for background
    getTextColor: (backgroundColor)=>{
        // Simple light/dark detection - in production, use a proper contrast calculation
        const darkColors = [
            '#1b4332',
            '#163a2b',
            '#267d47',
            '#2d3436',
            '#212529'
        ];
        return darkColors.includes(backgroundColor) ? brandColors.cream[50] : brandColors.charcoal[800];
    }
};
const __TURBOPACK__default__export__ = {
    colors: brandColors,
    terpenes: terpeneColors,
    gradients: brandGradients,
    shadows: brandShadows,
    animations: brandAnimations,
    typography: brandTypography,
    spacing: brandSpacing,
    utils: brandUtils
};
}}),
"[project]/src/components/visual/demos/ComponentDemo.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * ComponentDemo Wrapper
 * 
 * A reusable demo wrapper for showcasing visual components
 * with code examples and interactive controls.
 */ const ComponentDemo = ({ name, description, component, codeExample, propsDoc })=>{
    const [showCode, setShowCode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showProps, setShowProps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "component-demo bg-cream-100 rounded-lg p-6 mb-8 border border-sage-200",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "demo-header mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-2xl font-serif font-bold text-charcoal-800 mb-2",
                        children: name
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-charcoal-600 leading-relaxed",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 31,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "demo-area bg-white rounded-lg p-8 mb-4 relative overflow-hidden",
                style: {
                    minHeight: '200px',
                    boxShadow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium
                },
                children: component
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "demo-controls flex gap-4 mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>setShowCode(!showCode),
                        className: "btn-secondary text-sm",
                        style: {
                            backgroundColor: showCode ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[600] : 'transparent',
                            color: showCode ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].cream[50] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                            border: `2px solid ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[600]}`,
                            padding: '0.5rem 1rem',
                            borderRadius: '0.375rem',
                            fontWeight: 600,
                            transition: 'all 0.2s ease-in-out'
                        },
                        children: showCode ? 'Hide Code' : 'Show Code'
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 49,
                        columnNumber: 9
                    }, this),
                    propsDoc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>setShowProps(!showProps),
                        className: "btn-secondary text-sm",
                        style: {
                            backgroundColor: showProps ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].sage[500] : 'transparent',
                            color: showProps ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].cream[50] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].sage[500],
                            border: `2px solid ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].sage[500]}`,
                            padding: '0.5rem 1rem',
                            borderRadius: '0.375rem',
                            fontWeight: 600,
                            transition: 'all 0.2s ease-in-out'
                        },
                        children: showProps ? 'Hide Props' : 'Show Props'
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            showCode && codeExample && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    height: 0
                },
                animate: {
                    opacity: 1,
                    height: 'auto'
                },
                exit: {
                    opacity: 0,
                    height: 0
                },
                className: "code-example bg-charcoal-800 rounded-lg p-4 mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                    className: "text-cream-100 text-sm overflow-x-auto",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                        children: codeExample
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 93,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                    lineNumber: 92,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                lineNumber: 86,
                columnNumber: 9
            }, this),
            showProps && propsDoc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    height: 0
                },
                animate: {
                    opacity: 1,
                    height: 'auto'
                },
                exit: {
                    opacity: 0,
                    height: 0
                },
                className: "props-doc bg-sage-50 rounded-lg p-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "font-semibold text-charcoal-800 mb-3",
                        children: "Props"
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: Object.entries(propsDoc).map(([prop, info])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "prop-item",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-mono text-sm text-primary-600 font-semibold",
                                        children: prop
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                                        lineNumber: 110,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-charcoal-600 ml-2",
                                        children: typeof info === 'string' ? info : JSON.stringify(info)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                                        lineNumber: 113,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, prop, true, {
                                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                                lineNumber: 109,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                        lineNumber: 107,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
                lineNumber: 100,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/visual/demos/ComponentDemo.tsx",
        lineNumber: 25,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ComponentDemo;
}}),
"[project]/src/components/visual/ScrollTriggeredFadeIn.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * ScrollTriggeredFadeIn Component
 * 
 * Animates cards, text blocks, or images to "rise and fade in" when they enter the viewport.
 * Features Apothecary Farms brand aesthetics with mint-green glow effects.
 * 
 * @example
 * ```tsx
 * <ScrollTriggeredFadeIn>
 *   <div className="card">Content that fades in on scroll</div>
 * </ScrollTriggeredFadeIn>
 * ```
 */ const ScrollTriggeredFadeIn = ({ children, className = '', style, delay = 0, duration = 500, translateY = 40, threshold = 0.1, once = true, variants })=>{
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInView"])({
        threshold,
        triggerOnce: once
    });
    // Default animation variants
    const defaultVariants = {
        hidden: {
            opacity: 0,
            y: translateY
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: duration / 1000,
                delay: delay / 1000,
                ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
            }
        }
    };
    // Use custom variants if provided, otherwise use defaults
    const animationVariants = variants || defaultVariants;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: ref,
        initial: "hidden",
        animate: inView ? 'visible' : 'hidden',
        variants: animationVariants,
        className: `scroll-fade-in ${className}`,
        style: {
            ...style,
            // Add mint-green glow effect when visible
            boxShadow: inView ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow : 'none',
            transition: 'box-shadow 0.3s ease-in-out'
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/visual/ScrollTriggeredFadeIn.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ScrollTriggeredFadeIn;
}}),
"[project]/src/components/visual/ImageZoomOnHover.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * ImageZoomOnHover Component
 * 
 * Makes product imagery feel tactile and reactive to mouse interaction.
 * Features scale and brightness effects with Apothecary Green shadow.
 * 
 * @example
 * ```tsx
 * <ImageZoomOnHover 
 *   src="/products/rosin.jpg" 
 *   alt="Premium Rosin Extract"
 *   addBrandShadow={true}
 *   shadowTheme="apothecary"
 * />
 * ```
 */ const ImageZoomOnHover = ({ src, alt, className = '', style, scale = 1.07, brightness = 1.1, duration = 200, addBrandShadow = true, shadowTheme = 'apothecary' })=>{
    // Get the appropriate shadow based on theme
    const getShadow = (theme)=>{
        switch(theme){
            case 'apothecary':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow;
            case 'gold':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].goldGlow;
            case 'sage':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium;
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow;
        }
    };
    const hoverShadow = addBrandShadow ? getShadow(shadowTheme) : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `image-zoom-container relative overflow-hidden rounded-lg ${className}`,
        style: style,
        whileHover: {
            scale,
            filter: `brightness(${brightness})`,
            boxShadow: hoverShadow
        },
        transition: {
            duration: duration / 1000,
            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            src: src,
            alt: alt,
            fill: true,
            className: "object-cover",
            sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        }, void 0, false, {
            fileName: "[project]/src/components/visual/ImageZoomOnHover.tsx",
            lineNumber: 66,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/visual/ImageZoomOnHover.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ImageZoomOnHover;
}}),
"[project]/src/components/visual/SectionRevealWipe.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-intersection-observer/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
/**
 * SectionRevealWipe Component
 * 
 * Delivers editorial polish to major page sections with a "curtain wipe" effect.
 * Features side-entrance animations with clip-path and gradient backgrounds.
 * 
 * @example
 * ```tsx
 * <SectionRevealWipe 
 *   direction="left-to-right" 
 *   gradientTheme="cannabis"
 *   addEdgeAccents={true}
 * >
 *   <h2>Hero Section Content</h2>
 * </SectionRevealWipe>
 * ```
 */ const SectionRevealWipe = ({ children, className = '', style, direction = 'left-to-right', duration = 800, delay = 0, gradientTheme = 'cannabis', addEdgeAccents = true, trigger = 'scroll' })=>{
    const [ref, inView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$intersection$2d$observer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInView"])({
        threshold: 0.1,
        triggerOnce: true
    });
    const [isRevealed, setIsRevealed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(trigger === 'load');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (trigger === 'scroll' && inView) {
            setTimeout(()=>setIsRevealed(true), delay);
        } else if (trigger === 'load') {
            setTimeout(()=>setIsRevealed(true), delay);
        }
    }, [
        inView,
        trigger,
        delay
    ]);
    // Get clip-path values based on direction
    const getClipPath = (revealed)=>{
        if (!revealed) {
            switch(direction){
                case 'left-to-right':
                    return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';
                case 'right-to-left':
                    return 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)';
                case 'top-to-bottom':
                    return 'polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)';
                case 'bottom-to-top':
                    return 'polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)';
                default:
                    return 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)';
            }
        }
        return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';
    };
    // Get background gradient
    const getGradient = ()=>{
        switch(gradientTheme){
            case 'cannabis':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandGradients"].cannabis;
            case 'gold':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandGradients"].gold;
            case 'sage':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandGradients"].sage;
            case 'hero':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandGradients"].hero;
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandGradients"].cannabis;
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: `section-reveal-wipe relative overflow-hidden ${className}`,
        style: style,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute inset-0 z-0",
                style: {
                    background: getGradient(),
                    clipPath: getClipPath(isRevealed)
                },
                animate: {
                    clipPath: getClipPath(isRevealed)
                },
                transition: {
                    duration: duration / 1000,
                    ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/SectionRevealWipe.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            addEdgeAccents && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30",
                        initial: {
                            scaleX: 0
                        },
                        animate: {
                            scaleX: isRevealed ? 1 : 0
                        },
                        transition: {
                            duration: duration / 1000,
                            delay: (delay + duration * 0.5) / 1000,
                            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/SectionRevealWipe.tsx",
                        lineNumber: 112,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-30",
                        initial: {
                            scaleX: 0
                        },
                        animate: {
                            scaleX: isRevealed ? 1 : 0
                        },
                        transition: {
                            duration: duration / 1000,
                            delay: (delay + duration * 0.5) / 1000,
                            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/SectionRevealWipe.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "relative z-10",
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: isRevealed ? 1 : 0
                },
                transition: {
                    duration: duration * 0.6 / 1000,
                    delay: (delay + duration * 0.4) / 1000,
                    ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/visual/SectionRevealWipe.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/visual/SectionRevealWipe.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SectionRevealWipe;
}}),
"[project]/src/components/visual/NoiseOverlayGrain.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
/**
 * NoiseOverlayGrain Component
 * 
 * Adds subtle texture and analog warmth to full-page backgrounds.
 * Features animated film-grain texture with mint-green tint.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <NoiseOverlayGrain opacity={0.05} tintColor="#2FB886" />
 *   <h1>Hero Content</h1>
 * </div>
 * ```
 */ const NoiseOverlayGrain = ({ className = '', style, opacity = 0.04, blendMode = 'overlay', animationSpeed = 3000, tintColor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].apothecary, grainSize = 'medium' })=>{
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ensure we're on the client side to prevent hydration mismatches
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    // Generate noise pattern
    const generateNoise = (ctx, width, height)=>{
        const imageData = ctx.createImageData(width, height);
        const data = imageData.data;
        // Convert tint color to RGB
        const tintRGB = hexToRgb(tintColor);
        for(let i = 0; i < data.length; i += 4){
            const noise = Math.random() * 255;
            // Apply tint color
            data[i] = Math.min(255, noise + tintRGB.r * 0.1); // Red
            data[i + 1] = Math.min(255, noise + tintRGB.g * 0.1); // Green
            data[i + 2] = Math.min(255, noise + tintRGB.b * 0.1); // Blue
            data[i + 3] = noise * opacity * 255; // Alpha
        }
        ctx.putImageData(imageData, 0, 0);
    };
    // Convert hex color to RGB
    const hexToRgb = (hex)=>{
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : {
            r: 47,
            g: 184,
            b: 134
        }; // Default to apothecary green
    };
    // Get grain size multiplier
    const getGrainSizeMultiplier = ()=>{
        switch(grainSize){
            case 'small':
                return 0.5;
            case 'medium':
                return 1;
            case 'large':
                return 2;
            default:
                return 1;
        }
    };
    // Animation loop
    const animate = ()=>{
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // Generate new noise pattern
        generateNoise(ctx, canvas.width, canvas.height);
        // Schedule next frame
        animationRef.current = setTimeout(()=>{
            requestAnimationFrame(animate);
        }, animationSpeed);
    };
    // Setup canvas and start animation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return; // Only run on client side
        const canvas = canvasRef.current;
        if (!canvas) return;
        // Set canvas size based on grain size
        const sizeMultiplier = getGrainSizeMultiplier();
        canvas.width = Math.floor(window.innerWidth * sizeMultiplier);
        canvas.height = Math.floor(window.innerHeight * sizeMultiplier);
        // Start animation
        animate();
        // Handle window resize
        const handleResize = ()=>{
            canvas.width = Math.floor(window.innerWidth * sizeMultiplier);
            canvas.height = Math.floor(window.innerHeight * sizeMultiplier);
        };
        window.addEventListener('resize', handleResize);
        // Cleanup
        return ()=>{
            if (animationRef.current) {
                clearTimeout(animationRef.current);
            }
            window.removeEventListener('resize', handleResize);
        };
    }, [
        isClient,
        animationSpeed,
        grainSize,
        tintColor,
        opacity
    ]);
    // Don't render anything on server side to prevent hydration mismatch
    if (!isClient) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
        ref: canvasRef,
        className: `noise-overlay-grain absolute inset-0 pointer-events-none ${className}`,
        style: {
            mixBlendMode: blendMode,
            opacity,
            zIndex: 1,
            ...style
        }
    }, void 0, false, {
        fileName: "[project]/src/components/visual/NoiseOverlayGrain.tsx",
        lineNumber: 142,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NoiseOverlayGrain;
}}),
"[project]/src/components/visual/DynamicGradientText.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * DynamicGradientText Component
 * 
 * Draws attention to headlines using animated gradient fills.
 * Features terpene-themed gradients with background-clip: text.
 * 
 * @example
 * ```tsx
 * <DynamicGradientText 
 *   text="Premium Cannabis Extracts" 
 *   theme="rosin"
 *   fontSize="3rem"
 *   animate={true}
 * />
 * ```
 */ const DynamicGradientText = ({ text, className = '', style, theme = 'pineapple', customGradient, animationSpeed = 3000, fontSize = '2rem', fontWeight = 700, animate = true })=>{
    const [gradientPosition, setGradientPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Get gradient based on theme or custom gradient
    const getGradient = ()=>{
        if (customGradient) return customGradient;
        const terpene = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"][theme];
        if (!terpene) return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"].pineapple.gradient;
        return terpene.gradient;
    };
    // Create animated gradient with moving position
    const getAnimatedGradient = ()=>{
        if (!animate) return getGradient();
        const baseGradient = getGradient();
        // Extract colors from gradient and create animated version
        if (theme === 'pineapple') {
            return `linear-gradient(45deg, 
        #ff8c00 ${gradientPosition - 50}%, 
        #ffd700 ${gradientPosition}%, 
        #ff8c00 ${gradientPosition + 50}%)`;
        } else if (theme === 'gmo') {
            return `linear-gradient(45deg, 
        #4a90e2 ${gradientPosition - 50}%, 
        #87ceeb ${gradientPosition}%, 
        #4a90e2 ${gradientPosition + 50}%)`;
        } else if (theme === 'rosin') {
            return `linear-gradient(45deg, 
        #d4a574 ${gradientPosition - 50}%, 
        #ff8c00 ${gradientPosition}%, 
        #d4a574 ${gradientPosition + 50}%)`;
        } else if (theme === 'flower') {
            return `linear-gradient(45deg, 
        #359a5a ${gradientPosition - 50}%, 
        #ffd700 ${gradientPosition}%, 
        #359a5a ${gradientPosition + 50}%)`;
        } else if (theme === 'extract') {
            return `linear-gradient(45deg, 
        #ff8c00 ${gradientPosition - 50}%, 
        #9370db ${gradientPosition}%, 
        #ff8c00 ${gradientPosition + 50}%)`;
        }
        return baseGradient;
    };
    // Animate gradient position
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!animate) return;
        const interval = setInterval(()=>{
            setGradientPosition((prev)=>(prev + 1) % 200);
        }, animationSpeed / 200);
        return ()=>clearInterval(interval);
    }, [
        animate,
        animationSpeed
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].h1, {
        className: `dynamic-gradient-text ${className}`,
        style: {
            backgroundImage: getAnimatedGradient(),
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            color: 'transparent',
            fontSize,
            fontWeight,
            fontFamily: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandTypography"].fonts.serif,
            backgroundSize: '200% 200%',
            display: 'inline-block',
            lineHeight: 1.2,
            ...style
        },
        initial: {
            opacity: 0,
            scale: 0.9
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 0.6,
            ease: brandAnimations.easing.smooth
        },
        children: text
    }, void 0, false, {
        fileName: "[project]/src/components/visual/DynamicGradientText.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = DynamicGradientText;
}}),
"[project]/src/components/visual/Tilt3DCardEffect.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * Tilt3DCardEffect Component
 * 
 * Increases interactivity and realism for cards or strain showcases.
 * Features 3D tilt effects based on mouse position with dynamic shadows.
 * 
 * @example
 * ```tsx
 * <Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>
 *   <div className="strain-card">
 *     <h3>Blue Dream</h3>
 *     <p>Premium Sativa Hybrid</p>
 *   </div>
 * </Tilt3DCardEffect>
 * ```
 */ const Tilt3DCardEffect = ({ children, className = '', style, maxTilt = 10, perspective = 1000, scale = 1.02, speed = 300, addDynamicShadow = true, reset = true })=>{
    const cardRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [tiltValues, setTiltValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        rotateX: 0,
        rotateY: 0
    });
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Calculate tilt based on mouse position
    const handleMouseMove = (e)=>{
        if (!cardRef.current) return;
        const card = cardRef.current;
        const rect = card.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        // Calculate mouse position relative to card center
        const mouseX = e.clientX - centerX;
        const mouseY = e.clientY - centerY;
        // Calculate tilt angles (inverted for natural feel)
        const rotateY = mouseX / (rect.width / 2) * maxTilt;
        const rotateX = -(mouseY / (rect.height / 2)) * maxTilt;
        setTiltValues({
            rotateX,
            rotateY
        });
    };
    // Reset tilt on mouse leave
    const handleMouseLeave = ()=>{
        setIsHovered(false);
        if (reset) {
            setTiltValues({
                rotateX: 0,
                rotateY: 0
            });
        }
    };
    // Set hover state on mouse enter
    const handleMouseEnter = ()=>{
        setIsHovered(true);
    };
    // Get dynamic shadow based on tilt direction
    const getDynamicShadow = ()=>{
        if (!addDynamicShadow || !isHovered) return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium;
        const { rotateX, rotateY } = tiltValues;
        // Calculate shadow offset based on tilt
        const shadowX = rotateY * 0.5;
        const shadowY = rotateX * 0.5;
        const shadowBlur = Math.abs(rotateX) + Math.abs(rotateY) + 10;
        return `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, 0.2)`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        ref: cardRef,
        className: `tilt-3d-card ${className}`,
        style: {
            perspective: `${perspective}px`,
            transformStyle: 'preserve-3d',
            ...style
        },
        onMouseMove: handleMouseMove,
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        animate: {
            rotateX: tiltValues.rotateX,
            rotateY: tiltValues.rotateY,
            scale: isHovered ? scale : 1,
            boxShadow: getDynamicShadow()
        },
        transition: {
            duration: speed / 1000,
            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/visual/Tilt3DCardEffect.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Tilt3DCardEffect;
}}),
"[project]/src/components/visual/TypewriterIntroText.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * TypewriterIntroText Component
 * 
 * Adds nostalgic, cinematic intros to landing pages.
 * Features letter-by-letter typing with blinking cursor.
 * 
 * @example
 * ```tsx
 * <TypewriterIntroText 
 *   text={["Welcome to", "Apothecary Farms", "Premium Cannabis Extracts"]}
 *   charDelay={50}
 *   cursor="✨"
 *   loop={false}
 * />
 * ```
 */ const TypewriterIntroText = ({ text, className = '', style, charDelay = 50, wordDelay = 1000, cursor = '|', cursorBlinkSpeed = 1000, loop = false, startDelay = 0, fontFamily = 'serif' })=>{
    const [displayText, setDisplayText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [currentIndex, setCurrentIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [currentTextIndex, setCurrentTextIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isTyping, setIsTyping] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCursor, setShowCursor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ensure we're on the client side to prevent hydration mismatches
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    // Convert text to array if it's a string
    const textArray = Array.isArray(text) ? text : [
        text
    ];
    // Start typing animation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return; // Only run on client side
        const startTyping = setTimeout(()=>{
            setIsTyping(true);
        }, startDelay);
        return ()=>clearTimeout(startTyping);
    }, [
        isClient,
        startDelay
    ]);
    // Typing animation logic
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isTyping) return;
        const currentText = textArray[currentTextIndex];
        if (currentIndex < currentText.length) {
            // Type next character
            const timeout = setTimeout(()=>{
                setDisplayText((prev)=>prev + currentText[currentIndex]);
                setCurrentIndex((prev)=>prev + 1);
            }, charDelay);
            return ()=>clearTimeout(timeout);
        } else {
            // Finished typing current text
            if (currentTextIndex < textArray.length - 1) {
                // Move to next text after word delay
                const timeout = setTimeout(()=>{
                    setDisplayText('');
                    setCurrentIndex(0);
                    setCurrentTextIndex((prev)=>prev + 1);
                }, wordDelay);
                return ()=>clearTimeout(timeout);
            } else if (loop) {
                // Restart from beginning if looping
                const timeout = setTimeout(()=>{
                    setDisplayText('');
                    setCurrentIndex(0);
                    setCurrentTextIndex(0);
                }, wordDelay);
                return ()=>clearTimeout(timeout);
            } else {
                // Stop typing
                setIsTyping(false);
            }
        }
    }, [
        currentIndex,
        currentTextIndex,
        isTyping,
        textArray,
        charDelay,
        wordDelay,
        loop
    ]);
    // Cursor blinking animation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return; // Only run on client side
        const interval = setInterval(()=>{
            setShowCursor((prev)=>!prev);
        }, cursorBlinkSpeed);
        return ()=>clearInterval(interval);
    }, [
        isClient,
        cursorBlinkSpeed
    ]);
    // Get font family
    const getFontFamily = ()=>{
        return fontFamily === 'serif' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandTypography"].fonts.serif : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandTypography"].fonts.sans;
    };
    // Don't render anything on server side to prevent hydration mismatch
    if (!isClient) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `typewriter-intro-text ${className}`,
            style: {
                fontFamily: getFontFamily(),
                ...style
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: textArray[0]
            }, void 0, false, {
                fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
                lineNumber: 130,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `typewriter-intro-text ${className}`,
        style: {
            fontFamily: getFontFamily(),
            ...style
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].span, {
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: 1
                },
                transition: {
                    duration: 0.5
                },
                children: displayText
            }, void 0, false, {
                fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
                lineNumber: 143,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: (isTyping || showCursor) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].span, {
                    className: "typewriter-cursor",
                    style: {
                        color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].apothecary,
                        marginLeft: '2px'
                    },
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: showCursor ? 1 : 0
                    },
                    exit: {
                        opacity: 0
                    },
                    transition: {
                        duration: 0.1
                    },
                    children: cursor
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
                    lineNumber: 153,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
                lineNumber: 151,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/visual/TypewriterIntroText.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TypewriterIntroText;
}}),
"[project]/src/components/visual/ParticleCanvasBackground.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
/**
 * ParticleCanvasBackground Component
 * 
 * Creates immersive, responsive backgrounds for hero sections.
 * Features floating, interactive particles that react to cursor proximity.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <ParticleCanvasBackground 
 *     particleCount={100}
 *     colors={['#fff9c4', '#d4edda', '#e2d5f1']}
 *     interactive={true}
 *   />
 *   <h1>Hero Content</h1>
 * </div>
 * ```
 */ const ParticleCanvasBackground = ({ className = '', style, particleCount = 80, colors = [
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"].particles.yellow,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"].particles.lime,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"].particles.lavender
], sizeRange = [
    1,
    3
], speed = 0.5, connectionDistance = 100, interactive = true, repulsionDistance = 100, attractionStrength = 0.02 })=>{
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const mouseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    const particlesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ensure we're on the client side to prevent hydration mismatches
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    // Particle class
    class Particle {
        x;
        y;
        vx;
        vy;
        size;
        color;
        originalX;
        originalY;
        constructor(width, height){
            this.x = Math.random() * width;
            this.y = Math.random() * height;
            this.originalX = this.x;
            this.originalY = this.y;
            this.vx = (Math.random() - 0.5) * speed;
            this.vy = (Math.random() - 0.5) * speed;
            this.size = Math.random() * (sizeRange[1] - sizeRange[0]) + sizeRange[0];
            this.color = colors[Math.floor(Math.random() * colors.length)];
        }
        update(width, height, mouseX, mouseY) {
            // Calculate distance to mouse
            const dx = mouseX - this.x;
            const dy = mouseY - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (interactive && distance < repulsionDistance) {
                // Repulsion from mouse
                const force = (repulsionDistance - distance) / repulsionDistance;
                this.vx -= dx / distance * force * attractionStrength;
                this.vy -= dy / distance * force * attractionStrength;
            }
            // Update position
            this.x += this.vx;
            this.y += this.vy;
            // Boundary collision
            if (this.x < 0 || this.x > width) this.vx *= -1;
            if (this.y < 0 || this.y > height) this.vy *= -1;
            // Keep particles within bounds
            this.x = Math.max(0, Math.min(width, this.x));
            this.y = Math.max(0, Math.min(height, this.y));
            // Apply friction
            this.vx *= 0.99;
            this.vy *= 0.99;
        }
        draw(ctx) {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();
        }
    }
    // Initialize particles
    const initParticles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((width, height)=>{
        particlesRef.current = [];
        for(let i = 0; i < particleCount; i++){
            particlesRef.current.push(new Particle(width, height));
        }
    }, [
        particleCount
    ]);
    // Draw connections between nearby particles
    const drawConnections = (ctx)=>{
        const particles = particlesRef.current;
        for(let i = 0; i < particles.length; i++){
            for(let j = i + 1; j < particles.length; j++){
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance < connectionDistance) {
                    const opacity = 1 - distance / connectionDistance;
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.2})`;
                    ctx.lineWidth = 0.5;
                    ctx.stroke();
                }
            }
        }
    };
    // Animation loop
    const animate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // Update and draw particles
        particlesRef.current.forEach((particle)=>{
            particle.update(canvas.width, canvas.height, mouseRef.current.x, mouseRef.current.y);
            particle.draw(ctx);
        });
        // Draw connections
        drawConnections(ctx);
        animationRef.current = requestAnimationFrame(animate);
    }, []);
    // Handle mouse movement
    const handleMouseMove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        const canvas = canvasRef.current;
        if (!canvas) return;
        const rect = canvas.getBoundingClientRect();
        mouseRef.current.x = e.clientX - rect.left;
        mouseRef.current.y = e.clientY - rect.top;
    }, []);
    // Setup canvas and start animation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return; // Only run on client side
        const canvas = canvasRef.current;
        if (!canvas) return;
        // Set canvas size
        const resizeCanvas = ()=>{
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            initParticles(canvas.width, canvas.height);
        };
        resizeCanvas();
        animate();
        // Event listeners
        window.addEventListener('resize', resizeCanvas);
        if (interactive) {
            window.addEventListener('mousemove', handleMouseMove);
        }
        // Cleanup
        return ()=>{
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
            window.removeEventListener('resize', resizeCanvas);
            window.removeEventListener('mousemove', handleMouseMove);
        };
    }, [
        isClient,
        animate,
        handleMouseMove,
        initParticles,
        interactive
    ]);
    // Don't render anything on server side to prevent hydration mismatch
    if (!isClient) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
        ref: canvasRef,
        className: `particle-canvas-background absolute inset-0 pointer-events-none ${className}`,
        style: {
            zIndex: 0,
            ...style
        }
    }, void 0, false, {
        fileName: "[project]/src/components/visual/ParticleCanvasBackground.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ParticleCanvasBackground;
}}),
"[project]/src/components/visual/LottieIconAnimations.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * LottieIconAnimations Component
 * 
 * Makes key icons come alive via smooth, relevant microinteractions.
 * Features animated icons for extraction tech, flower types, and product categories.
 * 
 * Note: This is a placeholder implementation. In production, you would use:
 * import Lottie from 'react-lottie-player'
 * 
 * @example
 * ```tsx
 * <LottieIconAnimations 
 *   animationData={beakerAnimation}
 *   trigger="hover"
 *   category="beaker"
 *   width={64}
 *   height={64}
 * />
 * ```
 */ const LottieIconAnimations = ({ animationData, className = '', style, trigger = 'hover', loop = true, speed = 1, width = 48, height = 48, category = 'beaker' })=>{
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(trigger === 'load');
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Get category-specific styling
    const getCategoryStyles = ()=>{
        switch(category){
            case 'beaker':
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow
                };
            case 'trichome':
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].gold[400],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].goldGlow
                };
            case 'flame':
                return {
                    color: '#ff8c00',
                    glow: '0 0 20px rgba(255, 140, 0, 0.3)'
                };
            case 'extraction':
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].sage[500],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium
                };
            case 'flower':
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[500],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow
                };
            case 'product':
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].charcoal[600],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].medium
                };
            default:
                return {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandColors"].primary[600],
                    glow: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandShadows"].apothecaryGlow
                };
        }
    };
    const categoryStyles = getCategoryStyles();
    // Handle animation triggers
    const handleMouseEnter = ()=>{
        setIsHovered(true);
        if (trigger === 'hover') {
            setIsPlaying(true);
        }
    };
    const handleMouseLeave = ()=>{
        setIsHovered(false);
        if (trigger === 'hover' && !loop) {
            setIsPlaying(false);
        }
    };
    const handleClick = ()=>{
        if (trigger === 'click') {
            setIsPlaying(!isPlaying);
        }
    };
    // Placeholder SVG icons for different categories
    const getPlaceholderIcon = ()=>{
        switch(category){
            case 'beaker':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M9 2v6.5L4.5 14c-.83 1.24-.5 2.91.74 *********.96.45 1.51.45h9.5c.55 0 1.05-.15 1.5-.45 1.24-.83 1.57-2.5.74-3.74L13 8.5V2h-4zm2 2h2v5.5l4.5 5.5H6.5L11 9.5V4z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                        lineNumber: 112,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 111,
                    columnNumber: 11
                }, this);
            case 'trichome':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "12",
                            cy: "12",
                            r: "3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 118,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "6",
                            cy: "6",
                            r: "2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 119,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "18",
                            cy: "6",
                            r: "2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 120,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "6",
                            cy: "18",
                            r: "2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "18",
                            cy: "18",
                            r: "2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 122,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                            x1: "12",
                            y1: "9",
                            x2: "12",
                            y2: "15"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 123,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                            x1: "9",
                            y1: "12",
                            x2: "15",
                            y2: "12"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 124,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 117,
                    columnNumber: 11
                }, this);
            case 'flame':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M12 2c1.5 3 4 6 4 10a4 4 0 0 1-8 0c0-4 2.5-7 4-10z"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 130,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M12 7c.5 1.5 2 3 2 5a2 2 0 1 1-4 0c0-2 1.5-3.5 2-5z"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 131,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, this);
            case 'extraction':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 137,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M9 12l2 2 4-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 138,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 136,
                    columnNumber: 11
                }, this);
            case 'flower':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M12 2a3 3 0 0 0-3 3c0 1.12.61 2.1 1.5 2.61L9 9.5l-2.5-1.5C5.61 7.1 5 6.12 5 5a3 3 0 0 0-6 0c0 1.31.84 2.41 2 2.83v2.34C.84 10.59 0 11.69 0 13a3 3 0 0 0 6 0c0-1.12-.61-2.1-1.5-2.61L7 8.5l2.5 1.5c.89-.51 1.5-1.49 1.5-2.61a3 3 0 0 0-6 0z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                        lineNumber: 144,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 143,
                    columnNumber: 11
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                    width: width,
                    height: height,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            cx: "12",
                            cy: "12",
                            r: "10"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 150,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M8 12l2 2 4-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                            lineNumber: 151,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
                    lineNumber: 149,
                    columnNumber: 11
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `lottie-icon-animations inline-block cursor-pointer ${className}`,
        style: {
            color: categoryStyles.color,
            ...style
        },
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        onClick: handleClick,
        whileHover: {
            scale: 1.1,
            filter: 'brightness(1.2)',
            boxShadow: isHovered ? categoryStyles.glow : 'none'
        },
        whileTap: {
            scale: 0.95
        },
        animate: {
            rotate: isPlaying ? [
                0,
                360
            ] : 0
        },
        transition: {
            rotate: {
                duration: 2 / speed,
                repeat: loop && isPlaying ? Infinity : 0,
                ease: 'linear'
            },
            scale: {
                duration: 0.2
            },
            filter: {
                duration: 0.2
            }
        },
        children: getPlaceholderIcon()
    }, void 0, false, {
        fileName: "[project]/src/components/visual/LottieIconAnimations.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = LottieIconAnimations;
}}),
"[project]/src/components/visual/MorphingBlobHero.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/styles/brand.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
/**
 * MorphingBlobHero Component
 * 
 * Adds fluid, organic motion behind content in hero sections.
 * Features smooth SVG blob morphing with terpene-themed gradients.
 * 
 * @example
 * ```tsx
 * <div className="hero-section relative">
 *   <MorphingBlobHero 
 *     gradientTheme="rosin"
 *     size={400}
 *     position="center"
 *     animate={true}
 *   />
 *   <h1 className="relative z-10">Hero Content</h1>
 * </div>
 * ```
 */ const MorphingBlobHero = ({ className = '', style, shapeCount = 4, morphDuration = 3000, morphDelay = 500, size = 300, gradientTheme = 'flower', customGradient, animate = true, blurIntensity = 20, position = 'center' })=>{
    const [currentShapeIndex, setCurrentShapeIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Predefined blob shapes for morphing
    const blobShapes = [
        // Shape 1 - Organic blob
        "M60,-60C80,-40,100,-20,100,0C100,20,80,40,60,60C40,80,20,100,0,100C-20,100,-40,80,-60,60C-80,40,-100,20,-100,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-100C20,-100,40,-80,60,-60Z",
        // Shape 2 - Rounded square-ish
        "M50,-50C70,-50,90,-30,90,-10C90,10,70,30,50,50C30,70,10,90,-10,90C-30,90,-50,70,-50,50C-70,30,-90,10,-90,-10C-90,-30,-70,-50,-50,-50C-30,-70,-10,-90,10,-90C30,-90,50,-70,50,-50Z",
        // Shape 3 - Flowing organic
        "M70,-30C90,-10,90,30,70,50C50,70,10,70,-10,50C-30,30,-30,-10,-10,-30C10,-50,50,-50,70,-30Z",
        // Shape 4 - Irregular blob
        "M40,-60C60,-40,80,-20,80,20C80,60,40,80,0,80C-40,80,-80,60,-80,20C-80,-20,-60,-40,-40,-60C-20,-80,20,-80,40,-60Z"
    ];
    // Get gradient based on theme
    const getGradient = ()=>{
        if (customGradient) return customGradient;
        const terpene = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"][gradientTheme];
        if (!terpene) return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"].flower.gradient;
        return terpene.gradient;
    };
    // Get position styles
    const getPositionStyles = ()=>{
        const baseStyles = {
            position: 'absolute',
            zIndex: 0
        };
        switch(position){
            case 'center':
                return {
                    ...baseStyles,
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)'
                };
            case 'top-left':
                return {
                    ...baseStyles,
                    top: '10%',
                    left: '10%'
                };
            case 'top-right':
                return {
                    ...baseStyles,
                    top: '10%',
                    right: '10%'
                };
            case 'bottom-left':
                return {
                    ...baseStyles,
                    bottom: '10%',
                    left: '10%'
                };
            case 'bottom-right':
                return {
                    ...baseStyles,
                    bottom: '10%',
                    right: '10%'
                };
            default:
                return {
                    ...baseStyles,
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)'
                };
        }
    };
    // Morphing animation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!animate) return;
        const interval = setInterval(()=>{
            setCurrentShapeIndex((prev)=>(prev + 1) % shapeCount);
        }, morphDuration + morphDelay);
        return ()=>clearInterval(interval);
    }, [
        animate,
        morphDuration,
        morphDelay,
        shapeCount
    ]);
    const gradientId = `blob-gradient-${Math.random().toString(36).substr(2, 9)}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `morphing-blob-hero ${className}`,
        style: {
            ...getPositionStyles(),
            filter: `blur(${blurIntensity}px)`,
            ...style
        },
        initial: {
            opacity: 0,
            scale: 0.8
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 1,
            ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            width: size,
            height: size,
            viewBox: "-100 -100 200 200",
            xmlns: "http://www.w3.org/2000/svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                        id: gradientId,
                        x1: "0%",
                        y1: "0%",
                        x2: "100%",
                        y2: "100%",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "0%",
                                stopColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"][gradientTheme]?.from || '#359a5a'
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
                                lineNumber: 149,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                offset: "100%",
                                stopColor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["terpeneColors"][gradientTheme]?.to || '#ffd700'
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
                                lineNumber: 150,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
                        lineNumber: 148,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].path, {
                    d: blobShapes[currentShapeIndex],
                    fill: `url(#${gradientId})`,
                    animate: {
                        d: blobShapes[currentShapeIndex]
                    },
                    transition: {
                        duration: morphDuration / 1000,
                        ease: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$brand$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["brandAnimations"].easing.smooth
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
            lineNumber: 141,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/visual/MorphingBlobHero.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = MorphingBlobHero;
}}),
"[project]/src/components/visual/ClientWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ClientWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function ClientWrapper({ children, fallback = null }) {
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    if (!isClient) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: fallback
        }, void 0, false);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}}),
"[project]/src/components/visual/demos/VisualComponentsShowcase.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/demos/ComponentDemo.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ScrollTriggeredFadeIn$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/ScrollTriggeredFadeIn.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ImageZoomOnHover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/ImageZoomOnHover.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$SectionRevealWipe$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/SectionRevealWipe.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$NoiseOverlayGrain$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/NoiseOverlayGrain.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$DynamicGradientText$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/DynamicGradientText.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$Tilt3DCardEffect$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/Tilt3DCardEffect.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$TypewriterIntroText$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/TypewriterIntroText.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ParticleCanvasBackground$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/ParticleCanvasBackground.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$LottieIconAnimations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/LottieIconAnimations.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$MorphingBlobHero$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/MorphingBlobHero.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ClientWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/visual/ClientWrapper.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * Visual Components Showcase
 * 
 * Comprehensive demo page showcasing all Apothecary Farms visual components
 */ const VisualComponentsShowcase = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "visual-components-showcase max-w-6xl mx-auto p-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "showcase-header text-center mb-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-5xl font-serif font-bold text-charcoal-800 mb-4",
                        children: "Apothecary Farms Visual Components"
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xl text-charcoal-600 max-w-3xl mx-auto",
                        children: "A production-ready component library featuring immersive animations and interactions infused with cannabis industry aesthetics and modern web technologies."
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "ScrollTriggeredFadeIn",
                description: "Animates elements to rise and fade in when they enter the viewport with mint-green glow effects.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ScrollTriggeredFadeIn$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-primary-100 p-6 rounded-lg text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-lg font-semibold text-primary-800",
                                children: "Scroll-triggered content"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 43,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-primary-600",
                                children: "This card fades in with a gentle rise animation"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 46,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 42,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 41,
                    columnNumber: 11
                }, void 0),
                codeExample: `<ScrollTriggeredFadeIn>
  <div className="card">Content that fades in on scroll</div>
</ScrollTriggeredFadeIn>`,
                propsDoc: {
                    delay: 'Animation delay in ms (default: 0)',
                    duration: 'Animation duration in ms (default: 500)',
                    translateY: 'Distance to translate from in px (default: 40)',
                    threshold: 'Intersection observer threshold (default: 0.1)',
                    once: 'Animate only once (default: true)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "ImageZoomOnHover",
                description: "Makes product imagery feel tactile and reactive with scale and brightness effects on hover.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-48 h-48 relative",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ImageZoomOnHover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            src: "/api/placeholder/300/300",
                            alt: "Cannabis Product",
                            addBrandShadow: true,
                            shadowTheme: "apothecary"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 69,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 68,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 67,
                    columnNumber: 11
                }, void 0),
                codeExample: `<ImageZoomOnHover 
  src="/products/rosin.jpg" 
  alt="Premium Rosin Extract"
  addBrandShadow={true}
  shadowTheme="apothecary"
/>`,
                propsDoc: {
                    scale: 'Scale factor on hover (default: 1.07)',
                    brightness: 'Brightness factor on hover (default: 1.1)',
                    addBrandShadow: 'Add brand-themed shadow (default: true)',
                    shadowTheme: 'Shadow color theme: apothecary | gold | sage'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "SectionRevealWipe",
                description: "Delivers editorial polish with side-entrance curtain wipe effects using clip-path animations.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$SectionRevealWipe$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    direction: "left-to-right",
                    gradientTheme: "cannabis",
                    addEdgeAccents: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8 text-white",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-2xl font-bold mb-2",
                                children: "Hero Section"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 103,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Content revealed with curtain wipe effect"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 104,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 102,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 97,
                    columnNumber: 11
                }, void 0),
                codeExample: `<SectionRevealWipe 
  direction="left-to-right" 
  gradientTheme="cannabis"
  addEdgeAccents={true}
>
  <h2>Hero Section Content</h2>
</SectionRevealWipe>`,
                propsDoc: {
                    direction: 'Wipe direction: left-to-right | right-to-left | top-to-bottom | bottom-to-top',
                    gradientTheme: 'Background gradient: cannabis | gold | sage | hero',
                    addEdgeAccents: 'Add light edge accents (default: true)',
                    trigger: 'Animation trigger: scroll | load | click | manual'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "NoiseOverlayGrain",
                description: "Adds subtle texture and analog warmth to backgrounds with animated film-grain effects.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative bg-primary-800 rounded-lg h-32 overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ClientWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$NoiseOverlayGrain$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                opacity: 0.1,
                                tintColor: "#2FB886"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 130,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 129,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 flex items-center justify-center h-full text-white",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Background with animated grain texture"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 133,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 132,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 128,
                    columnNumber: 11
                }, void 0),
                codeExample: `<div className="hero-section relative">
  <NoiseOverlayGrain opacity={0.05} tintColor="#2FB886" />
  <h1>Hero Content</h1>
</div>`,
                propsDoc: {
                    opacity: 'Grain opacity 0-1 (default: 0.04)',
                    blendMode: 'CSS blend mode (default: overlay)',
                    tintColor: 'Grain tint color (default: #2FB886)',
                    grainSize: 'Grain pattern size: small | medium | large'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 124,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "DynamicGradientText",
                description: "Draws attention to headlines using animated gradient fills with terpene-themed colors.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$DynamicGradientText$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        text: "Premium Cannabis Extracts",
                        theme: "rosin",
                        fontSize: "2.5rem",
                        animate: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 155,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 154,
                    columnNumber: 11
                }, void 0),
                codeExample: `<DynamicGradientText 
  text="Premium Cannabis Extracts" 
  theme="rosin"
  fontSize="3rem"
  animate={true}
/>`,
                propsDoc: {
                    theme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',
                    customGradient: 'Custom CSS gradient string',
                    animationSpeed: 'Animation speed in ms (default: 3000)',
                    animate: 'Enable gradient animation (default: true)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 150,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "Tilt3DCardEffect",
                description: "Increases interactivity and realism for cards with 3D tilt effects based on mouse position.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$Tilt3DCardEffect$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        maxTilt: 15,
                        addDynamicShadow: true,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-br from-gold-200 to-gold-400 p-6 rounded-lg w-64 text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-lg font-semibold text-charcoal-800 mb-2",
                                    children: "Blue Dream"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                    lineNumber: 185,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-charcoal-600",
                                    children: "Premium Sativa Hybrid"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                    lineNumber: 188,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-4 text-2xl",
                                    children: "🌿"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                    lineNumber: 189,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 184,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 183,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 182,
                    columnNumber: 11
                }, void 0),
                codeExample: `<Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>
  <div className="strain-card">
    <h3>Blue Dream</h3>
    <p>Premium Sativa Hybrid</p>
  </div>
</Tilt3DCardEffect>`,
                propsDoc: {
                    maxTilt: 'Maximum tilt angle in degrees (default: 10)',
                    perspective: 'CSS perspective value (default: 1000)',
                    scale: 'Scale factor on hover (default: 1.02)',
                    addDynamicShadow: 'Add dynamic shadows (default: true)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 178,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "TypewriterIntroText",
                description: "Adds nostalgic, cinematic intros with letter-by-letter typing and blinking cursor.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ClientWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-2xl font-serif",
                            children: "Welcome to Apothecary Farms"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 214,
                            columnNumber: 38
                        }, void 0),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$TypewriterIntroText$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            text: [
                                "Welcome to",
                                "Apothecary Farms",
                                "Premium Cannabis Extracts"
                            ],
                            charDelay: 50,
                            cursor: "✨",
                            fontFamily: "serif"
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 215,
                            columnNumber: 15
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                        lineNumber: 214,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 213,
                    columnNumber: 11
                }, void 0),
                codeExample: `<TypewriterIntroText 
  text={["Welcome to", "Apothecary Farms", "Premium Cannabis Extracts"]}
  charDelay={50}
  cursor="✨"
  loop={false}
/>`,
                propsDoc: {
                    text: 'String or array of strings to type',
                    charDelay: 'Delay between characters in ms (default: 50)',
                    wordDelay: 'Delay between words in ms (default: 1000)',
                    cursor: 'Cursor character or element (default: |)',
                    loop: 'Loop the animation (default: false)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "ParticleCanvasBackground",
                description: "Creates immersive backgrounds with floating, interactive particles that react to cursor proximity.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative bg-charcoal-800 rounded-lg h-64 overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ClientWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$ParticleCanvasBackground$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                particleCount: 50,
                                colors: [
                                    '#fff9c4',
                                    '#d4edda',
                                    '#e2d5f1'
                                ],
                                interactive: true
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 246,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 245,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 flex items-center justify-center h-full text-white",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Move your mouse to interact with particles"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 253,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 252,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 244,
                    columnNumber: 11
                }, void 0),
                codeExample: `<div className="hero-section relative">
  <ParticleCanvasBackground 
    particleCount={100}
    colors={['#fff9c4', '#d4edda', '#e2d5f1']}
    interactive={true}
  />
  <h1>Hero Content</h1>
</div>`,
                propsDoc: {
                    particleCount: 'Number of particles (default: 80)',
                    colors: 'Array of particle colors',
                    interactive: 'React to mouse movement (default: true)',
                    connectionDistance: 'Distance for particle connections (default: 100)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 240,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "LottieIconAnimations",
                description: "Makes key icons come alive with smooth microinteractions for extraction tech and product categories.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$LottieIconAnimations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            animationData: {},
                            trigger: "hover",
                            category: "beaker",
                            width: 64,
                            height: 64
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 279,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$LottieIconAnimations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            animationData: {},
                            trigger: "hover",
                            category: "trichome",
                            width: 64,
                            height: 64
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 286,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$LottieIconAnimations$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            animationData: {},
                            trigger: "hover",
                            category: "flame",
                            width: 64,
                            height: 64
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 293,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 278,
                    columnNumber: 11
                }, void 0),
                codeExample: `<LottieIconAnimations 
  animationData={beakerAnimation}
  trigger="hover"
  category="beaker"
  width={64}
  height={64}
/>`,
                propsDoc: {
                    animationData: 'Lottie animation data or URL',
                    trigger: 'Animation trigger: hover | click | load | manual',
                    category: 'Icon category: beaker | trichome | flame | extraction | flower | product',
                    loop: 'Loop animation (default: true)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 274,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$demos$2f$ComponentDemo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: "MorphingBlobHero",
                description: "Adds fluid, organic SVG blob animations that morph behind content in hero sections.",
                component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative h-64 bg-charcoal-100 rounded-lg overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$visual$2f$MorphingBlobHero$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            gradientTheme: "rosin",
                            size: 200,
                            position: "center",
                            animate: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 323,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative z-10 flex items-center justify-center h-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-2xl font-bold text-charcoal-800",
                                children: "Hero Content"
                            }, void 0, false, {
                                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                                lineNumber: 330,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                            lineNumber: 329,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                    lineNumber: 322,
                    columnNumber: 11
                }, void 0),
                codeExample: `<div className="hero-section relative">
  <MorphingBlobHero 
    gradientTheme="rosin"
    size={400}
    position="center"
    animate={true}
  />
  <h1 className="relative z-10">Hero Content</h1>
</div>`,
                propsDoc: {
                    gradientTheme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',
                    size: 'Blob size in pixels (default: 300)',
                    position: 'Position: center | top-left | top-right | bottom-left | bottom-right',
                    animate: 'Enable morphing animation (default: true)'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
                lineNumber: 318,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/visual/demos/VisualComponentsShowcase.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = VisualComponentsShowcase;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__426a36fb._.js.map