import { motion } from "framer-motion";

export default function HeroVideo({ headline, subheadline, ctas, videoSrc }) {
  return (
    <section className="relative h-screen overflow-hidden bg-black text-white">
      <video autoPlay muted loop playsInline className="absolute w-full h-full object-cover opacity-40">
        <source src={videoSrc} type="video/mp4" />
      </video>
      <div className="relative z-10 flex flex-col items-center justify-center h-full text-center px-6">
        <motion.h1 className="text-4xl md:text-6xl font-bold" initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }}>
          {headline}
        </motion.h1>
        <p className="mt-4 text-xl md:text-2xl">{subheadline}</p>
        <div className="mt-6 flex gap-4 flex-wrap justify-center">
          {ctas.map((cta, i) => (
            <button key={i} className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-xl shadow-xl">
              {cta}
            </button>
          ))}
        </div>
      </div>
    </section>
  );
}



import { motion } from "framer-motion";

export function AnimatedProductCards({ cards }) {
  return (
    <section className="py-12 px-4 md:px-12">
      <h2 className="text-3xl font-bold mb-8 text-center">🔥 What's Hot</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {cards.map((card, i) => (
          <motion.div
            key={i}
            whileHover={{ scale: 1.05 }}
            className="rounded-2xl shadow-lg bg-white overflow-hidden p-6 text-center"
          >
            <img src={card.image} alt={card.headline} className="w-24 h-24 mx-auto mb-4" />
            <h3 className="text-xl font-semibold">{card.headline}</h3>
            <p className="text-gray-600 mt-2">{card.subtext}</p>
          </motion.div>
        ))}
      </div>
    </section>
  );
}



import HeroVideo from "@/components/ui/HeroVideo";
import { AnimatedProductCards } from "@/components/cards/AnimatedProductCards";

const cards = [
  {
    headline: "🔥 Live Resin Hits",
    subtext: "High-terpene, high-vibes. Dabs for days.",
    image: "/assets/cards/live-resin.png"
  },
  {
    headline: "🌿 Rare Drops",
    subtext: "Limited strains you’ll brag about.",
    image: "/assets/cards/rare-drops.png"
  },
  {
    headline: "🛍️ Daily Deals",
    subtext: "Fresh savings, every single day.",
    image: "/assets/cards/daily-deals.png"
  }
];

export default function Home() {
  return (
    <>
      <HeroVideo
        headline="Craft Cannabis. Next Level."
        subheadline="Award-winning extracts. Rare strains. Daily deals. Let’s vibe."
        videoSrc="/assets/hero/loop-vapor.mp4"
        ctas={["Browse Products", "Find Your Store", "Explore Extracts"]}
      />
      <AnimatedProductCards cards={cards} />
      {/* Additional sections go here */}
    </>
  );
}




