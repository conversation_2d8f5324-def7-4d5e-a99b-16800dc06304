{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigationItems = [\n    { name: 'Home', href: '/' },\n    { name: 'Products', href: '/products' },\n    { name: 'Cultivars', href: '/cultivars' },\n    { name: 'Deals', href: '/deals' },\n    { name: 'Locations', href: '/locations' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <nav className=\"bg-cream-50 shadow-soft sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-2xl font-serif font-bold text-primary-800\">\n                Apothecary Extracts\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;sCAOpE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE5B,KAAK,IAAI;2BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAa9B;GAzGwB;KAAA", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/AgeVerification.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AgeVerificationProps {\n  onVerified: () => void;\n}\n\nexport default function AgeVerification({ onVerified }: AgeVerificationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Check if user has already verified their age\n    const hasVerified = localStorage.getItem('ageVerified');\n    if (!hasVerified) {\n      setIsVisible(true);\n    } else {\n      onVerified();\n    }\n  }, [onVerified]);\n\n  const handleVerification = (isOfAge: boolean) => {\n    if (isOfAge) {\n      localStorage.setItem('ageVerified', 'true');\n      setIsVisible(false);\n      onVerified();\n    } else {\n      // Redirect to educational resources or show message\n      window.location.href = 'https://www.samhsa.gov/marijuana';\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-serif font-bold text-primary-800 mb-2\">\n            Age Verification Required\n          </h2>\n          <p className=\"text-charcoal-700 text-sm leading-relaxed\">\n            You must be 21 years of age or older to view this website and purchase cannabis products.\n            Please verify your age to continue.\n          </p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={() => handleVerification(true)}\n            className=\"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2\"\n          >\n            Yes, I&apos;m 21 or older\n          </button>\n          \n          <button\n            onClick={() => handleVerification(false)}\n            className=\"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2\"\n          >\n            No, I&apos;m under 21\n          </button>\n        </div>\n        \n        <div className=\"mt-6 pt-4 border-t border-charcoal-200\">\n          <p className=\"text-xs text-charcoal-500 leading-relaxed\">\n            By entering this website, you certify that you are of legal age to purchase cannabis products \n            in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended \n            to diagnose, treat, cure, or prevent any disease.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,gBAAgB,EAAE,UAAU,EAAwB;;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,+CAA+C;YAC/C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,CAAC,aAAa;gBAChB,aAAa;YACf,OAAO;gBACL;YACF;QACF;oCAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,eAAe;YACpC,aAAa;YACb;QACF,OAAO;YACL,oDAAoD;YACpD,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAM3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;;;;;;;;;;;AASnE;GAjEwB;KAAA", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/Hero.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { motion } from 'framer-motion';\n\ninterface HeroProps {\n  videoSrc?: string;\n  headline?: string;\n  subheadline?: string;\n  ctas?: string[];\n}\n\nexport default function Hero({\n  videoSrc,\n  headline = \"Premium Cannabis\",\n  subheadline = \"Discover Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity.\",\n  ctas = [\"Shop Products\", \"Find Locations\"]\n}: HeroProps) {\n  return (\n    <section className=\"relative h-screen overflow-hidden bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50\">\n      {/* Video Background */}\n      {videoSrc && (\n        <video\n          autoPlay\n          muted\n          loop\n          playsInline\n          className=\"absolute w-full h-full object-cover opacity-30\"\n        >\n          <source src={videoSrc} type=\"video/mp4\" />\n        </video>\n      )}\n\n      {/* Fallback Background Pattern */}\n      {!videoSrc && (\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute inset-0 bg-primary-900\"></div>\n        </div>\n      )}\n\n      <div className=\"relative z-10 flex flex-col items-center justify-center h-full text-center px-6\">\n        <motion.h1\n          className=\"text-4xl md:text-6xl font-serif font-bold leading-tight mb-6\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          {headline}\n          <span className=\"block text-gold-300\">Excellence</span>\n        </motion.h1>\n\n        <motion.p\n          className=\"text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          {subheadline}\n        </motion.p>\n\n        <motion.div\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <Link\n            href=\"/products\"\n            className=\"inline-flex items-center justify-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800\"\n          >\n            {ctas[0] || \"Shop Products\"}\n            <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </Link>\n\n          <Link\n            href=\"/locations\"\n            className=\"inline-flex items-center justify-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cream-50 focus:ring-offset-2 focus:ring-offset-primary-800\"\n          >\n            {ctas[1] || \"Find Locations\"}\n            <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n          </Link>\n        </motion.div>\n      </div>\n\n      {/* Bottom wave */}\n      <div className=\"absolute bottom-0 left-0 right-0 z-20\">\n        <svg viewBox=\"0 0 1440 120\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z\" fill=\"#f8f6f0\"/>\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASe,SAAS,KAAK,EAC3B,QAAQ,EACR,WAAW,kBAAkB,EAC7B,cAAc,+GAA+G,EAC7H,OAAO;IAAC;IAAiB;CAAiB,EAChC;IACV,qBACE,6LAAC;QAAQ,WAAU;;YAEhB,0BACC,6LAAC;gBACC,QAAQ;gBACR,KAAK;gBACL,IAAI;gBACJ,WAAW;gBACX,WAAU;0BAEV,cAAA,6LAAC;oBAAO,KAAK;oBAAU,MAAK;;;;;;;;;;;YAK/B,CAAC,0BACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBA<PERSON>,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;4BAE3B;0CACD,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;kCAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC;;;;;;kCAGH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCAET,IAAI,CAAC,EAAE,IAAI;kDACZ,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;0CAIzE,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCAET,IAAI,CAAC,EAAE,IAAI;kDACZ,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;;0DACtE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;0DACrE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,SAAQ;oBAAe,MAAK;oBAAO,OAAM;8BAC5C,cAAA,6LAAC;wBAAK,GAAE;wBAA+N,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKtP;KArFwB", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ProductShowcase.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst productCategories = [\n  {\n    name: 'Premium Flower',\n    description: 'Hand-selected, top-shelf cannabis flower with exceptional quality and potency.',\n    image: '/api/placeholder/400/300',\n    href: '/products/flower',\n    features: ['Lab Tested', 'Organic Grown', 'Various Strains'],\n    color: 'from-primary-600 to-primary-700'\n  },\n  {\n    name: 'Concentrates & Extracts',\n    description: 'Pure, potent concentrates including wax, shatter, and live resin.',\n    image: '/api/placeholder/400/300',\n    href: '/products/concentrates',\n    features: ['High Potency', 'Pure Extraction', 'Multiple Forms'],\n    color: 'from-gold-500 to-gold-600'\n  },\n  {\n    name: 'Edibles',\n    description: 'Delicious, precisely dosed edibles for a controlled cannabis experience.',\n    image: '/api/placeholder/400/300',\n    href: '/products/edibles',\n    features: ['Precise Dosing', 'Great Taste', 'Long Lasting'],\n    color: 'from-sage-400 to-sage-500'\n  },\n  {\n    name: 'Topicals',\n    description: 'Therapeutic cannabis topicals for localized relief and wellness.',\n    image: '/api/placeholder/400/300',\n    href: '/products/topicals',\n    features: ['Non-Psychoactive', 'Therapeutic', 'Natural Relief'],\n    color: 'from-cream-400 to-cream-500'\n  }\n];\n\nexport default function ProductShowcase() {\n  return (\n    <section className=\"py-20 bg-cream-100\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl font-serif font-bold text-primary-800 mb-4\">\n            Our Product Categories\n          </h2>\n          <p className=\"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed\">\n            Explore our carefully curated selection of premium cannabis products,\n            each category offering unique benefits and experiences.\n          </p>\n        </motion.div>\n\n        {/* Product Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {productCategories.map((category, index) => (\n            <motion.div\n              key={category.name}\n              className=\"group bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              whileHover={{ scale: 1.05, y: -8 }}\n              transition={{\n                duration: 0.3,\n                delay: index * 0.1\n              }}\n              viewport={{ once: true }}\n            >\n              {/* Image Container */}\n              <div className={`h-48 bg-gradient-to-br ${category.color} relative overflow-hidden`}>\n                <div className=\"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300\"></div>\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-center text-cream-50\">\n                    <div className=\"w-16 h-16 mx-auto mb-2 bg-cream-50 bg-opacity-20 rounded-full flex items-center justify-center\">\n                      {/* Category Icons */}\n                      {index === 0 && (\n                        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                        </svg>\n                      )}\n                      {index === 1 && (\n                        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                        </svg>\n                      )}\n                      {index === 2 && (\n                        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                        </svg>\n                      )}\n                      {index === 3 && (\n                        <svg className=\"w-8 h-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z\"/>\n                        </svg>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-semibold text-primary-800 mb-2\">\n                  {category.name}\n                </h3>\n                <p className=\"text-charcoal-600 text-sm mb-4 leading-relaxed\">\n                  {category.description}\n                </p>\n\n                {/* Features */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {category.features.map((feature) => (\n                    <span\n                      key={feature}\n                      className=\"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n\n                {/* CTA */}\n                <Link\n                  href={category.href}\n                  className=\"inline-flex items-center text-primary-700 hover:text-primary-800 font-medium text-sm group-hover:underline transition-colors duration-200\"\n                >\n                  Explore {category.name}\n                  <svg className=\"ml-1 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* View All Products CTA */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <Link\n            href=\"/products\"\n            className=\"inline-flex items-center px-8 py-4 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2\"\n          >\n            View All Products\n            <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,oBAAoB;IACxB;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAc;YAAiB;SAAkB;QAC5D,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAgB;YAAmB;SAAiB;QAC/D,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAkB;YAAe;SAAe;QAC3D,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAoB;YAAe;SAAiB;QAC/D,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA8D;;;;;;;;;;;;8BAO7E,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;4BACjB;4BACA,UAAU;gCAAE,MAAM;4BAAK;;8CAGvB,6LAAC;oCAAI,WAAW,CAAC,uBAAuB,EAAE,SAAS,KAAK,CAAC,yBAAyB,CAAC;;sDACjF,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDAEZ,UAAU,mBACT,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;wDAGX,UAAU,mBACT,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;wDAGX,UAAU,mBACT,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;wDAGX,UAAU,mBACT,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAe,SAAQ;sEACnD,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAIvB,6LAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACtB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDASX,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,SAAS,IAAI;4CACnB,WAAU;;gDACX;gDACU,SAAS,IAAI;8DACtB,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;2BAvEtE,SAAS,IAAI;;;;;;;;;;8BAgFxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;4BACX;0CAEC,6LAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KA7HwB", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/LocationInfo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nconst locations = [\n  {\n    name: 'Colorado Springs - Garden of the Gods',\n    address: '414 Garden of the Gods Road, Colorado Springs, CO 80907',\n    phone: '(*************',\n    hours: {\n      weekdays: '9:00 AM - 9:00 PM',\n      saturday: '9:00 AM - 9:00 PM',\n      sunday: '10:00 AM - 8:00 PM'\n    },\n    services: ['Medical', 'Recreational', 'Curbside Pickup'],\n    isNew: true\n  },\n  {\n    name: 'Colorado Springs - Downtown',\n    address: '123 Main Street, Colorado Springs, CO 80903',\n    phone: '(*************',\n    hours: {\n      weekdays: '8:00 AM - 10:00 PM',\n      saturday: '8:00 AM - 10:00 PM',\n      sunday: '9:00 AM - 9:00 PM'\n    },\n    services: ['Medical', 'Recreational', 'Delivery', 'Consultation'],\n    isNew: false\n  }\n];\n\nexport default function LocationInfo() {\n  return (\n    <section className=\"py-20 bg-cream-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-serif font-bold text-primary-800 mb-4\">\n            Visit Our Dispensaries\n          </h2>\n          <p className=\"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed\">\n            Find us at convenient locations throughout Colorado Springs. \n            Each dispensary offers the full Apothecary Extracts experience.\n          </p>\n        </div>\n\n        {/* Locations Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\">\n          {locations.map((location) => (\n            <div\n              key={location.name}\n              className=\"bg-cream-100 rounded-xl p-8 shadow-soft hover:shadow-medium transition-shadow duration-300 relative overflow-hidden\"\n            >\n              {/* New Badge */}\n              {location.isNew && (\n                <div className=\"absolute top-4 right-4\">\n                  <span className=\"bg-gold-500 text-primary-800 px-3 py-1 rounded-full text-sm font-semibold\">\n                    New Location\n                  </span>\n                </div>\n              )}\n\n              <div className=\"mb-6\">\n                <h3 className=\"text-2xl font-semibold text-primary-800 mb-2\">\n                  {location.name}\n                </h3>\n                <p className=\"text-charcoal-600 mb-4 flex items-start\">\n                  <svg className=\"w-5 h-5 text-primary-600 mr-2 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n                  </svg>\n                  {location.address}\n                </p>\n                <p className=\"text-charcoal-600 mb-4 flex items-center\">\n                  <svg className=\"w-5 h-5 text-primary-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z\"/>\n                  </svg>\n                  {location.phone}\n                </p>\n              </div>\n\n              {/* Hours */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-lg font-semibold text-primary-800 mb-3\">Store Hours</h4>\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Monday - Friday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.weekdays}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Saturday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.saturday}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Sunday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.sunday}</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Services */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-lg font-semibold text-primary-800 mb-3\">Services</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {location.services.map((service) => (\n                    <span\n                      key={service}\n                      className=\"px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full\"\n                    >\n                      {service}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex flex-col sm:flex-row gap-3\">\n                <Link\n                  href={`/locations/${location.name.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '')}`}\n                  className=\"flex-1 bg-primary-800 text-cream-50 py-3 px-4 rounded-lg text-center font-medium hover:bg-primary-700 transition-colors duration-200\"\n                >\n                  View Details\n                </Link>\n                <a\n                  href={`https://maps.google.com/?q=${encodeURIComponent(location.address)}`}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex-1 bg-transparent text-primary-800 py-3 px-4 rounded-lg text-center font-medium border border-primary-800 hover:bg-primary-50 transition-colors duration-200\"\n                >\n                  Get Directions\n                </a>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"bg-gold-100 rounded-xl p-8 text-center\">\n          <h3 className=\"text-2xl font-serif font-bold text-primary-800 mb-4\">\n            Can&apos;t Make It to Our Store?\n          </h3>\n          <p className=\"text-charcoal-700 mb-6 max-w-2xl mx-auto\">\n            We offer curbside pickup and delivery services (where legally permitted) \n            to make your cannabis shopping experience as convenient as possible.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/delivery\"\n              className=\"inline-flex items-center px-6 py-3 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z\"/>\n                <path d=\"M5 6h5v2H5zm11.5 9c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z\"/>\n              </svg>\n              Delivery Info\n            </Link>\n            <Link\n              href=\"/curbside\"\n              className=\"inline-flex items-center px-6 py-3 bg-transparent text-primary-800 font-semibold rounded-lg border border-primary-800 hover:bg-primary-50 transition-colors duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z\"/>\n              </svg>\n              Curbside Pickup\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,UAAU;YAAC;YAAW;YAAgB;SAAkB;QACxD,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,UAAU;YAAC;YAAW;YAAgB;YAAY;SAAe;QACjE,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA8D;;;;;;;;;;;;8BAO7E,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;4BAEC,WAAU;;gCAGT,SAAS,KAAK,kBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;8CAMhG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,WAAU;oDAAqD,MAAK;oDAAe,SAAQ;8DAC9F,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;gDAET,SAAS,OAAO;;;;;;;sDAEnB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,WAAU;oDAAgC,MAAK;oDAAe,SAAQ;8DACzE,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;gDAET,SAAS,KAAK;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,QAAQ;;;;;;;;;;;;8DAE1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,QAAQ;;;;;;;;;;;;8DAE1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAM5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACtB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;8CAUb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,eAAe,KAAK;4CACjG,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAM,CAAC,2BAA2B,EAAE,mBAAmB,SAAS,OAAO,GAAG;4CAC1E,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;2BA7EE,SAAS,IAAI;;;;;;;;;;8BAsFxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;;8DACxD,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;;;;;;;wCACJ;;;;;;;8CAGR,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;sDACxD,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;wCACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;KA1IwB", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ComplianceDisclaimer.tsx"], "sourcesContent": ["export default function ComplianceDisclaimer() {\n  return (\n    <div className=\"bg-gold-100 border border-gold-400 rounded-lg p-4 mb-6\">\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-gold-600 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3\">\n          <h3 className=\"text-sm font-semibold text-gold-800 mb-2\">\n            Important Legal Information\n          </h3>\n          <div className=\"text-sm text-gold-700 space-y-2\">\n            <p>\n              <strong>Colorado State Compliance:</strong> This establishment is licensed by the State of Colorado \n              to sell cannabis products to adults 21 years of age and older. License #: [LICENSE_NUMBER]\n            </p>\n            <p>\n              <strong>Health & Safety:</strong> Cannabis products have not been evaluated by the FDA and are not \n              intended to diagnose, treat, cure, or prevent any disease. Keep out of reach of children and pets.\n            </p>\n            <p>\n              <strong>Consumption Guidelines:</strong> Do not operate vehicles or machinery after use. \n              Effects may be delayed with edible products. Start with low doses and wait before consuming more.\n            </p>\n            <p>\n              <strong>Legal Restrictions:</strong> Cannabis products may not be transported across state lines. \n              Consumption is prohibited in public places and federal properties.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAe,SAAQ;kCACxE,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoN,UAAS;;;;;;;;;;;;;;;;8BAG5P,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAmC;;;;;;;8CAG7C,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAGnC,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAgC;;;;;;;8CAG1C,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;KAnCwB", "debugId": null}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Navigation from '@/components/Navigation';\nimport AgeVerification from '@/components/AgeVerification';\nimport Hero from '@/components/Hero';\nimport ProductShowcase from '@/components/ProductShowcase';\nimport LocationInfo from '@/components/LocationInfo';\nimport ComplianceDisclaimer from '@/components/ComplianceDisclaimer';\n\nexport default function Home() {\n  const [isAgeVerified, setIsAgeVerified] = useState(false);\n\n  return (\n    <>\n      {!isAgeVerified && (\n        <AgeVerification onVerified={() => setIsAgeVerified(true)} />\n      )}\n\n      {isAgeVerified && (\n        <div className=\"min-h-screen bg-cream-100\">\n          <Navigation />\n\n          <main>\n            {/* Hero Section */}\n            <Hero />\n\n            {/* Compliance Disclaimer */}\n            <section className=\"py-8 bg-cream-100\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <ComplianceDisclaimer />\n              </div>\n            </section>\n\n            {/* Product Categories Section */}\n            <ProductShowcase />\n\n            {/* Location Information */}\n            <LocationInfo />\n\n            {/* Newsletter Section */}\n            <section className=\"py-20 bg-primary-800\">\n              <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n                <h2 className=\"text-3xl font-serif font-bold text-cream-50 mb-4\">\n                  Stay Updated with Apothecary Extracts\n                </h2>\n                <p className=\"text-xl text-cream-200 mb-8\">\n                  Get the latest news on new products, special offers, and cannabis education.\n                </p>\n                <form className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                  <input\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent text-charcoal-800\"\n                    required\n                  />\n                  <button\n                    type=\"submit\"\n                    className=\"px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800\"\n                  >\n                    Subscribe\n                  </button>\n                </form>\n                <p className=\"text-sm text-cream-300 mt-4\">\n                  We respect your privacy. Unsubscribe at any time.\n                </p>\n              </div>\n            </section>\n          </main>\n\n          {/* Footer */}\n          <footer className=\"bg-charcoal-800 text-cream-100 py-16\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n                {/* Company Info */}\n                <div className=\"col-span-1 md:col-span-2\">\n                  <div className=\"text-2xl font-serif font-bold text-cream-50 mb-4\">\n                    Apothecary Extracts\n                  </div>\n                  <p className=\"text-cream-300 mb-4 leading-relaxed\">\n                    Colorado&apos;s premier cannabis dispensary, committed to providing\n                    the highest quality products and exceptional customer service.\n                  </p>\n                  <div className=\"flex space-x-4\">\n                    <a href=\"#\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">\n                      <span className=\"sr-only\">Facebook</span>\n                      <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                      </svg>\n                    </a>\n                    <a href=\"#\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">\n                      <span className=\"sr-only\">Instagram</span>\n                      <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"/>\n                      </svg>\n                    </a>\n                  </div>\n                </div>\n\n                {/* Quick Links */}\n                <div>\n                  <h3 className=\"text-lg font-semibold text-cream-50 mb-4\">Quick Links</h3>\n                  <ul className=\"space-y-2\">\n                    <li><a href=\"/products\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Products</a></li>\n                    <li><a href=\"/locations\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Locations</a></li>\n                    <li><a href=\"/about\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">About Us</a></li>\n                    <li><a href=\"/education\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Education</a></li>\n                  </ul>\n                </div>\n\n                {/* Legal */}\n                <div>\n                  <h3 className=\"text-lg font-semibold text-cream-50 mb-4\">Legal</h3>\n                  <ul className=\"space-y-2\">\n                    <li><a href=\"/privacy\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Privacy Policy</a></li>\n                    <li><a href=\"/terms\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Terms of Service</a></li>\n                    <li><a href=\"/compliance\" className=\"text-cream-300 hover:text-gold-400 transition-colors\">Compliance</a></li>\n                  </ul>\n                </div>\n              </div>\n\n              <div className=\"border-t border-charcoal-600 mt-12 pt-8 text-center\">\n                <p className=\"text-cream-400 text-sm\">\n                  © 2025 Apothecary Extracts. All rights reserved. |\n                  Licensed Cannabis Retailer | Must be 21+ to purchase\n                </p>\n              </div>\n            </div>\n          </footer>\n        </div>\n      )}\n    </>\n  );\n}\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE;;YACG,CAAC,+BACA,6LAAC,wIAAA,CAAA,UAAe;gBAAC,YAAY,IAAM,iBAAiB;;;;;;YAGrD,+BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,UAAU;;;;;kCAEX,6LAAC;;0CAEC,6LAAC,6HAAA,CAAA,UAAI;;;;;0CAGL,6LAAC;gCAAQ,WAAU;0CACjB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;0CAKzB,6LAAC,wIAAA,CAAA,UAAe;;;;;0CAGhB,6LAAC,qIAAA,CAAA,UAAY;;;;;0CAGb,6LAAC;gCAAQ,WAAU;0CACjB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;sDAG3C,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;8DAEV,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;sDAIH,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmD;;;;;;8DAGlE,6LAAC;oDAAE,WAAU;8DAAsC;;;;;;8DAInD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,MAAK;4DAAI,WAAU;;8EACpB,6LAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAe,SAAQ;8EACnD,cAAA,6LAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;sEAGZ,6LAAC;4DAAE,MAAK;4DAAI,WAAU;;8EACpB,6LAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAe,SAAQ;8EACnD,cAAA,6LAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOhB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAY,WAAU;0EAAuD;;;;;;;;;;;sEACzF,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAa,WAAU;0EAAuD;;;;;;;;;;;sEAC1F,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAS,WAAU;0EAAuD;;;;;;;;;;;sEACtF,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAa,WAAU;0EAAuD;;;;;;;;;;;;;;;;;;;;;;;sDAK9F,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAW,WAAU;0EAAuD;;;;;;;;;;;sEACxF,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAS,WAAU;0EAAuD;;;;;;;;;;;sEACtF,6LAAC;sEAAG,cAAA,6LAAC;gEAAE,MAAK;gEAAc,WAAU;0EAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKjG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GA3HwB;KAAA", "debugId": null}}]}