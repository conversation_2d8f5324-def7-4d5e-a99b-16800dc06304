/**
 * Apothecary Farms Visual Components Library
 * Export all visual components for easy importing
 */

// Component exports - All components are now implemented
export { default as ScrollTriggeredFadeIn } from './ScrollTriggeredFadeIn';
export { default as ImageZoomOnHover } from './ImageZoomOnHover';
export { default as SectionRevealWipe } from './SectionRevealWipe';
export { default as NoiseOverlayGrain } from './NoiseOverlayGrain';
export { default as DynamicGradientText } from './DynamicGradientText';
export { default as Tilt3DCardEffect } from './Tilt3DCardEffect';
export { default as TypewriterIntroText } from './TypewriterIntroText';
export { default as ParticleCanvasBackground } from './ParticleCanvasBackground';
export { default as LottieIconAnimations } from './LottieIconAnimations';
export { default as MorphingBlobHero } from './MorphingBlobHero';

// Type exports
export type {
  ScrollTriggeredFadeInProps,
  ImageZoomOnHoverProps,
  SectionRevealWipeProps,
  NoiseOverlayGrainProps,
  DynamicGradientTextProps,
  Tilt3DCardEffectProps,
  TypewriterIntroTextProps,
  ParticleCanvasBackgroundProps,
  LottieIconAnimationsProps,
  MorphingBlobHeroProps,
  TerpeneTheme,
  AnimationTrigger,
  Direction,
  WipeDirection,
} from '../../types/visual';

// Brand utilities export
export { default as brand } from '../../styles/brand';

// Demo components export
export { default as ComponentDemo } from './demos/ComponentDemo';
export { default as VisualComponentsShowcase } from './demos/VisualComponentsShowcase';
