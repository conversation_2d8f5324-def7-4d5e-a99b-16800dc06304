'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

interface SliderCard {
  id: string;
  title: string;
  description?: string;
  image?: string;
  icon?: string;
  emoji?: string;
  badge?: string;
  price?: string;
  cta?: {
    text: string;
    action: () => void;
  };
}

interface CardSliderProps {
  cards: SliderCard[];
  autoScroll?: boolean;
  autoScrollInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  slidesToShow?: number;
  gap?: number;
  className?: string;
}

export default function CardSlider({
  cards,
  autoScroll = true,
  autoScrollInterval = 4000,
  showDots = true,
  showArrows = true,
  slidesToShow = 3,
  gap = 24,
  className = ''
}: CardSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const maxIndex = Math.max(0, cards.length - slidesToShow);

  // Auto-scroll functionality
  useEffect(() => {
    if (autoScroll && !isHovered && cards.length > slidesToShow) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));
      }, autoScrollInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoScroll, isHovered, autoScrollInterval, maxIndex, cards.length, slidesToShow]);

  const goToSlide = (index: number) => {
    setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));
  };

  const nextSlide = () => {
    setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => (prev <= 0 ? maxIndex : prev - 1));
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    hover: { y: -8, scale: 1.02 }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Slider Container */}
      <div className="overflow-hidden">
        <motion.div
          className="flex transition-transform duration-500 ease-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / slidesToShow)}%)`,
            gap: `${gap}px`
          }}
        >
          {cards.map((card, index) => (
            <motion.div
              key={card.id}
              variants={cardVariants}
              whileHover="hover"
              className="flex-shrink-0"
              style={{ width: `calc(${100 / slidesToShow}% - ${gap * (slidesToShow - 1) / slidesToShow}px)` }}
            >
              <div className="bg-cream-50 rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden group">
                {/* Image/Icon/Emoji */}
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-sage-100 flex items-center justify-center">
                  {card.image ? (
                    <Image
                      src={card.image}
                      alt={card.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : card.emoji ? (
                    <span className="text-6xl">{card.emoji}</span>
                  ) : card.icon ? (
                    <div className="text-4xl text-primary-600">{card.icon}</div>
                  ) : (
                    <div className="w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 2L3 7v11h14V7l-7-5z" />
                      </svg>
                    </div>
                  )}

                  {/* Badge */}
                  {card.badge && (
                    <div className="absolute top-3 right-3">
                      <span className="bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full">
                        {card.badge}
                      </span>
                    </div>
                  )}

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {card.title}
                  </h3>

                  {card.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {card.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    {card.price && (
                      <span className="text-lg font-bold text-primary-600">
                        {card.price}
                      </span>
                    )}

                    {card.cta && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={card.cta.action}
                        className="bg-primary-600 text-cream-50 px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                      >
                        {card.cta.text}
                      </motion.button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Navigation Arrows */}
      {showArrows && cards.length > slidesToShow && (
        <>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full shadow-medium flex items-center justify-center transition-colors duration-200 z-10"
            aria-label="Previous slide"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full shadow-medium flex items-center justify-center transition-colors duration-200 z-10"
            aria-label="Next slide"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.button>
        </>
      )}

      {/* Dots Indicator */}
      {showDots && cards.length > slidesToShow && (
        <div className="flex justify-center mt-6 space-x-2">
          {Array.from({ length: maxIndex + 1 }).map((_, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.8 }}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentIndex
                  ? 'bg-primary-600'
                  : 'bg-cream-300 hover:bg-primary-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      {autoScroll && !isHovered && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-cream-200">
          <motion.div
            className="h-full bg-primary-600"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{
              duration: autoScrollInterval / 1000,
              ease: 'linear',
              repeat: Infinity
            }}
          />
        </div>
      )}
    </motion.div>
  );
}

// Example usage
export function ExampleCardSlider() {
  const sampleCards: SliderCard[] = [
    {
      id: '1',
      title: '🔥 Live Resin Hits',
      description: 'High-terpene, high-vibes. Dabs for days.',
      emoji: '🔥',
      badge: 'Hot',
      price: '$45',
      cta: { text: 'Shop Now', action: () => console.log('Shop Live Resin') }
    },
    {
      id: '2',
      title: '🌿 Rare Drops',
      description: 'Limited strains you\'ll brag about.',
      emoji: '🌿',
      badge: 'Limited',
      price: '$60',
      cta: { text: 'Shop Now', action: () => console.log('Shop Rare Drops') }
    },
    {
      id: '3',
      title: '🛍️ Daily Deals',
      description: 'Fresh savings, every single day.',
      emoji: '🛍️',
      badge: 'Sale',
      price: '$25',
      cta: { text: 'Shop Now', action: () => console.log('Shop Daily Deals') }
    },
    {
      id: '4',
      title: '💎 Premium Extracts',
      description: 'The finest concentrates available.',
      emoji: '💎',
      badge: 'Premium',
      price: '$80',
      cta: { text: 'Shop Now', action: () => console.log('Shop Premium') }
    },
    {
      id: '5',
      title: '🍃 Organic Flower',
      description: 'Sustainably grown, naturally potent.',
      emoji: '🍃',
      badge: 'Organic',
      price: '$35',
      cta: { text: 'Shop Now', action: () => console.log('Shop Organic') }
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-3xl font-serif font-bold text-center mb-8">🔥 What's Hot</h2>
      <CardSlider 
        cards={sampleCards} 
        autoScroll={true}
        slidesToShow={3}
        autoScrollInterval={5000}
      />
    </div>
  );
}
