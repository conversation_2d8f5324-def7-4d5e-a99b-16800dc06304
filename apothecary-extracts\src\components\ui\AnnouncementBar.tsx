'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import Link from 'next/link';

interface AnnouncementBarProps {
  message: string;
  ctaText?: string;
  ctaLink?: string;
  type?: 'info' | 'success' | 'warning' | 'promotion';
  dismissible?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

export default function AnnouncementBar({
  message,
  ctaText,
  ctaLink,
  type = 'info',
  dismissible = true,
  autoHide = false,
  autoHideDelay = 5000,
  className = ''
}: AnnouncementBarProps) {
  const [isVisible, setIsVisible] = useState(true);

  const typeStyles = {
    info: {
      bg: 'bg-primary-600',
      text: 'text-cream-50',
      accent: 'text-gold-300',
      button: 'bg-gold-500 hover:bg-gold-400 text-primary-800'
    },
    success: {
      bg: 'bg-sage-500',
      text: 'text-cream-50',
      accent: 'text-cream-200',
      button: 'bg-cream-50 hover:bg-cream-100 text-sage-700'
    },
    warning: {
      bg: 'bg-gold-500',
      text: 'text-primary-800',
      accent: 'text-primary-700',
      button: 'bg-primary-600 hover:bg-primary-700 text-cream-50'
    },
    promotion: {
      bg: 'bg-gradient-to-r from-primary-600 to-sage-500',
      text: 'text-cream-50',
      accent: 'text-gold-300',
      button: 'bg-gold-500 hover:bg-gold-400 text-primary-800'
    }
  };

  const currentStyle = typeStyles[type];

  // Auto-hide functionality
  if (autoHide && isVisible) {
    setTimeout(() => setIsVisible(false), autoHideDelay);
  }

  const barVariants = {
    hidden: {
      height: 0,
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    },
    visible: {
      height: 'auto',
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  const contentVariants = {
    hidden: {
      y: -20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        delay: 0.1,
        duration: 0.3
      }
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          variants={barVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className={`relative overflow-hidden ${currentStyle.bg} ${className}`}
        >
          <motion.div
            variants={contentVariants}
            className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
          >
            <div className="flex items-center justify-between py-3">
              <div className="flex items-center space-x-4">
                {/* Icon based on type */}
                <div className={`flex-shrink-0 ${currentStyle.accent}`}>
                  {type === 'info' && (
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  )}
                  {type === 'success' && (
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                  {type === 'warning' && (
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                  {type === 'promotion' && (
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  )}
                </div>

                {/* Message */}
                <p className={`text-sm font-medium ${currentStyle.text}`}>
                  {message}
                </p>
              </div>

              <div className="flex items-center space-x-4">
                {/* CTA Button */}
                {ctaText && ctaLink && (
                  <Link
                    href={ctaLink}
                    className={`inline-flex items-center px-3 py-1 rounded-md text-xs font-semibold transition-colors duration-200 ${currentStyle.button}`}
                  >
                    {ctaText}
                    <svg className="ml-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                )}

                {/* Dismiss Button */}
                {dismissible && (
                  <button
                    onClick={() => setIsVisible(false)}
                    className={`${currentStyle.text} hover:opacity-75 transition-opacity duration-200`}
                    aria-label="Dismiss announcement"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </motion.div>

          {/* Animated background pattern for promotion type */}
          {type === 'promotion' && (
            <motion.div
              className="absolute inset-0 opacity-10"
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%'],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: 'linear'
              }}
              style={{
                backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)',
                backgroundSize: '20px 20px'
              }}
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
