/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Attractor.js":
/*!***********************************!*\
  !*** ./dist/browser/Attractor.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attractor: () => (/* binding */ Attractor)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n/* harmony import */ var _Options_Classes_Attract_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Attract.js */ \"./dist/browser/Options/Classes/Attract.js\");\n\n\n\nconst attractMode = \"attract\";\nclass Attractor extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n  constructor(engine, container) {\n    super(container);\n    this._engine = engine;\n    if (!container.attract) {\n      container.attract = {\n        particles: []\n      };\n    }\n    this.handleClickMode = mode => {\n      const options = this.container.actualOptions,\n        attract = options.interactivity.modes.attract;\n      if (!attract || mode !== attractMode) {\n        return;\n      }\n      if (!container.attract) {\n        container.attract = {\n          particles: []\n        };\n      }\n      container.attract.clicking = true;\n      container.attract.count = 0;\n      for (const particle of container.attract.particles) {\n        if (!this.isEnabled(particle)) {\n          continue;\n        }\n        particle.velocity.setTo(particle.initialVelocity);\n      }\n      container.attract.particles = [];\n      container.attract.finish = false;\n      setTimeout(() => {\n        if (container.destroyed) {\n          return;\n        }\n        if (!container.attract) {\n          container.attract = {\n            particles: []\n          };\n        }\n        container.attract.clicking = false;\n      }, attract.duration * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds);\n    };\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      attract = container.actualOptions.interactivity.modes.attract;\n    if (!attract) {\n      return;\n    }\n    container.retina.attractModeDistance = attract.distance * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions,\n      mouseMoveStatus = container.interactivity.status === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.mouseMoveEvent,\n      events = options.interactivity.events,\n      {\n        enable: hoverEnabled,\n        mode: hoverMode\n      } = events.onHover,\n      {\n        enable: clickEnabled,\n        mode: clickMode\n      } = events.onClick;\n    if (mouseMoveStatus && hoverEnabled && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(attractMode, hoverMode)) {\n      (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.hoverAttract)(this._engine, this.container, p => this.isEnabled(p));\n    } else if (clickEnabled && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(attractMode, clickMode)) {\n      (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.clickAttract)(this._engine, this.container, p => this.isEnabled(p));\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      options = container.actualOptions,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? options.interactivity).events;\n    if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n      return false;\n    }\n    const hoverMode = events.onHover.mode,\n      clickMode = events.onClick.mode;\n    return (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(attractMode, hoverMode) || (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(attractMode, clickMode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.attract) {\n      options.attract = new _Options_Classes_Attract_js__WEBPACK_IMPORTED_MODULE_2__.Attract();\n    }\n    for (const source of sources) {\n      options.attract.load(source?.attract);\n    }\n  }\n  reset() {}\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-attract/./dist/browser/Attractor.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Attract.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Attract.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attract: () => (/* binding */ Attract)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass Attract {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.easing = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.EasingType.easeOutQuad;\n    this.factor = 1;\n    this.maxSpeed = 50;\n    this.speed = 1;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n    if (data.easing !== undefined) {\n      this.easing = data.easing;\n    }\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = data.maxSpeed;\n    }\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-attract/./dist/browser/Options/Classes/Attract.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clickAttract: () => (/* binding */ clickAttract),\n/* harmony export */   hoverAttract: () => (/* binding */ hoverAttract)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst minFactor = 1,\n  identity = 1,\n  minRadius = 0;\nfunction processAttract(engine, container, position, attractRadius, area, queryCb) {\n  const attractOptions = container.actualOptions.interactivity.modes.attract;\n  if (!attractOptions) {\n    return;\n  }\n  const query = container.particles.quadTree.query(area, queryCb);\n  for (const particle of query) {\n    const {\n        dx,\n        dy,\n        distance\n      } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(particle.position, position),\n      velocity = attractOptions.speed * attractOptions.factor,\n      attractFactor = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(engine.getEasing(attractOptions.easing)(identity - distance / attractRadius) * velocity, minFactor, attractOptions.maxSpeed),\n      normVec = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.create(!distance ? velocity : dx / distance * attractFactor, !distance ? velocity : dy / distance * attractFactor);\n    particle.position.subFrom(normVec);\n  }\n}\nfunction clickAttract(engine, container, enabledCb) {\n  if (!container.attract) {\n    container.attract = {\n      particles: []\n    };\n  }\n  const {\n    attract\n  } = container;\n  if (!attract.finish) {\n    if (!attract.count) {\n      attract.count = 0;\n    }\n    attract.count++;\n    if (attract.count === container.particles.count) {\n      attract.finish = true;\n    }\n  }\n  if (attract.clicking) {\n    const mousePos = container.interactivity.mouse.clickPosition,\n      attractRadius = container.retina.attractModeDistance;\n    if (!attractRadius || attractRadius < minRadius || !mousePos) {\n      return;\n    }\n    processAttract(engine, container, mousePos, attractRadius, new _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Circle(mousePos.x, mousePos.y, attractRadius), p => enabledCb(p));\n  } else if (attract.clicking === false) {\n    attract.particles = [];\n  }\n}\nfunction hoverAttract(engine, container, enabledCb) {\n  const mousePos = container.interactivity.mouse.position,\n    attractRadius = container.retina.attractModeDistance;\n  if (!attractRadius || attractRadius < minRadius || !mousePos) {\n    return;\n  }\n  processAttract(engine, container, mousePos, attractRadius, new _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Circle(mousePos.x, mousePos.y, attractRadius), p => enabledCb(p));\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-attract/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attract: () => (/* reexport safe */ _Options_Classes_Attract_js__WEBPACK_IMPORTED_MODULE_1__.Attract),\n/* harmony export */   loadExternalAttractInteraction: () => (/* binding */ loadExternalAttractInteraction)\n/* harmony export */ });\n/* harmony import */ var _Attractor_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Attractor.js */ \"./dist/browser/Attractor.js\");\n/* harmony import */ var _Options_Classes_Attract_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Attract.js */ \"./dist/browser/Options/Classes/Attract.js\");\n\nasync function loadExternalAttractInteraction(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addInteractor(\"externalAttract\", container => {\n    return Promise.resolve(new _Attractor_js__WEBPACK_IMPORTED_MODULE_0__.Attractor(engine, container));\n  }, refresh);\n}\n\n\n\n//# sourceURL=webpack://@tsparticles/interaction-external-attract/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});