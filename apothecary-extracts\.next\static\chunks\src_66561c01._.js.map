{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigationItems = [\n    { name: 'Home', href: '/' },\n    { name: 'Products', href: '/products' },\n    { name: 'Cultivars', href: '/cultivars' },\n    { name: 'Deals', href: '/deals' },\n    { name: 'Locations', href: '/locations' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <nav className=\"bg-cream-50 shadow-soft sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-2xl font-serif font-bold text-primary-800\">\n                Apothecary Extracts\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;sCAOpE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE5B,KAAK,IAAI;2BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAa9B;GAzGwB;KAAA", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/LocationInfo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nconst locations = [\n  {\n    name: 'Colorado Springs - Garden of the Gods',\n    address: '414 Garden of the Gods Road, Colorado Springs, CO 80907',\n    phone: '(*************',\n    hours: {\n      weekdays: '9:00 AM - 9:00 PM',\n      saturday: '9:00 AM - 9:00 PM',\n      sunday: '10:00 AM - 8:00 PM'\n    },\n    services: ['Medical', 'Recreational', 'Curbside Pickup'],\n    isNew: true\n  },\n  {\n    name: 'Colorado Springs - Downtown',\n    address: '123 Main Street, Colorado Springs, CO 80903',\n    phone: '(*************',\n    hours: {\n      weekdays: '8:00 AM - 10:00 PM',\n      saturday: '8:00 AM - 10:00 PM',\n      sunday: '9:00 AM - 9:00 PM'\n    },\n    services: ['Medical', 'Recreational', 'Delivery', 'Consultation'],\n    isNew: false\n  }\n];\n\nexport default function LocationInfo() {\n  return (\n    <section className=\"py-20 bg-cream-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-serif font-bold text-primary-800 mb-4\">\n            Visit Our Dispensaries\n          </h2>\n          <p className=\"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed\">\n            Find us at convenient locations throughout Colorado Springs. \n            Each dispensary offers the full Apothecary Extracts experience.\n          </p>\n        </div>\n\n        {/* Locations Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\">\n          {locations.map((location) => (\n            <div\n              key={location.name}\n              className=\"bg-cream-100 rounded-xl p-8 shadow-soft hover:shadow-medium transition-shadow duration-300 relative overflow-hidden\"\n            >\n              {/* New Badge */}\n              {location.isNew && (\n                <div className=\"absolute top-4 right-4\">\n                  <span className=\"bg-gold-500 text-primary-800 px-3 py-1 rounded-full text-sm font-semibold\">\n                    New Location\n                  </span>\n                </div>\n              )}\n\n              <div className=\"mb-6\">\n                <h3 className=\"text-2xl font-semibold text-primary-800 mb-2\">\n                  {location.name}\n                </h3>\n                <p className=\"text-charcoal-600 mb-4 flex items-start\">\n                  <svg className=\"w-5 h-5 text-primary-600 mr-2 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n                  </svg>\n                  {location.address}\n                </p>\n                <p className=\"text-charcoal-600 mb-4 flex items-center\">\n                  <svg className=\"w-5 h-5 text-primary-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z\"/>\n                  </svg>\n                  {location.phone}\n                </p>\n              </div>\n\n              {/* Hours */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-lg font-semibold text-primary-800 mb-3\">Store Hours</h4>\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Monday - Friday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.weekdays}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Saturday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.saturday}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-charcoal-600\">Sunday:</span>\n                    <span className=\"text-charcoal-800 font-medium\">{location.hours.sunday}</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Services */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-lg font-semibold text-primary-800 mb-3\">Services</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {location.services.map((service) => (\n                    <span\n                      key={service}\n                      className=\"px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full\"\n                    >\n                      {service}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex flex-col sm:flex-row gap-3\">\n                <Link\n                  href={`/locations/${location.name.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '')}`}\n                  className=\"flex-1 bg-primary-800 text-cream-50 py-3 px-4 rounded-lg text-center font-medium hover:bg-primary-700 transition-colors duration-200\"\n                >\n                  View Details\n                </Link>\n                <a\n                  href={`https://maps.google.com/?q=${encodeURIComponent(location.address)}`}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex-1 bg-transparent text-primary-800 py-3 px-4 rounded-lg text-center font-medium border border-primary-800 hover:bg-primary-50 transition-colors duration-200\"\n                >\n                  Get Directions\n                </a>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"bg-gold-100 rounded-xl p-8 text-center\">\n          <h3 className=\"text-2xl font-serif font-bold text-primary-800 mb-4\">\n            Can&apos;t Make It to Our Store?\n          </h3>\n          <p className=\"text-charcoal-700 mb-6 max-w-2xl mx-auto\">\n            We offer curbside pickup and delivery services (where legally permitted) \n            to make your cannabis shopping experience as convenient as possible.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/delivery\"\n              className=\"inline-flex items-center px-6 py-3 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z\"/>\n                <path d=\"M5 6h5v2H5zm11.5 9c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z\"/>\n              </svg>\n              Delivery Info\n            </Link>\n            <Link\n              href=\"/curbside\"\n              className=\"inline-flex items-center px-6 py-3 bg-transparent text-primary-800 font-semibold rounded-lg border border-primary-800 hover:bg-primary-50 transition-colors duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z\"/>\n              </svg>\n              Curbside Pickup\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,UAAU;YAAC;YAAW;YAAgB;SAAkB;QACxD,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,UAAU;YAAC;YAAW;YAAgB;YAAY;SAAe;QACjE,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA8D;;;;;;;;;;;;8BAO7E,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;4BAEC,WAAU;;gCAGT,SAAS,KAAK,kBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;8CAMhG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,WAAU;oDAAqD,MAAK;oDAAe,SAAQ;8DAC9F,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;gDAET,SAAS,OAAO;;;;;;;sDAEnB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,WAAU;oDAAgC,MAAK;oDAAe,SAAQ;8DACzE,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;gDAET,SAAS,KAAK;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,QAAQ;;;;;;;;;;;;8DAE1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,QAAQ;;;;;;;;;;;;8DAE1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;sEACpC,6LAAC;4DAAK,WAAU;sEAAiC,SAAS,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAM5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACtB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;8CAUb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,eAAe,KAAK;4CACjG,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAM,CAAC,2BAA2B,EAAE,mBAAmB,SAAS,OAAO,GAAG;4CAC1E,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;2BA7EE,SAAS,IAAI;;;;;;;;;;8BAsFxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;;8DACxD,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;;;;;;;wCACJ;;;;;;;8CAGR,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;sDACxD,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;wCACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;KA1IwB", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ComplianceDisclaimer.tsx"], "sourcesContent": ["export default function ComplianceDisclaimer() {\n  return (\n    <div className=\"bg-gold-100 border border-gold-400 rounded-lg p-4 mb-6\">\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-gold-600 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3\">\n          <h3 className=\"text-sm font-semibold text-gold-800 mb-2\">\n            Important Legal Information\n          </h3>\n          <div className=\"text-sm text-gold-700 space-y-2\">\n            <p>\n              <strong>Colorado State Compliance:</strong> This establishment is licensed by the State of Colorado \n              to sell cannabis products to adults 21 years of age and older. License #: [LICENSE_NUMBER]\n            </p>\n            <p>\n              <strong>Health & Safety:</strong> Cannabis products have not been evaluated by the FDA and are not \n              intended to diagnose, treat, cure, or prevent any disease. Keep out of reach of children and pets.\n            </p>\n            <p>\n              <strong>Consumption Guidelines:</strong> Do not operate vehicles or machinery after use. \n              Effects may be delayed with edible products. Start with low doses and wait before consuming more.\n            </p>\n            <p>\n              <strong>Legal Restrictions:</strong> Cannabis products may not be transported across state lines. \n              Consumption is prohibited in public places and federal properties.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAe,SAAQ;kCACxE,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoN,UAAS;;;;;;;;;;;;;;;;8BAG5P,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAmC;;;;;;;8CAG7C,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAGnC,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAgC;;;;;;;8CAG1C,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;KAnCwB", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/app/locations/page.tsx"], "sourcesContent": ["'use client';\n\nimport Navigation from '@/components/Navigation';\nimport LocationInfo from '@/components/LocationInfo';\nimport ComplianceDisclaimer from '@/components/ComplianceDisclaimer';\n\nexport default function LocationsPage() {\n  return (\n    <div className=\"min-h-screen bg-cream-100\">\n      <Navigation />\n      \n      <main>\n        {/* Header */}\n        <section className=\"bg-gradient-to-br from-primary-800 to-primary-600 text-cream-50 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl sm:text-5xl font-serif font-bold mb-4\">\n                Our Locations\n              </h1>\n              <p className=\"text-xl text-cream-200 max-w-3xl mx-auto\">\n                Visit us at our convenient Colorado Springs locations for the full Apothecary Extracts experience.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Compliance */}\n        <section className=\"py-8 bg-cream-100\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <ComplianceDisclaimer />\n          </div>\n        </section>\n\n        {/* Locations */}\n        <LocationInfo />\n\n        {/* Additional Info */}\n        <section className=\"py-16 bg-cream-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n              <div>\n                <h2 className=\"text-3xl font-serif font-bold text-primary-800 mb-6\">\n                  What to Expect\n                </h2>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1\">\n                      <svg className=\"w-3 h-3 text-cream-50\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-primary-800 mb-1\">Expert Consultation</h3>\n                      <p className=\"text-charcoal-600\">Our knowledgeable budtenders will help you find the perfect products for your needs.</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1\">\n                      <svg className=\"w-3 h-3 text-cream-50\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-primary-800 mb-1\">Premium Selection</h3>\n                      <p className=\"text-charcoal-600\">Browse our extensive collection of lab-tested, high-quality cannabis products.</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-primary-800 rounded-full flex items-center justify-center mr-3 mt-1\">\n                      <svg className=\"w-3 h-3 text-cream-50\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-primary-800 mb-1\">Safe Environment</h3>\n                      <p className=\"text-charcoal-600\">Clean, secure, and welcoming dispensary environment with strict safety protocols.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div>\n                <h2 className=\"text-3xl font-serif font-bold text-primary-800 mb-6\">\n                  Visit Requirements\n                </h2>\n                <div className=\"bg-gold-100 rounded-lg p-6\">\n                  <ul className=\"space-y-3 text-charcoal-700\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-gold-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Must be 21+ years old\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-gold-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Valid government-issued ID required\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-gold-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Cash, debit, or CanPay accepted\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-gold-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Medical patients welcome\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-charcoal-800 text-cream-100 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-serif font-bold text-cream-50 mb-4\">\n              Apothecary Extracts\n            </div>\n            <p className=\"text-cream-300 mb-4\">\n              Colorado&apos;s premier cannabis dispensary\n            </p>\n            <p className=\"text-cream-400 text-sm\">\n              © 2025 Apothecary Extracts. All rights reserved. | Must be 21+ to purchase\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;kCAKzB,6LAAC,qIAAA,CAAA,UAAY;;;;;kCAGb,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAwB,MAAK;oEAAe,SAAQ;8EACjE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;;;;;;0EAG7J,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAsC;;;;;;kFACpD,6LAAC;wEAAE,WAAU;kFAAoB;;;;;;;;;;;;;;;;;;kEAIrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAwB,MAAK;oEAAe,SAAQ;8EACjE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;;;;;;0EAG7J,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAsC;;;;;;kFACpD,6LAAC;wEAAE,WAAU;kFAAoB;;;;;;;;;;;;;;;;;;kEAIrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAwB,MAAK;oEAAe,SAAQ;8EACjE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;;;;;;0EAG7J,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAsC;;;;;;kFACpD,6LAAC;wEAAE,WAAU;kFAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMzC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAwI,UAAS;;;;;;;;;;;gEACxK;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAwI,UAAS;;;;;;;;;;;gEACxK;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAwI,UAAS;;;;;;;;;;;gEACxK;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAwI,UAAS;;;;;;;;;;;gEACxK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYtB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAmD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAGnC,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;KArIwB", "debugId": null}}]}