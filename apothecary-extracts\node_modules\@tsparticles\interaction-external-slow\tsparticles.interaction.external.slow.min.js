/*! For license information please see tsparticles.interaction.external.slow.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var r in o)("object"==typeof exports?exports:e)[r]=o[r]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function r(e){var i=o[e];if(void 0!==i)return i.exports;var n=o[e]={exports:{}};return t[e](n,n.exports,r),n.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};r.r(i),r.d(i,{Slow:()=>s,loadExternalSlowInteraction:()=>c});var n=r(303);class s{constructor(){this.factor=3,this.radius=200}load(e){(0,n.isNull)(e)||(void 0!==e.factor&&(this.factor=e.factor),void 0!==e.radius&&(this.radius=e.radius))}}class a extends n.ExternalInteractorBase{constructor(e){super(e)}clear(e,t,o){e.slow.inRange&&!o||(e.slow.factor=1)}init(){const e=this.container,t=e.actualOptions.interactivity.modes.slow;t&&(e.retina.slowModeRadius=t.radius*e.retina.pixelRatio)}interact(){}isEnabled(e){const t=this.container,o=t.interactivity.mouse,r=(e?.interactivity??t.actualOptions.interactivity).events;return r.onHover.enable&&!!o.position&&(0,n.isInArray)("slow",r.onHover.mode)}loadModeOptions(e,...t){e.slow||(e.slow=new s);for(const o of t)e.slow.load(o?.slow)}reset(e){e.slow.inRange=!1;const t=this.container,o=t.actualOptions,r=t.interactivity.mouse.position,i=t.retina.slowModeRadius,s=o.interactivity.modes.slow;if(!s||!i||i<0||!r)return;const a=e.getPosition(),c=(0,n.getDistance)(r,a),l=c/i,d=s.factor,{slow:u}=e;c>i||(u.inRange=!0,u.factor=l/d)}}async function c(e,t=!0){e.checkVersion("3.8.1"),await e.addInteractor("externalSlow",(e=>Promise.resolve(new a(e))),t)}return i})()));