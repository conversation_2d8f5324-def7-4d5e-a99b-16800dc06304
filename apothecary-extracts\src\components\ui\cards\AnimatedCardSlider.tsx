'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { AnimatedCardSliderProps, Product } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * AnimatedCardSlider Component
 * 
 * An animated card slider for product showcases with smooth transitions and touch support.
 * Features auto-advance, navigation controls, and responsive design.
 * 
 * @example
 * ```tsx
 * <AnimatedCardSlider
 *   products={products}
 *   visibleCards={3}
 *   autoAdvance={5000}
 *   touchEnabled={true}
 *   onCardClick={(product) => router.push(`/products/${product.id}`)}
 * />
 * ```
 */
const AnimatedCardSlider: React.FC<AnimatedCardSliderProps> = ({
  products,
  visibleCards = 3,
  autoAdvance = 4000,
  touchEnabled = true,
  showDots = true,
  showArrows = true,
  onCardClick,
  className = '',
  style,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const maxIndex = Math.max(0, products.length - visibleCards);

  // Auto-advance functionality
  useEffect(() => {
    if (autoAdvance && !isHovered && products.length > visibleCards) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));
      }, autoAdvance);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoAdvance, isHovered, maxIndex, products.length, visibleCards]);

  // Touch handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!touchEnabled) return;
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchEnabled) return;
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchEnabled || !touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));
  };

  const nextSlide = () => {
    setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => (prev <= 0 ? maxIndex : prev - 1));
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    hover: { y: -8, scale: 1.02 }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`relative ${className}`}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Slider Container */}
      <div className="overflow-hidden">
        <motion.div
          className="flex transition-transform duration-500 ease-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / visibleCards)}%)`,
            gap: '24px'
          }}
        >
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              variants={cardVariants}
              whileHover="hover"
              className="flex-shrink-0 cursor-pointer"
              style={{ width: `calc(${100 / visibleCards}% - ${24 * (visibleCards - 1) / visibleCards}px)` }}
              onClick={() => onCardClick?.(product)}
            >
              <div className="bg-cream-50 rounded-xl overflow-hidden group"
                   style={{ boxShadow: brandShadows.soft }}>
                {/* Product Image */}
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-sage-100">
                  {product.image ? (
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 2L3 7v11h14V7l-7-5z" />
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* Category Badge */}
                  <div className="absolute top-3 right-3">
                    <span 
                      className="text-xs font-semibold px-2 py-1 rounded-full text-cream-50"
                      style={{ backgroundColor: brandColors.primary[600] }}
                    >
                      {product.category}
                    </span>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {product.name}
                  </h3>

                  {product.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {product.description}
                    </p>
                  )}

                  {/* Product Details */}
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold text-primary-600">
                      {formatPrice(product.price)}
                    </span>
                    
                    {product.strain && (
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {product.strain}
                      </span>
                    )}
                  </div>

                  {/* THC/CBD Info */}
                  {(product.thc || product.cbd) && (
                    <div className="flex gap-2 mb-4">
                      {product.thc && (
                        <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded">
                          THC: {product.thc}%
                        </span>
                      )}
                      {product.cbd && (
                        <span className="text-xs bg-sage-100 text-sage-700 px-2 py-1 rounded">
                          CBD: {product.cbd}%
                        </span>
                      )}
                    </div>
                  )}

                  {/* Effects Tags */}
                  {product.effects && product.effects.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {product.effects.slice(0, 3).map((effect, idx) => (
                        <span 
                          key={idx}
                          className="text-xs bg-gold-100 text-gold-700 px-2 py-1 rounded"
                        >
                          {effect}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Navigation Arrows */}
      {showArrows && products.length > visibleCards && (
        <>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10"
            style={{ boxShadow: brandShadows.medium }}
            aria-label="Previous slide"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 z-10"
            style={{ boxShadow: brandShadows.medium }}
            aria-label="Next slide"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.button>
        </>
      )}

      {/* Dots Indicator */}
      {showDots && products.length > visibleCards && (
        <div className="flex justify-center mt-6 space-x-2">
          {Array.from({ length: maxIndex + 1 }).map((_, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.8 }}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentIndex
                  ? 'bg-primary-600'
                  : 'bg-cream-300 hover:bg-primary-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      {autoAdvance && !isHovered && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-cream-200">
          <motion.div
            className="h-full bg-primary-600"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{
              duration: autoAdvance / 1000,
              ease: 'linear',
              repeat: Infinity
            }}
          />
        </div>
      )}
    </motion.div>
  );
};

export default AnimatedCardSlider;
