/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Slow.js":
/*!**********************************************!*\
  !*** ./dist/browser/Options/Classes/Slow.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slow: () => (/* binding */ Slow)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass Slow {\n  constructor() {\n    this.factor = 3;\n    this.radius = 200;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-slow/./dist/browser/Options/Classes/Slow.js?");

/***/ }),

/***/ "./dist/browser/Slower.js":
/*!********************************!*\
  !*** ./dist/browser/Slower.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slower: () => (/* binding */ Slower)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Slow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Slow.js */ \"./dist/browser/Options/Classes/Slow.js\");\n\n\nconst slowMode = \"slow\",\n  minRadius = 0;\nclass Slower extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear(particle, delta, force) {\n    if (particle.slow.inRange && !force) {\n      return;\n    }\n    particle.slow.factor = 1;\n  }\n  init() {\n    const container = this.container,\n      slow = container.actualOptions.interactivity.modes.slow;\n    if (!slow) {\n      return;\n    }\n    container.retina.slowModeRadius = slow.radius * container.retina.pixelRatio;\n  }\n  interact() {}\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    return events.onHover.enable && !!mouse.position && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(slowMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.slow) {\n      options.slow = new _Options_Classes_Slow_js__WEBPACK_IMPORTED_MODULE_1__.Slow();\n    }\n    for (const source of sources) {\n      options.slow.load(source?.slow);\n    }\n  }\n  reset(particle) {\n    particle.slow.inRange = false;\n    const container = this.container,\n      options = container.actualOptions,\n      mousePos = container.interactivity.mouse.position,\n      radius = container.retina.slowModeRadius,\n      slowOptions = options.interactivity.modes.slow;\n    if (!slowOptions || !radius || radius < minRadius || !mousePos) {\n      return;\n    }\n    const particlePos = particle.getPosition(),\n      dist = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(mousePos, particlePos),\n      proximityFactor = dist / radius,\n      slowFactor = slowOptions.factor,\n      {\n        slow\n      } = particle;\n    if (dist > radius) {\n      return;\n    }\n    slow.inRange = true;\n    slow.factor = proximityFactor / slowFactor;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-slow/./dist/browser/Slower.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slow: () => (/* reexport safe */ _Options_Classes_Slow_js__WEBPACK_IMPORTED_MODULE_1__.Slow),\n/* harmony export */   loadExternalSlowInteraction: () => (/* binding */ loadExternalSlowInteraction)\n/* harmony export */ });\n/* harmony import */ var _Slower_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Slower.js */ \"./dist/browser/Slower.js\");\n/* harmony import */ var _Options_Classes_Slow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Slow.js */ \"./dist/browser/Options/Classes/Slow.js\");\n\nasync function loadExternalSlowInteraction(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addInteractor(\"externalSlow\", container => {\n    return Promise.resolve(new _Slower_js__WEBPACK_IMPORTED_MODULE_0__.Slower(container));\n  }, refresh);\n}\n\n\n\n//# sourceURL=webpack://@tsparticles/interaction-external-slow/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});