import Link from 'next/link';
import { motion } from 'framer-motion';

const productCategories = [
  {
    name: 'Premium Flower',
    description: 'Hand-selected, top-shelf cannabis flower with exceptional quality and potency.',
    image: '/api/placeholder/400/300',
    href: '/products/flower',
    features: ['Lab Tested', 'Organic Grown', 'Various Strains'],
    color: 'from-primary-600 to-primary-700'
  },
  {
    name: 'Concentrates & Extracts',
    description: 'Pure, potent concentrates including wax, shatter, and live resin.',
    image: '/api/placeholder/400/300',
    href: '/products/concentrates',
    features: ['High Potency', 'Pure Extraction', 'Multiple Forms'],
    color: 'from-gold-500 to-gold-600'
  },
  {
    name: 'Edibles',
    description: 'Delicious, precisely dosed edibles for a controlled cannabis experience.',
    image: '/api/placeholder/400/300',
    href: '/products/edibles',
    features: ['Precise Dosing', 'Great Taste', 'Long Lasting'],
    color: 'from-sage-400 to-sage-500'
  },
  {
    name: 'Topicals',
    description: 'Therapeutic cannabis topicals for localized relief and wellness.',
    image: '/api/placeholder/400/300',
    href: '/products/topicals',
    features: ['Non-Psychoactive', 'Therapeutic', 'Natural Relief'],
    color: 'from-cream-400 to-cream-500'
  }
];

export default function ProductShowcase() {
  return (
    <section className="py-20 bg-cream-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-serif font-bold text-primary-800 mb-4">
            Our Product Categories
          </h2>
          <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
            Explore our carefully curated selection of premium cannabis products,
            each category offering unique benefits and experiences.
          </p>
        </motion.div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {productCategories.map((category, index) => (
            <motion.div
              key={category.name}
              className="group bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.05, y: -8 }}
              transition={{
                duration: 0.3,
                delay: index * 0.1
              }}
              viewport={{ once: true }}
            >
              {/* Image Container */}
              <div className={`h-48 bg-gradient-to-br ${category.color} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-cream-50">
                    <div className="w-16 h-16 mx-auto mb-2 bg-cream-50 bg-opacity-20 rounded-full flex items-center justify-center">
                      {/* Category Icons */}
                      {index === 0 && (
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                      )}
                      {index === 1 && (
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                      )}
                      {index === 2 && (
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                      )}
                      {index === 3 && (
                        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/>
                        </svg>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-primary-800 mb-2">
                  {category.name}
                </h3>
                <p className="text-charcoal-600 text-sm mb-4 leading-relaxed">
                  {category.description}
                </p>

                {/* Features */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {category.features.map((feature) => (
                    <span
                      key={feature}
                      className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* CTA */}
                <Link
                  href={category.href}
                  className="inline-flex items-center text-primary-700 hover:text-primary-800 font-medium text-sm group-hover:underline transition-colors duration-200"
                >
                  Explore {category.name}
                  <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Products CTA */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Link
            href="/products"
            className="inline-flex items-center px-8 py-4 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2"
          >
            View All Products
            <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
