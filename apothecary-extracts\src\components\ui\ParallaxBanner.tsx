'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, ReactNode } from 'react';
import Image from 'next/image';

interface ParallaxBannerProps {
  backgroundImage: string;
  height?: string;
  children?: ReactNode;
  parallaxSpeed?: number;
  overlayOpacity?: number;
  overlayColor?: string;
  className?: string;
}

export default function ParallaxBanner({
  backgroundImage,
  height = 'h-screen',
  children,
  parallaxSpeed = 0.5,
  overlayOpacity = 0.4,
  overlayColor = 'black',
  className = ''
}: ParallaxBannerProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  });

  const y = useTransform(scrollYProgress, [0, 1], ['0%', `${parallaxSpeed * 100}%`]);
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);

  return (
    <div ref={ref} className={`relative overflow-hidden ${height} ${className}`}>
      {/* Parallax Background */}
      <motion.div
        style={{ y }}
        className="absolute inset-0 w-full h-[120%] -top-[10%]"
      >
        <Image
          src={backgroundImage}
          alt="Parallax background"
          fill
          className="object-cover"
          sizes="100vw"
          priority
        />
      </motion.div>

      {/* Overlay */}
      <div 
        className="absolute inset-0"
        style={{ 
          backgroundColor: overlayColor,
          opacity: overlayOpacity 
        }}
      />

      {/* Content */}
      <motion.div
        style={{ opacity }}
        className="relative z-10 h-full flex items-center justify-center"
      >
        {children}
      </motion.div>
    </div>
  );
}

// ScrollFadeIn Component
interface ScrollFadeInProps {
  children: ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  duration?: number;
  delay?: number;
  className?: string;
}

export function ScrollFadeIn({
  children,
  direction = 'up',
  distance = 50,
  duration = 0.6,
  delay = 0,
  className = ''
}: ScrollFadeInProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start 0.8', 'start 0.2']
  });

  const directionMap = {
    up: { y: distance, x: 0 },
    down: { y: -distance, x: 0 },
    left: { y: 0, x: distance },
    right: { y: 0, x: -distance }
  };

  const initialOffset = directionMap[direction];

  const x = useTransform(scrollYProgress, [0, 1], [initialOffset.x, 0]);
  const y = useTransform(scrollYProgress, [0, 1], [initialOffset.y, 0]);
  const opacity = useTransform(scrollYProgress, [0, 1], [0, 1]);

  return (
    <motion.div
      ref={ref}
      style={{ x, y, opacity }}
      transition={{ duration, delay, ease: 'easeOut' }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Example usage components
export function ExampleParallaxBanner() {
  return (
    <ParallaxBanner
      backgroundImage="/assets/backgrounds/cannabis-field.jpg"
      height="h-96"
      parallaxSpeed={0.3}
      overlayOpacity={0.5}
    >
      <div className="text-center text-white px-6">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-4xl md:text-5xl font-serif font-bold mb-4"
        >
          Sustainably Grown
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-xl md:text-2xl text-cream-200 max-w-2xl mx-auto"
        >
          Our organic cultivation practices ensure the highest quality cannabis while protecting the environment
        </motion.p>
      </div>
    </ParallaxBanner>
  );
}

export function ExampleScrollFadeIn() {
  return (
    <div className="space-y-12 py-16">
      <ScrollFadeIn direction="up" delay={0}>
        <div className="text-center">
          <h2 className="text-3xl font-serif font-bold text-foreground mb-4">
            Premium Quality
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Every product undergoes rigorous testing to ensure purity, potency, and safety.
          </p>
        </div>
      </ScrollFadeIn>

      <ScrollFadeIn direction="left" delay={0.2}>
        <div className="flex items-center space-x-6">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-foreground mb-2">Lab Tested</h3>
            <p className="text-gray-600">Comprehensive testing for cannabinoids, terpenes, and contaminants.</p>
          </div>
        </div>
      </ScrollFadeIn>

      <ScrollFadeIn direction="right" delay={0.4}>
        <div className="flex items-center space-x-6">
          <div className="w-16 h-16 bg-sage-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-sage-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-foreground mb-2">Organic Certified</h3>
            <p className="text-gray-600">Sustainably grown using organic farming practices.</p>
          </div>
        </div>
      </ScrollFadeIn>
    </div>
  );
}

// Combined Parallax Section with ScrollFadeIn elements
export function ParallaxSection() {
  return (
    <div className="space-y-0">
      {/* First parallax banner */}
      <ParallaxBanner
        backgroundImage="/assets/backgrounds/extraction-lab.jpg"
        height="h-96"
        parallaxSpeed={0.4}
        overlayOpacity={0.6}
      >
        <ScrollFadeIn direction="up">
          <div className="text-center text-white px-6">
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-4">
              State-of-the-Art Extraction
            </h2>
            <p className="text-xl text-cream-200 max-w-2xl mx-auto">
              Advanced CO2 extraction techniques preserve the full spectrum of cannabinoids and terpenes
            </p>
          </div>
        </ScrollFadeIn>
      </ParallaxBanner>

      {/* Content section with scroll animations */}
      <section className="py-16 bg-cream-50">
        <div className="max-w-6xl mx-auto px-6">
          <ExampleScrollFadeIn />
        </div>
      </section>

      {/* Second parallax banner */}
      <ParallaxBanner
        backgroundImage="/assets/backgrounds/dispensary-interior.jpg"
        height="h-80"
        parallaxSpeed={0.3}
        overlayOpacity={0.5}
      >
        <ScrollFadeIn direction="down">
          <div className="text-center text-white px-6">
            <h2 className="text-3xl md:text-4xl font-serif font-bold mb-4">
              Expert Guidance
            </h2>
            <p className="text-lg text-cream-200 max-w-xl mx-auto">
              Our knowledgeable budtenders help you find the perfect products for your needs
            </p>
          </div>
        </ScrollFadeIn>
      </ParallaxBanner>
    </div>
  );
}
