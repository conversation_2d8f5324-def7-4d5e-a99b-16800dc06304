(()=>{var e={};e.id=436,e.ids=[436],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>l});var s=r(5239),a=r(8088),n=r(8170),i=r.n(n),o=r(893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3920)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\demo\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3873:e=>{"use strict";e.exports=require("path")},3920:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\Apothecary\\\\apothecary-extracts\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\demo\\page.tsx","default")},4222:(e,t,r)=>{Promise.resolve().then(r.bind(r,6886))},6886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(687),a=r(3210),n=r(6246),i=r(7042),o=r(3492),c=r(2236);function l(){let[e,t]=(0,a.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[!e&&(0,s.jsx)(i.A,{onVerified:()=>t(!0)}),e&&(0,s.jsxs)("div",{className:"min-h-screen bg-cream-100",children:[(0,s.jsx)(n.A,{}),(0,s.jsxs)("main",{children:[(0,s.jsx)(o.A,{headline:"Premium Cannabis Excellence",subheadline:"Experience Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity with cutting-edge extraction techniques.",ctas:["Explore Products","Visit Locations"]}),(0,s.jsx)(c.A,{}),(0,s.jsx)("section",{className:"py-20 bg-primary-800",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-serif font-bold text-cream-50 mb-4",children:"Enhanced Components Demo"}),(0,s.jsx)("p",{className:"text-xl text-cream-200 mb-8",children:"This page demonstrates the enhanced Hero and ProductShowcase components with framer-motion animations."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 text-left",children:[(0,s.jsxs)("div",{className:"bg-cream-50 p-6 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-3",children:"Hero Enhancements"}),(0,s.jsxs)("ul",{className:"text-charcoal-600 space-y-2",children:[(0,s.jsx)("li",{children:"• Video background support"}),(0,s.jsx)("li",{children:"• Smooth fade-in animations"}),(0,s.jsx)("li",{children:"• Customizable headlines and CTAs"}),(0,s.jsx)("li",{children:"• Professional design system integration"}),(0,s.jsx)("li",{children:"• Full-screen responsive layout"})]})]}),(0,s.jsxs)("div",{className:"bg-cream-50 p-6 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-primary-800 mb-3",children:"ProductShowcase Enhancements"}),(0,s.jsxs)("ul",{className:"text-charcoal-600 space-y-2",children:[(0,s.jsx)("li",{children:"• Staggered card animations"}),(0,s.jsx)("li",{children:"• Hover scale effects"}),(0,s.jsx)("li",{children:"• Scroll-triggered animations"}),(0,s.jsx)("li",{children:"• Maintained professional styling"}),(0,s.jsx)("li",{children:"• Improved user engagement"})]})]})]})]})})]}),(0,s.jsx)("footer",{className:"bg-charcoal-800 text-cream-100 py-8",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsx)("p",{className:"text-cream-400 text-sm",children:"Demo Page - Enhanced Components | Apothecary Extracts"})})})]})]})}},8950:(e,t,r)=>{Promise.resolve().then(r.bind(r,3920))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,210,567,121,312],()=>r(3501));module.exports=s})();