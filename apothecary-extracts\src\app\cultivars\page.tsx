'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Head from 'next/head';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';
import ComplianceDisclaimer from '@/components/ComplianceDisclaimer';
import { siteConfig } from '@/content/site-config';

const strainCategories = [
  {
    type: "Sativa",
    description: "Energizing and uplifting effects, ideal for daytime use",
    color: "from-gold-500 to-gold-600",
    icon: "☀️"
  },
  {
    type: "Indica", 
    description: "Relaxing and sedating effects, perfect for evening use",
    color: "from-primary-600 to-primary-700",
    icon: "🌙"
  },
  {
    type: "Hybrid",
    description: "Balanced effects combining the best of both worlds",
    color: "from-sage-400 to-sage-500", 
    icon: "⚖️"
  }
];

const featuredStrains = [
  {
    name: "Papaya Cake",
    type: "Hybrid",
    thc: "22-26%",
    cbd: "<1%",
    effects: ["Relaxed", "Euphoric", "Creative"],
    flavors: ["Tropical", "Sweet", "Creamy"],
    description: "A delightful hybrid that combines tropical sweetness with relaxing effects. Perfect for creative endeavors and social situations.",
    image: "/api/placeholder/300/200",
    genetics: "Papaya × Wedding Cake"
  },
  {
    name: "GMO Crasher",
    type: "Indica",
    thc: "24-28%", 
    cbd: "<1%",
    effects: ["Sedating", "Relaxed", "Sleepy"],
    flavors: ["Garlic", "Diesel", "Earthy"],
    description: "A potent indica-dominant strain known for its unique savory profile and powerful relaxing effects. Ideal for evening use.",
    image: "/api/placeholder/300/200",
    genetics: "GMO × Wedding Crasher"
  },
  {
    name: "Pineapple Habanero",
    type: "Sativa",
    thc: "20-24%",
    cbd: "<1%", 
    effects: ["Energetic", "Creative", "Focused"],
    flavors: ["Pineapple", "Spicy", "Citrus"],
    description: "An invigorating sativa with a unique sweet and spicy flavor profile. Excellent for daytime productivity and creative projects.",
    image: "/api/placeholder/300/200",
    genetics: "Pineapple Express × Habanero Haze"
  },
  {
    name: "Purple Sunset",
    type: "Hybrid",
    thc: "18-22%",
    cbd: "1-2%",
    effects: ["Balanced", "Calm", "Happy"],
    flavors: ["Berry", "Grape", "Floral"],
    description: "A beautifully balanced hybrid with stunning purple hues and a complex berry flavor profile. Perfect for any time of day.",
    image: "/api/placeholder/300/200",
    genetics: "Purple Punch × Sunset Sherbet"
  },
  {
    name: "Colorado Kush",
    type: "Indica",
    thc: "19-23%",
    cbd: "<1%",
    effects: ["Relaxed", "Sleepy", "Pain Relief"],
    flavors: ["Pine", "Earthy", "Woody"],
    description: "A classic Colorado strain with traditional kush characteristics. Known for its therapeutic benefits and classic cannabis flavor.",
    image: "/api/placeholder/300/200",
    genetics: "Local Colorado Genetics"
  },
  {
    name: "Citrus Burst",
    type: "Sativa",
    thc: "21-25%",
    cbd: "<1%",
    effects: ["Uplifting", "Energetic", "Social"],
    flavors: ["Citrus", "Lemon", "Orange"],
    description: "A bright and energizing sativa with an explosive citrus flavor. Perfect for social gatherings and outdoor activities.",
    image: "/api/placeholder/300/200",
    genetics: "Tangie × Super Lemon Haze"
  }
];

export default function CultivarsPage() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("All");
  const { cultivars: pageConfig } = siteConfig.pages;

  const filteredStrains = selectedCategory === "All" 
    ? featuredStrains 
    : featuredStrains.filter(strain => strain.type === selectedCategory);

  return (
    <>
      <Head>
        <title>{pageConfig.seo.title}</title>
        <meta name="description" content={pageConfig.seo.description} />
        <meta property="og:title" content={pageConfig.seo.title} />
        <meta property="og:description" content={pageConfig.seo.description} />
        <meta name="age-restriction" content="21+" />
      </Head>

      {!isAgeVerified && (
        <AgeVerification onVerified={() => setIsAgeVerified(true)} />
      )}

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          <Navigation />

          <main>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden py-24 lg:py-32">
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-primary-900"></div>
              </div>
              
              <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <motion.h1 
                  className="text-4xl md:text-6xl font-serif font-bold leading-tight mb-6"
                  initial={{ opacity: 0, y: -20 }} 
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  {pageConfig.hero.headline}
                </motion.h1>

                <motion.p
                  className="text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  {pageConfig.hero.subheadline}
                </motion.p>
              </div>
            </section>

            {/* Compliance Disclaimer */}
            <section className="py-8 bg-cream-100">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <ComplianceDisclaimer />
              </div>
            </section>

            {/* Strain Categories */}
            <section className="py-16 bg-cream-100">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                  className="text-center mb-12"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-serif font-bold text-primary-800 mb-4">
                    Cannabis Categories
                  </h2>
                  <p className="text-lg text-charcoal-600 max-w-3xl mx-auto">
                    Understanding the differences between Sativa, Indica, and Hybrid strains helps you choose the perfect cannabis for your needs.
                  </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                  {strainCategories.map((category, index) => (
                    <motion.div
                      key={category.type}
                      className="bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ 
                        duration: 0.3,
                        delay: index * 0.1 
                      }}
                      viewport={{ once: true }}
                    >
                      <div className={`h-20 bg-gradient-to-br ${category.color} flex items-center justify-center`}>
                        <div className="text-center text-cream-50">
                          <div className="text-2xl mb-1">{category.icon}</div>
                          <div className="font-semibold">{category.type}</div>
                        </div>
                      </div>
                      <div className="p-6">
                        <p className="text-charcoal-600 text-sm leading-relaxed">
                          {category.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Filter Buttons */}
                <motion.div 
                  className="flex flex-wrap justify-center gap-4 mb-12"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  {["All", "Sativa", "Indica", "Hybrid"].map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
                        selectedCategory === category
                          ? "bg-primary-800 text-cream-50"
                          : "bg-cream-50 text-primary-800 hover:bg-primary-100"
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </motion.div>
              </div>
            </section>

            {/* Featured Strains */}
            <section className="py-20 bg-cream-200">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                  className="text-center mb-16"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-4xl font-serif font-bold text-primary-800 mb-4">
                    Featured Strains
                  </h2>
                  <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
                    Discover our current selection of premium cannabis strains, each with unique characteristics and carefully documented genetics.
                  </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredStrains.map((strain, index) => (
                    <motion.div
                      key={strain.name}
                      className="bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      whileHover={{ scale: 1.02, y: -4 }}
                      transition={{ 
                        duration: 0.3,
                        delay: index * 0.1 
                      }}
                      viewport={{ once: true }}
                    >
                      <div className="h-48 bg-gradient-to-br from-primary-200 to-primary-300 relative overflow-hidden">
                        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div className="absolute top-4 left-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            strain.type === 'Sativa' ? 'bg-gold-500 text-cream-50' :
                            strain.type === 'Indica' ? 'bg-primary-700 text-cream-50' :
                            'bg-sage-500 text-cream-50'
                          }`}>
                            {strain.type}
                          </span>
                        </div>
                      </div>

                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-primary-800 mb-2">
                          {strain.name}
                        </h3>
                        <p className="text-sm text-charcoal-500 mb-3">
                          {strain.genetics}
                        </p>
                        <p className="text-charcoal-600 text-sm mb-4 leading-relaxed">
                          {strain.description}
                        </p>

                        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="font-medium text-primary-800">THC:</span>
                            <span className="ml-1 text-charcoal-600">{strain.thc}</span>
                          </div>
                          <div>
                            <span className="font-medium text-primary-800">CBD:</span>
                            <span className="ml-1 text-charcoal-600">{strain.cbd}</span>
                          </div>
                        </div>

                        <div className="mb-4">
                          <div className="text-sm font-medium text-primary-800 mb-2">Effects:</div>
                          <div className="flex flex-wrap gap-1">
                            {strain.effects.map((effect) => (
                              <span
                                key={effect}
                                className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                              >
                                {effect}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <div className="text-sm font-medium text-primary-800 mb-2">Flavors:</div>
                          <div className="flex flex-wrap gap-1">
                            {strain.flavors.map((flavor) => (
                              <span
                                key={flavor}
                                className="px-2 py-1 bg-gold-100 text-gold-700 text-xs rounded-full"
                              >
                                {flavor}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-primary-800">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-serif font-bold text-cream-50 mb-4">
                    Find Your Perfect Strain
                  </h2>
                  <p className="text-xl text-cream-200 mb-8">
                    Visit our dispensary to explore these strains in person and get expert recommendations from our knowledgeable budtenders.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="/locations"
                      className="inline-flex items-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200"
                    >
                      Visit Our Dispensary
                    </a>
                    <a
                      href="/products"
                      className="inline-flex items-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200"
                    >
                      Browse All Products
                    </a>
                  </div>
                </motion.div>
              </div>
            </section>
          </main>

          {/* Footer */}
          <footer className="bg-charcoal-800 text-cream-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <p className="text-cream-400 text-sm">
                © 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase
              </p>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}
