'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

interface FAQAccordionProps {
  faqs: FAQ[];
  allowMultiple?: boolean;
  defaultOpen?: string[];
  variant?: 'default' | 'bordered' | 'minimal';
  className?: string;
}

export default function FAQAccordion({
  faqs,
  allowMultiple = false,
  defaultOpen = [],
  variant = 'default',
  className = ''
}: FAQAccordionProps) {
  const [openItems, setOpenItems] = useState<string[]>(defaultOpen);

  const toggleItem = (id: string) => {
    if (allowMultiple) {
      setOpenItems(prev =>
        prev.includes(id)
          ? prev.filter(item => item !== id)
          : [...prev, id]
      );
    } else {
      setOpenItems(prev =>
        prev.includes(id) ? [] : [id]
      );
    }
  };

  const variantStyles = {
    default: {
      container: 'bg-cream-50 rounded-lg shadow-soft',
      item: 'border-b border-cream-200 last:border-b-0',
      button: 'w-full px-6 py-4 text-left hover:bg-cream-100 transition-colors duration-200',
      content: 'px-6 pb-4'
    },
    bordered: {
      container: 'space-y-4',
      item: 'border border-cream-300 rounded-lg overflow-hidden',
      button: 'w-full px-6 py-4 text-left hover:bg-cream-50 transition-colors duration-200',
      content: 'px-6 pb-4 bg-cream-25'
    },
    minimal: {
      container: 'space-y-2',
      item: 'border-b border-cream-200 pb-2',
      button: 'w-full py-3 text-left hover:text-primary-600 transition-colors duration-200',
      content: 'pb-3'
    }
  };

  const styles = variantStyles[variant];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut'
      }
    }
  };

  const contentVariants = {
    hidden: {
      height: 0,
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    },
    visible: {
      height: 'auto',
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  const iconVariants = {
    closed: { rotate: 0 },
    open: { rotate: 180 }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`${styles.container} ${className}`}
    >
      {faqs.map((faq) => {
        const isOpen = openItems.includes(faq.id);

        return (
          <motion.div
            key={faq.id}
            variants={itemVariants}
            className={styles.item}
          >
            <motion.button
              onClick={() => toggleItem(faq.id)}
              className={`${styles.button} flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset`}
              whileHover={{ backgroundColor: 'rgba(0,0,0,0.02)' }}
            >
              <span className="font-semibold text-foreground pr-4">
                {faq.question}
              </span>
              
              <motion.div
                variants={iconVariants}
                animate={isOpen ? 'open' : 'closed'}
                transition={{ duration: 0.2 }}
                className="flex-shrink-0 text-primary-600"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </motion.div>
            </motion.button>

            <AnimatePresence>
              {isOpen && (
                <motion.div
                  variants={contentVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className={styles.content}
                >
                  <div className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}
    </motion.div>
  );
}

// Example usage with cannabis-related FAQs
export function ExampleFAQAccordion() {
  const cannabisFAQs: FAQ[] = [
    {
      id: '1',
      question: 'What are the legal requirements for purchasing cannabis in Colorado?',
      answer: 'You must be 21 years or older with a valid government-issued ID. Medical marijuana patients with a valid medical marijuana card can purchase at 18. All purchases are subject to state and local taxes.',
      category: 'Legal'
    },
    {
      id: '2',
      question: 'What\'s the difference between indica, sativa, and hybrid strains?',
      answer: 'Indica strains typically provide relaxing, sedating effects ideal for evening use. Sativa strains tend to be more energizing and uplifting, perfect for daytime activities. Hybrid strains combine characteristics of both, offering balanced effects.',
      category: 'Education'
    },
    {
      id: '3',
      question: 'How should I store my cannabis products?',
      answer: 'Store cannabis in a cool, dark, dry place away from children and pets. Use airtight containers to maintain freshness and potency. Avoid exposure to heat, light, and moisture which can degrade quality.',
      category: 'Storage'
    },
    {
      id: '4',
      question: 'What payment methods do you accept?',
      answer: 'We accept cash, debit cards, and CanPay. Due to federal banking regulations, we cannot accept credit cards. ATMs are available on-site for your convenience.',
      category: 'Payment'
    },
    {
      id: '5',
      question: 'Do you offer delivery services?',
      answer: 'Yes, we offer delivery within our service area. Delivery orders must meet minimum purchase requirements and are subject to delivery fees. Same-day delivery is available for orders placed before 2 PM.',
      category: 'Delivery'
    },
    {
      id: '6',
      question: 'How do I know which product is right for me?',
      answer: 'Our knowledgeable budtenders are here to help! We\'ll discuss your experience level, desired effects, and preferences to recommend the perfect products. Start low and go slow, especially with edibles.',
      category: 'Guidance'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-serif font-bold text-foreground mb-4">
          Frequently Asked Questions
        </h2>
        <p className="text-lg text-gray-600">
          Everything you need to know about cannabis and our services
        </p>
      </div>
      
      <FAQAccordion 
        faqs={cannabisFAQs} 
        variant="bordered"
        allowMultiple={true}
      />
    </div>
  );
}
