'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnnouncementBannerProps } from '../../../types/ui';
import { brandColors, brandAnimations } from '../../../styles/brand';

/**
 * AnnouncementBanner Component
 * 
 * Build announcement banners for deals, new products, and compliance notices.
 * Features auto-dismiss, sticky positioning, and themed styling for different message types.
 * 
 * @example
 * ```tsx
 * <AnnouncementBanner
 *   message="🌿 New Premium Rosin Collection Available - 20% Off This Week!"
 *   type="deal"
 *   dismissible={true}
 *   autoDismiss={10000}
 *   actionText="Shop Now"
 *   onAction={() => router.push('/products/rosin')}
 *   sticky={true}
 * />
 * ```
 */
const AnnouncementBanner: React.FC<AnnouncementBannerProps> = ({
  message,
  type = 'info',
  dismissible = true,
  autoDismiss,
  actionText,
  onAction,
  onClose,
  sticky = false,
  className = '',
  style,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);

  // Auto-dismiss functionality
  useEffect(() => {
    if (autoDismiss && autoDismiss > 0) {
      setTimeLeft(autoDismiss);
      
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev && prev <= 1000) {
            handleClose();
            return null;
          }
          return prev ? prev - 1000 : null;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [autoDismiss]);

  // Handle close
  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  // Get banner styling based on type
  const getBannerStyling = () => {
    switch (type) {
      case 'info':
        return {
          backgroundColor: brandColors.primary[600],
          textColor: 'text-cream-50',
          iconColor: brandColors.cream[100],
          actionBg: brandColors.cream[50],
          actionText: brandColors.primary[600],
          actionHover: brandColors.cream[100],
        };
      case 'warning':
        return {
          backgroundColor: '#f59e0b',
          textColor: 'text-cream-50',
          iconColor: '#fef3c7',
          actionBg: brandColors.cream[50],
          actionText: '#f59e0b',
          actionHover: '#fef3c7',
        };
      case 'success':
        return {
          backgroundColor: brandColors.sage[600],
          textColor: 'text-cream-50',
          iconColor: brandColors.sage[100],
          actionBg: brandColors.cream[50],
          actionText: brandColors.sage[600],
          actionHover: brandColors.sage[100],
        };
      case 'error':
        return {
          backgroundColor: '#dc2626',
          textColor: 'text-cream-50',
          iconColor: '#fecaca',
          actionBg: brandColors.cream[50],
          actionText: '#dc2626',
          actionHover: '#fecaca',
        };
      case 'deal':
        return {
          backgroundColor: brandColors.gold[600],
          textColor: 'text-charcoal-800',
          iconColor: brandColors.gold[200],
          actionBg: brandColors.charcoal[800],
          actionText: brandColors.cream[50],
          actionHover: brandColors.charcoal[700],
        };
      default:
        return {
          backgroundColor: brandColors.primary[600],
          textColor: 'text-cream-50',
          iconColor: brandColors.cream[100],
          actionBg: brandColors.cream[50],
          actionText: brandColors.primary[600],
          actionHover: brandColors.cream[100],
        };
    }
  };

  const styling = getBannerStyling();

  // Get icon based on type
  const getIcon = () => {
    switch (type) {
      case 'info':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'success':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'deal':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Format time left for display
  const formatTimeLeft = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return seconds;
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ 
            duration: 0.4, 
            ease: brandAnimations.easing.smooth 
          }}
          className={`announcement-banner ${
            sticky ? 'fixed top-0 left-0 right-0 z-50' : 'relative'
          } ${className}`}
          style={{
            backgroundColor: styling.backgroundColor,
            ...style,
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-3">
              {/* Content */}
              <div className="flex items-center space-x-3 flex-1">
                {/* Icon */}
                <div style={{ color: styling.iconColor }}>
                  {getIcon()}
                </div>

                {/* Message */}
                <p className={`font-medium ${styling.textColor} flex-1`}>
                  {message}
                </p>

                {/* Auto-dismiss countdown */}
                {timeLeft && timeLeft > 0 && (
                  <div className={`text-sm ${styling.textColor} opacity-75`}>
                    Auto-close in {formatTimeLeft(timeLeft)}s
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-3">
                {/* Action Button */}
                {actionText && onAction && (
                  <motion.button
                    onClick={onAction}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200"
                    style={{
                      backgroundColor: styling.actionBg,
                      color: styling.actionText,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = styling.actionHover;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = styling.actionBg;
                    }}
                  >
                    {actionText}
                  </motion.button>
                )}

                {/* Close Button */}
                {dismissible && (
                  <motion.button
                    onClick={handleClose}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`p-1 rounded-md transition-colors duration-200 ${styling.textColor} hover:bg-black hover:bg-opacity-10`}
                    aria-label="Close announcement"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </motion.button>
                )}
              </div>
            </div>

            {/* Progress bar for auto-dismiss */}
            {autoDismiss && timeLeft && (
              <motion.div
                className="h-1 bg-black bg-opacity-20"
                initial={{ scaleX: 1 }}
                animate={{ scaleX: 0 }}
                transition={{ 
                  duration: autoDismiss / 1000, 
                  ease: 'linear' 
                }}
                style={{ transformOrigin: 'left' }}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AnnouncementBanner;
