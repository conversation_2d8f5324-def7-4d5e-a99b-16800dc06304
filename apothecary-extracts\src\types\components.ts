import { ReactNode } from 'react';

// Base interfaces
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

export interface AnimatedComponentProps extends BaseComponentProps {
  animate?: boolean;
  animationDelay?: number;
}

// Common types
export interface CTAButton {
  text: string;
  href?: string;
  action?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
}

export interface MediaItem {
  id: string;
  title: string;
  description?: string;
  image?: string;
  category?: string;
  featured?: boolean;
}

// Component-specific interfaces
export interface GlitchEffectProps extends BaseComponentProps {
  text: string;
  glitchIntensity?: 'low' | 'medium' | 'high';
  triggerOnHover?: boolean;
  autoGlitch?: boolean;
  autoGlitchInterval?: number;
}

export interface AnnouncementBarProps extends BaseComponentProps {
  message: string;
  ctaText?: string;
  ctaLink?: string;
  type?: 'info' | 'success' | 'warning' | 'promotion';
  dismissible?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export interface Feature {
  icon: ReactNode;
  title: string;
  description: string;
  color?: string;
}

export interface FeatureIconsProps extends AnimatedComponentProps {
  features: Feature[];
  layout?: 'grid' | 'horizontal' | 'vertical';
  hoverEffect?: boolean;
}

export interface Tab {
  id: string;
  label: string;
  content: ReactNode;
  icon?: ReactNode;
  badge?: string | number;
}

export interface TabsProps extends BaseComponentProps {
  tabs: Tab[];
  defaultTab?: string;
  variant?: 'default' | 'soft' | 'pills' | 'underline';
  orientation?: 'horizontal' | 'vertical';
  onTabChange?: (tabId: string) => void;
}

export interface GridCard {
  id: string;
  title: string;
  description?: string;
  image?: string;
  icon?: ReactNode;
  badge?: string;
  price?: string;
  originalPrice?: string;
  features?: string[];
  cta?: CTAButton;
  href?: string;
}

export interface GridCardsProps extends AnimatedComponentProps {
  cards: GridCard[];
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  cardVariant?: 'default' | 'elevated' | 'bordered' | 'minimal';
}

export interface SplitMediaTextProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  content: ReactNode;
  media: {
    type: 'image' | 'video';
    src: string;
    alt?: string;
    poster?: string;
  };
  layout?: 'default' | 'reversed';
  mediaPosition?: 'left' | 'right';
  backgroundColor?: string;
  textAlign?: 'left' | 'center' | 'right';
  cta?: CTAButton;
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

export interface FAQAccordionProps extends BaseComponentProps {
  faqs: FAQ[];
  allowMultiple?: boolean;
  defaultOpen?: string[];
  variant?: 'default' | 'bordered' | 'minimal';
}

export interface SliderCard {
  id: string;
  title: string;
  description?: string;
  image?: string;
  icon?: string;
  emoji?: string;
  badge?: string;
  price?: string;
  cta?: CTAButton;
}

export interface CardSliderProps extends BaseComponentProps {
  cards: SliderCard[];
  autoScroll?: boolean;
  autoScrollInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  slidesToShow?: number;
  gap?: number;
}

export interface Strain {
  id: string;
  name: string;
  type: 'indica' | 'sativa' | 'hybrid';
  thc: number;
  cbd: number;
  price: number;
  image?: string;
  description: string;
  effects: string[];
  flavors: string[];
  genetics?: string;
  flowering?: string;
  yield?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  inStock: boolean;
  featured?: boolean;
}

export interface FilterableStrainCardsProps extends BaseComponentProps {
  strains: Strain[];
  showFilters?: boolean;
  defaultFilter?: string;
  sortBy?: 'name' | 'thc' | 'price' | 'type';
}

export interface MasonryItem {
  id: string;
  title: string;
  description?: string;
  image: string;
  category?: string;
  height?: 'small' | 'medium' | 'large';
  featured?: boolean;
  cta?: CTAButton;
}

export interface MasonryGridProps extends BaseComponentProps {
  items: MasonryItem[];
  columns?: number;
  gap?: number;
}

export interface CarouselItem {
  id: string;
  title: string;
  description?: string;
  image: string;
  thumbnail?: string;
  category?: string;
  badge?: string;
  cta?: CTAButton;
}

export interface CarouselWithThumbnailsProps extends BaseComponentProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showThumbnails?: boolean;
  showDots?: boolean;
  showArrows?: boolean;
}

export interface TimelineEvent {
  id: string;
  date: string;
  title: string;
  description: string;
  icon?: ReactNode;
  image?: string;
  category?: string;
  featured?: boolean;
}

export interface VerticalTimelineProps extends BaseComponentProps {
  events: TimelineEvent[];
  animate?: boolean;
  alternating?: boolean;
  showImages?: boolean;
}

export interface HeroVideoLoopProps extends BaseComponentProps {
  headline: string;
  subheadline?: string;
  videoSrc: string;
  posterImage?: string;
  ctas?: CTAButton[];
  overlayOpacity?: number;
  textPosition?: 'center' | 'left' | 'right';
  showScrollIndicator?: boolean;
  autoPlay?: boolean;
}

export interface ParallaxBannerProps extends BaseComponentProps {
  backgroundImage: string;
  height?: string;
  children?: ReactNode;
  parallaxSpeed?: number;
  overlayOpacity?: number;
  overlayColor?: string;
}

export interface ScrollFadeInProps extends BaseComponentProps {
  children: ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  duration?: number;
  delay?: number;
}
