'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { ImageZoomOnHoverProps } from '../../types/visual';
import { brandShadows, brandAnimations } from '../../styles/brand';

/**
 * ImageZoomOnHover Component
 * 
 * Makes product imagery feel tactile and reactive to mouse interaction.
 * Features scale and brightness effects with Apothecary Green shadow.
 * 
 * @example
 * ```tsx
 * <ImageZoomOnHover 
 *   src="/products/rosin.jpg" 
 *   alt="Premium Rosin Extract"
 *   addBrandShadow={true}
 *   shadowTheme="apothecary"
 * />
 * ```
 */
const ImageZoomOnHover: React.FC<ImageZoomOnHoverProps> = ({
  src,
  alt,
  className = '',
  style,
  scale = 1.07,
  brightness = 1.1,
  duration = 200,
  addBrandShadow = true,
  shadowTheme = 'apothecary',
}) => {
  // Get the appropriate shadow based on theme
  const getShadow = (theme: 'apothecary' | 'gold' | 'sage') => {
    switch (theme) {
      case 'apothecary':
        return brandShadows.apothecaryGlow;
      case 'gold':
        return brandShadows.goldGlow;
      case 'sage':
        return brandShadows.medium;
      default:
        return brandShadows.apothecaryGlow;
    }
  };

  const hoverShadow = addBrandShadow ? getShadow(shadowTheme) : brandShadows.medium;

  return (
    <motion.div
      className={`image-zoom-container relative overflow-hidden rounded-lg ${className}`}
      style={style}
      whileHover={{
        scale,
        filter: `brightness(${brightness})`,
        boxShadow: hoverShadow,
      }}
      transition={{
        duration: duration / 1000,
        ease: brandAnimations.easing.smooth,
      }}
    >
      <Image
        src={src}
        alt={alt}
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
    </motion.div>
  );
};

export default ImageZoomOnHover;
