'use client';

import { motion } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

interface MasonryItem {
  id: string;
  title: string;
  description?: string;
  image: string;
  category?: string;
  height?: 'small' | 'medium' | 'large';
  featured?: boolean;
  cta?: {
    text: string;
    action: () => void;
  };
}

interface MasonryGridProps {
  items: MasonryItem[];
  columns?: number;
  gap?: number;
  className?: string;
}

export default function MasonryGrid({
  items,
  columns = 3,
  gap = 16,
  className = ''
}: MasonryGridProps) {
  const [columnHeights, setColumnHeights] = useState<number[]>([]);
  const [itemPositions, setItemPositions] = useState<{ [key: string]: { x: number; y: number; width: number } }>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const heightClasses = {
    small: 'h-48',
    medium: 'h-64',
    large: 'h-80'
  };

  useEffect(() => {
    const calculateLayout = () => {
      if (!containerRef.current) return;

      const containerWidth = containerRef.current.offsetWidth;
      const columnWidth = (containerWidth - gap * (columns - 1)) / columns;
      const heights = new Array(columns).fill(0);
      const positions: { [key: string]: { x: number; y: number; width: number } } = {};

      items.forEach((item) => {
        const shortestColumnIndex = heights.indexOf(Math.min(...heights));
        const x = shortestColumnIndex * (columnWidth + gap);
        const y = heights[shortestColumnIndex];

        // Get actual height of the item
        const itemElement = itemRefs.current[item.id];
        const itemHeight = itemElement ? itemElement.offsetHeight : 300; // fallback height

        positions[item.id] = { x, y, width: columnWidth };
        heights[shortestColumnIndex] += itemHeight + gap;
      });

      setColumnHeights(heights);
      setItemPositions(positions);
    };

    // Initial calculation
    calculateLayout();

    // Recalculate on window resize
    const handleResize = () => {
      setTimeout(calculateLayout, 100); // Debounce
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [items, columns, gap]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.8
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const hoverVariants = {
    hover: {
      scale: 1.02,
      y: -5,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    }
  };

  const maxHeight = Math.max(...columnHeights);

  return (
    <motion.div
      ref={containerRef}
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`relative ${className}`}
      style={{ height: maxHeight }}
    >
      {items.map((item, index) => {
        const position = itemPositions[item.id];
        if (!position) return null;

        return (
          <motion.div
            key={item.id}
            ref={(el) => { itemRefs.current[item.id] = el; }}
            variants={itemVariants}
            whileHover="hover"
            className="absolute group cursor-pointer"
            style={{
              left: position.x,
              top: position.y,
              width: position.width
            }}
          >
            <motion.div
              variants={hoverVariants}
              className="bg-cream-50 rounded-xl shadow-soft hover:shadow-large transition-shadow duration-300 overflow-hidden"
            >
              {/* Image */}
              <div className={`relative ${item.height ? heightClasses[item.height] : 'h-64'} overflow-hidden`}>
                <Image
                  src={item.image}
                  alt={item.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />

                {/* Category Badge */}
                {item.category && (
                  <div className="absolute top-3 left-3">
                    <span className="bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full">
                      {item.category}
                    </span>
                  </div>
                )}

                {/* Featured Badge */}
                {item.featured && (
                  <div className="absolute top-3 right-3">
                    <span className="bg-gold-500 text-primary-800 text-xs font-semibold px-2 py-1 rounded-full">
                      ⭐ Featured
                    </span>
                  </div>
                )}

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Hover Content */}
                <div className="absolute inset-0 flex items-end p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="text-white">
                    <h3 className="text-lg font-semibold mb-1">{item.title}</h3>
                    {item.description && (
                      <p className="text-sm text-gray-200 line-clamp-2">
                        {item.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                  {item.title}
                </h3>

                {item.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {item.description}
                  </p>
                )}

                {/* CTA Button */}
                {item.cta && (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={item.cta.action}
                    className="w-full bg-primary-600 text-cream-50 py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200"
                  >
                    {item.cta.text}
                  </motion.button>
                )}
              </div>
            </motion.div>
          </motion.div>
        );
      })}
    </motion.div>
  );
}

// Example usage
export function ExampleMasonryGrid() {
  const sampleItems: MasonryItem[] = [
    {
      id: '1',
      title: 'Premium OG Kush',
      description: 'Classic indica strain with earthy, pine flavors and deeply relaxing effects perfect for evening use.',
      image: '/assets/products/og-kush.jpg',
      category: 'Indica',
      height: 'medium',
      featured: true,
      cta: { text: 'Shop Now', action: () => console.log('Shop OG Kush') }
    },
    {
      id: '2',
      title: 'Blue Dream',
      description: 'Balanced hybrid offering creative euphoria and gentle relaxation.',
      image: '/assets/products/blue-dream.jpg',
      category: 'Hybrid',
      height: 'small',
      cta: { text: 'Shop Now', action: () => console.log('Shop Blue Dream') }
    },
    {
      id: '3',
      title: 'Green Crack',
      description: 'Energizing sativa perfect for daytime use and creative activities. Known for its sweet, fruity flavor profile.',
      image: '/assets/products/green-crack.jpg',
      category: 'Sativa',
      height: 'large',
      cta: { text: 'Shop Now', action: () => console.log('Shop Green Crack') }
    },
    {
      id: '4',
      title: 'Purple Haze',
      description: 'Legendary sativa with psychedelic effects and sweet berry flavors.',
      image: '/assets/products/purple-haze.jpg',
      category: 'Sativa',
      height: 'medium',
      cta: { text: 'Shop Now', action: () => console.log('Shop Purple Haze') }
    },
    {
      id: '5',
      title: 'Granddaddy Purple',
      description: 'Potent indica with grape and berry flavors, perfect for relaxation.',
      image: '/assets/products/granddaddy-purple.jpg',
      category: 'Indica',
      height: 'small',
      featured: true,
      cta: { text: 'Shop Now', action: () => console.log('Shop GDP') }
    },
    {
      id: '6',
      title: 'Sour Diesel',
      description: 'Fast-acting sativa with diesel-like aroma and energizing effects that promote creativity and focus.',
      image: '/assets/products/sour-diesel.jpg',
      category: 'Sativa',
      height: 'large',
      cta: { text: 'Shop Now', action: () => console.log('Shop Sour Diesel') }
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-serif font-bold text-foreground mb-4">
          Featured Products
        </h2>
        <p className="text-lg text-gray-600">
          Discover our curated selection of premium cannabis strains
        </p>
      </div>
      
      <MasonryGrid items={sampleItems} columns={3} gap={20} />
    </div>
  );
}
