'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ScrollTriggeredFadeInProps } from '../../types/visual';
import { brandColors, brandShadows, brandAnimations } from '../../styles/brand';

/**
 * ScrollTriggeredFadeIn Component
 * 
 * Animates cards, text blocks, or images to "rise and fade in" when they enter the viewport.
 * Features Apothecary Farms brand aesthetics with mint-green glow effects.
 * 
 * @example
 * ```tsx
 * <ScrollTriggeredFadeIn>
 *   <div className="card">Content that fades in on scroll</div>
 * </ScrollTriggeredFadeIn>
 * ```
 */
const ScrollTriggeredFadeIn: React.FC<ScrollTriggeredFadeInProps> = ({
  children,
  className = '',
  style,
  delay = 0,
  duration = 500,
  translateY = 40,
  threshold = 0.1,
  once = true,
  variants,
}) => {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce: once,
  });

  // Default animation variants
  const defaultVariants = {
    hidden: {
      opacity: 0,
      y: translateY,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: duration / 1000,
        delay: delay / 1000,
        ease: brandAnimations.easing.smooth,
      },
    },
  };

  // Use custom variants if provided, otherwise use defaults
  const animationVariants = variants || defaultVariants;

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? 'visible' : 'hidden'}
      variants={animationVariants}
      className={`scroll-fade-in ${className}`}
      style={{
        ...style,
        // Add mint-green glow effect when visible
        boxShadow: inView ? brandShadows.apothecaryGlow : 'none',
        transition: 'box-shadow 0.3s ease-in-out',
      }}
    >
      {children}
    </motion.div>
  );
};

export default ScrollTriggeredFadeIn;
