'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import Image from 'next/image';

interface GridCard {
  id: string;
  title: string;
  description?: string;
  image?: string;
  icon?: ReactNode;
  badge?: string;
  price?: string;
  originalPrice?: string;
  features?: string[];
  cta?: {
    text: string;
    action: () => void;
  };
  href?: string;
}

interface GridCardsProps {
  cards: GridCard[];
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  cardVariant?: 'default' | 'elevated' | 'bordered' | 'minimal';
  animationDelay?: number;
  className?: string;
}

export default function GridCards({
  cards,
  columns = 3,
  gap = 'md',
  cardVariant = 'default',
  animationDelay = 0.1,
  className = ''
}: GridCardsProps) {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  const cardVariants = {
    default: 'bg-cream-50 rounded-lg shadow-soft hover:shadow-medium',
    elevated: 'bg-cream-50 rounded-xl shadow-medium hover:shadow-large',
    bordered: 'bg-cream-50 rounded-lg border border-cream-300 hover:border-primary-300',
    minimal: 'bg-transparent hover:bg-cream-50 rounded-lg'
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: animationDelay,
        delayChildren: 0.2
      }
    }
  };

  const cardItemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  const hoverVariants = {
    hover: {
      y: -5,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-50px' }}
      className={`grid ${columnClasses[columns]} ${gapClasses[gap]} ${className}`}
    >
      {cards.map((card, index) => (
        <motion.div
          key={card.id}
          variants={cardItemVariants}
          whileHover="hover"
          className="group"
        >
          <motion.div
            variants={hoverVariants}
            className={`${cardVariants[cardVariant]} overflow-hidden transition-all duration-300 cursor-pointer`}
            onClick={card.cta?.action}
          >
            {/* Badge */}
            {card.badge && (
              <div className="absolute top-4 right-4 z-10">
                <span className="bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full">
                  {card.badge}
                </span>
              </div>
            )}

            {/* Image or Icon */}
            {card.image ? (
              <div className="relative h-48 w-full overflow-hidden">
                <Image
                  src={card.image}
                  alt={card.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            ) : card.icon ? (
              <div className="flex items-center justify-center h-24 text-primary-600">
                <div className="text-4xl">
                  {card.icon}
                </div>
              </div>
            ) : null}

            {/* Content */}
            <div className="p-6">
              {/* Title */}
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200">
                {card.title}
              </h3>

              {/* Description */}
              {card.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {card.description}
                </p>
              )}

              {/* Features */}
              {card.features && card.features.length > 0 && (
                <div className="mb-4">
                  <ul className="space-y-1">
                    {card.features.slice(0, 3).map((feature, idx) => (
                      <li key={idx} className="flex items-center text-xs text-gray-600">
                        <svg className="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Price */}
              {card.price && (
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-primary-600">
                      {card.price}
                    </span>
                    {card.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {card.originalPrice}
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* CTA Button */}
              {card.cta && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={card.cta.action}
                  className="w-full bg-primary-600 text-cream-50 py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  {card.cta.text}
                </motion.button>
              )}
            </div>

            {/* Hover overlay */}
            <motion.div
              className="absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"
              initial={{ opacity: 0 }}
              whileHover={{ opacity: 0.05 }}
            />
          </motion.div>
        </motion.div>
      ))}
    </motion.div>
  );
}

// Example usage
export function ExampleGridCards() {
  const sampleCards: GridCard[] = [
    {
      id: '1',
      title: 'Premium OG Kush',
      description: 'Classic indica strain with earthy, pine flavors and relaxing effects.',
      image: '/assets/products/og-kush.jpg',
      badge: 'Popular',
      price: '$45',
      originalPrice: '$55',
      features: ['28% THC', 'Indoor Grown', 'Lab Tested'],
      cta: {
        text: 'Add to Cart',
        action: () => console.log('Added to cart')
      }
    },
    {
      id: '2',
      title: 'Blue Dream',
      description: 'Balanced hybrid offering creative euphoria and gentle relaxation.',
      image: '/assets/products/blue-dream.jpg',
      price: '$40',
      features: ['24% THC', 'Hybrid', 'Organic'],
      cta: {
        text: 'Add to Cart',
        action: () => console.log('Added to cart')
      }
    },
    {
      id: '3',
      title: 'Green Crack',
      description: 'Energizing sativa perfect for daytime use and creative activities.',
      image: '/assets/products/green-crack.jpg',
      badge: 'New',
      price: '$42',
      features: ['26% THC', 'Sativa', 'Energizing'],
      cta: {
        text: 'Add to Cart',
        action: () => console.log('Added to cart')
      }
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-3xl font-serif font-bold text-center mb-8">Featured Products</h2>
      <GridCards cards={sampleCards} columns={3} cardVariant="elevated" />
    </div>
  );
}
