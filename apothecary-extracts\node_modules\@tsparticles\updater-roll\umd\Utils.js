(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "@tsparticles/engine", "./RollMode.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.initParticle = initParticle;
    exports.updateRoll = updateRoll;
    const engine_1 = require("@tsparticles/engine");
    const RollMode_js_1 = require("./RollMode.js");
    const double = 2, doublePI = Math.PI * double, maxAngle = 360;
    function initParticle(engine, particle) {
        const rollOpt = particle.options.roll;
        if (!rollOpt?.enable) {
            particle.roll = {
                enable: false,
                horizontal: false,
                vertical: false,
                angle: 0,
                speed: 0,
            };
            return;
        }
        particle.roll = {
            enable: rollOpt.enable,
            horizontal: rollOpt.mode === RollMode_js_1.RollMode.horizontal || rollOpt.mode === RollMode_js_1.RollMode.both,
            vertical: rollOpt.mode === RollMode_js_1.RollMode.vertical || rollOpt.mode === RollMode_js_1.RollMode.both,
            angle: (0, engine_1.getRandom)() * doublePI,
            speed: (0, engine_1.getRangeValue)(rollOpt.speed) / maxAngle,
        };
        if (rollOpt.backColor) {
            particle.backColor = (0, engine_1.rangeColorToHsl)(engine, rollOpt.backColor);
        }
        else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {
            const alterType = (0, engine_1.getRandom)() >= engine_1.half ? engine_1.AlterType.darken : engine_1.AlterType.enlighten;
            particle.roll.alter = {
                type: alterType,
                value: (0, engine_1.getRangeValue)(alterType === engine_1.AlterType.darken ? rollOpt.darken.value : rollOpt.enlighten.value),
            };
        }
        else if (rollOpt.darken.enable) {
            particle.roll.alter = {
                type: engine_1.AlterType.darken,
                value: (0, engine_1.getRangeValue)(rollOpt.darken.value),
            };
        }
        else if (rollOpt.enlighten.enable) {
            particle.roll.alter = {
                type: engine_1.AlterType.enlighten,
                value: (0, engine_1.getRangeValue)(rollOpt.enlighten.value),
            };
        }
    }
    function updateRoll(particle, delta) {
        const roll = particle.options.roll, data = particle.roll;
        if (!data || !roll?.enable) {
            return;
        }
        const speed = data.speed * delta.factor, max = doublePI;
        data.angle += speed;
        if (data.angle > max) {
            data.angle -= max;
        }
    }
});
