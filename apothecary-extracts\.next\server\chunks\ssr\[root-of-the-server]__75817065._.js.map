{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigationItems = [\n    { name: 'Home', href: '/' },\n    { name: 'Products', href: '/products' },\n    { name: 'Cultivars', href: '/cultivars' },\n    { name: 'Deals', href: '/deals' },\n    { name: 'Locations', href: '/locations' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <nav className=\"bg-cream-50 shadow-soft sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-2xl font-serif font-bold text-primary-800\">\n                Apothecary Extracts\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;sCAOpE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE5B,KAAK,IAAI;2BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAa9B", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/AgeVerification.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AgeVerificationProps {\n  onVerified: () => void;\n}\n\nexport default function AgeVerification({ onVerified }: AgeVerificationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Check if user has already verified their age\n    const hasVerified = localStorage.getItem('ageVerified');\n    if (!hasVerified) {\n      setIsVisible(true);\n    } else {\n      onVerified();\n    }\n  }, [onVerified]);\n\n  const handleVerification = (isOfAge: boolean) => {\n    if (isOfAge) {\n      localStorage.setItem('ageVerified', 'true');\n      setIsVisible(false);\n      onVerified();\n    } else {\n      // Redirect to educational resources or show message\n      window.location.href = 'https://www.samhsa.gov/marijuana';\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-serif font-bold text-primary-800 mb-2\">\n            Age Verification Required\n          </h2>\n          <p className=\"text-charcoal-700 text-sm leading-relaxed\">\n            You must be 21 years of age or older to view this website and purchase cannabis products.\n            Please verify your age to continue.\n          </p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={() => handleVerification(true)}\n            className=\"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2\"\n          >\n            Yes, I&apos;m 21 or older\n          </button>\n          \n          <button\n            onClick={() => handleVerification(false)}\n            className=\"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2\"\n          >\n            No, I&apos;m under 21\n          </button>\n        </div>\n        \n        <div className=\"mt-6 pt-4 border-t border-charcoal-200\">\n          <p className=\"text-xs text-charcoal-500 leading-relaxed\">\n            By entering this website, you certify that you are of legal age to purchase cannabis products \n            in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended \n            to diagnose, treat, cure, or prevent any disease.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQe,SAAS,gBAAgB,EAAE,UAAU,EAAwB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,CAAC,aAAa;YAChB,aAAa;QACf,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,eAAe;YACpC,aAAa;YACb;QACF,OAAO;YACL,oDAAoD;YACpD,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAM3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/TextWithGlitchEffect.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\n\ninterface TextWithGlitchEffectProps {\n  text: string;\n  className?: string;\n  glitchIntensity?: 'low' | 'medium' | 'high';\n  triggerOnHover?: boolean;\n  autoGlitch?: boolean;\n  autoGlitchInterval?: number;\n}\n\nexport default function TextWithGlitchEffect({\n  text,\n  className = '',\n  glitchIntensity = 'medium',\n  triggerOnHover = false,\n  autoGlitch = false,\n  autoGlitchInterval = 3000\n}: TextWithGlitchEffectProps) {\n  const [isGlitching, setIsGlitching] = useState(false);\n  const [displayText, setDisplayText] = useState(text);\n\n  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';\n  \n  const intensitySettings = {\n    low: { duration: 0.1, iterations: 2 },\n    medium: { duration: 0.15, iterations: 3 },\n    high: { duration: 0.2, iterations: 5 }\n  };\n\n  const settings = intensitySettings[glitchIntensity];\n\n  const triggerGlitch = () => {\n    if (isGlitching) return;\n    \n    setIsGlitching(true);\n    let iteration = 0;\n\n    const glitchInterval = setInterval(() => {\n      setDisplayText(\n        text\n          .split('')\n          .map((char, index) => {\n            if (Math.random() < 0.3) {\n              return glitchChars[Math.floor(Math.random() * glitchChars.length)];\n            }\n            return char;\n          })\n          .join('')\n      );\n\n      iteration++;\n      if (iteration >= settings.iterations) {\n        clearInterval(glitchInterval);\n        setDisplayText(text);\n        setIsGlitching(false);\n      }\n    }, settings.duration * 100);\n  };\n\n  useEffect(() => {\n    if (autoGlitch) {\n      const interval = setInterval(triggerGlitch, autoGlitchInterval);\n      return () => clearInterval(interval);\n    }\n  }, [autoGlitch, autoGlitchInterval]);\n\n  const glitchVariants = {\n    normal: {\n      x: 0,\n      textShadow: 'none',\n      filter: 'none'\n    },\n    glitch: {\n      x: [0, -2, 2, -1, 1, 0],\n      textShadow: [\n        'none',\n        '2px 0 #ff0000, -2px 0 #00ff00',\n        '-2px 0 #ff0000, 2px 0 #00ff00',\n        '1px 0 #ff0000, -1px 0 #00ff00',\n        'none'\n      ],\n      filter: [\n        'none',\n        'hue-rotate(90deg)',\n        'hue-rotate(180deg)',\n        'hue-rotate(270deg)',\n        'none'\n      ],\n      transition: {\n        duration: settings.duration,\n        times: [0, 0.2, 0.4, 0.6, 0.8, 1],\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  return (\n    <motion.span\n      className={`inline-block relative ${className}`}\n      variants={glitchVariants}\n      animate={isGlitching ? 'glitch' : 'normal'}\n      onMouseEnter={triggerOnHover ? triggerGlitch : undefined}\n      style={{ cursor: triggerOnHover ? 'pointer' : 'default' }}\n    >\n      {displayText}\n      \n      {/* Glitch overlay effects */}\n      {isGlitching && (\n        <>\n          <motion.span\n            className=\"absolute top-0 left-0 text-red-500 opacity-70\"\n            style={{ clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)' }}\n            animate={{\n              x: [-2, 2, -1],\n              transition: { duration: 0.1, repeat: Infinity }\n            }}\n          >\n            {displayText}\n          </motion.span>\n          <motion.span\n            className=\"absolute top-0 left-0 text-green-500 opacity-70\"\n            style={{ clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)' }}\n            animate={{\n              x: [2, -2, 1],\n              transition: { duration: 0.1, repeat: Infinity }\n            }}\n          >\n            {displayText}\n          </motion.span>\n        </>\n      )}\n    </motion.span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,YAAY,EAAE,EACd,kBAAkB,QAAQ,EAC1B,iBAAiB,KAAK,EACtB,aAAa,KAAK,EAClB,qBAAqB,IAAI,EACC;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;IAEpB,MAAM,oBAAoB;QACxB,KAAK;YAAE,UAAU;YAAK,YAAY;QAAE;QACpC,QAAQ;YAAE,UAAU;YAAM,YAAY;QAAE;QACxC,MAAM;YAAE,UAAU;YAAK,YAAY;QAAE;IACvC;IAEA,MAAM,WAAW,iBAAiB,CAAC,gBAAgB;IAEnD,MAAM,gBAAgB;QACpB,IAAI,aAAa;QAEjB,eAAe;QACf,IAAI,YAAY;QAEhB,MAAM,iBAAiB,YAAY;YACjC,eACE,KACG,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,MAAM;gBACV,IAAI,KAAK,MAAM,KAAK,KAAK;oBACvB,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;gBACpE;gBACA,OAAO;YACT,GACC,IAAI,CAAC;YAGV;YACA,IAAI,aAAa,SAAS,UAAU,EAAE;gBACpC,cAAc;gBACd,eAAe;gBACf,eAAe;YACjB;QACF,GAAG,SAAS,QAAQ,GAAG;IACzB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,MAAM,WAAW,YAAY,eAAe;YAC5C,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAY;KAAmB;IAEnC,MAAM,iBAAiB;QACrB,QAAQ;YACN,GAAG;YACH,YAAY;YACZ,QAAQ;QACV;QACA,QAAQ;YACN,GAAG;gBAAC;gBAAG,CAAC;gBAAG;gBAAG,CAAC;gBAAG;gBAAG;aAAE;YACvB,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,UAAU,SAAS,QAAQ;gBAC3B,OAAO;oBAAC;oBAAG;oBAAK;oBAAK;oBAAK;oBAAK;iBAAE;gBACjC,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;QACV,WAAW,CAAC,sBAAsB,EAAE,WAAW;QAC/C,UAAU;QACV,SAAS,cAAc,WAAW;QAClC,cAAc,iBAAiB,gBAAgB;QAC/C,OAAO;YAAE,QAAQ,iBAAiB,YAAY;QAAU;;YAEvD;YAGA,6BACC;;kCACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,OAAO;4BAAE,UAAU;wBAAwC;wBAC3D,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAG;gCAAG,CAAC;6BAAE;4BACd,YAAY;gCAAE,UAAU;gCAAK,QAAQ;4BAAS;wBAChD;kCAEC;;;;;;kCAEH,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,OAAO;4BAAE,UAAU;wBAA8C;wBACjE,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;4BACb,YAAY;gCAAE,UAAU;gCAAK,QAAQ;4BAAS;wBAChD;kCAEC;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/AnnouncementBar.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState } from 'react';\nimport Link from 'next/link';\n\ninterface AnnouncementBarProps {\n  message: string;\n  ctaText?: string;\n  ctaLink?: string;\n  type?: 'info' | 'success' | 'warning' | 'promotion';\n  dismissible?: boolean;\n  autoHide?: boolean;\n  autoHideDelay?: number;\n  className?: string;\n}\n\nexport default function AnnouncementBar({\n  message,\n  ctaText,\n  ctaLink,\n  type = 'info',\n  dismissible = true,\n  autoHide = false,\n  autoHideDelay = 5000,\n  className = ''\n}: AnnouncementBarProps) {\n  const [isVisible, setIsVisible] = useState(true);\n\n  const typeStyles = {\n    info: {\n      bg: 'bg-primary-600',\n      text: 'text-cream-50',\n      accent: 'text-gold-300',\n      button: 'bg-gold-500 hover:bg-gold-400 text-primary-800'\n    },\n    success: {\n      bg: 'bg-sage-500',\n      text: 'text-cream-50',\n      accent: 'text-cream-200',\n      button: 'bg-cream-50 hover:bg-cream-100 text-sage-700'\n    },\n    warning: {\n      bg: 'bg-gold-500',\n      text: 'text-primary-800',\n      accent: 'text-primary-700',\n      button: 'bg-primary-600 hover:bg-primary-700 text-cream-50'\n    },\n    promotion: {\n      bg: 'bg-gradient-to-r from-primary-600 to-sage-500',\n      text: 'text-cream-50',\n      accent: 'text-gold-300',\n      button: 'bg-gold-500 hover:bg-gold-400 text-primary-800'\n    }\n  };\n\n  const currentStyle = typeStyles[type];\n\n  // Auto-hide functionality\n  if (autoHide && isVisible) {\n    setTimeout(() => setIsVisible(false), autoHideDelay);\n  }\n\n  const barVariants = {\n    hidden: {\n      height: 0,\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut'\n      }\n    },\n    visible: {\n      height: 'auto',\n      opacity: 1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  const contentVariants = {\n    hidden: {\n      y: -20,\n      opacity: 0\n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        delay: 0.1,\n        duration: 0.3\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          variants={barVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"hidden\"\n          className={`relative overflow-hidden ${currentStyle.bg} ${className}`}\n        >\n          <motion.div\n            variants={contentVariants}\n            className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"\n          >\n            <div className=\"flex items-center justify-between py-3\">\n              <div className=\"flex items-center space-x-4\">\n                {/* Icon based on type */}\n                <div className={`flex-shrink-0 ${currentStyle.accent}`}>\n                  {type === 'info' && (\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                  {type === 'success' && (\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                  {type === 'warning' && (\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                  {type === 'promotion' && (\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                    </svg>\n                  )}\n                </div>\n\n                {/* Message */}\n                <p className={`text-sm font-medium ${currentStyle.text}`}>\n                  {message}\n                </p>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                {/* CTA Button */}\n                {ctaText && ctaLink && (\n                  <Link\n                    href={ctaLink}\n                    className={`inline-flex items-center px-3 py-1 rounded-md text-xs font-semibold transition-colors duration-200 ${currentStyle.button}`}\n                  >\n                    {ctaText}\n                    <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </Link>\n                )}\n\n                {/* Dismiss Button */}\n                {dismissible && (\n                  <button\n                    onClick={() => setIsVisible(false)}\n                    className={`${currentStyle.text} hover:opacity-75 transition-opacity duration-200`}\n                    aria-label=\"Dismiss announcement\"\n                  >\n                    <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                  </button>\n                )}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Animated background pattern for promotion type */}\n          {type === 'promotion' && (\n            <motion.div\n              className=\"absolute inset-0 opacity-10\"\n              animate={{\n                backgroundPosition: ['0% 0%', '100% 100%'],\n              }}\n              transition={{\n                duration: 20,\n                repeat: Infinity,\n                ease: 'linear'\n              }}\n              style={{\n                backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)',\n                backgroundSize: '20px 20px'\n              }}\n            />\n          )}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAiBe,SAAS,gBAAgB,EACtC,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,MAAM,EACb,cAAc,IAAI,EAClB,WAAW,KAAK,EAChB,gBAAgB,IAAI,EACpB,YAAY,EAAE,EACO;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,SAAS;YACP,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,SAAS;YACP,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,WAAW;YACT,IAAI;Y<PERSON><PERSON>,MAAM;YAC<PERSON>,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,UAAU,CAAC,KAAK;IAErC,0BAA0B;IAC1B,IAAI,YAAY,WAAW;QACzB,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,MAAM,cAAc;QAClB,QAAQ;YACN,QAAQ;YACR,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,SAAS;YACP,QAAQ;YACR,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,GAAG,CAAC;YACJ,SAAS;QACX;QACA,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,OAAO;gBACP,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,WAAW,CAAC,yBAAyB,EAAE,aAAa,EAAE,CAAC,CAAC,EAAE,WAAW;;8BAErE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAW,CAAC,cAAc,EAAE,aAAa,MAAM,EAAE;;4CACnD,SAAS,wBACR,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAmI,UAAS;;;;;;;;;;;4CAG1K,SAAS,2BACR,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwI,UAAS;;;;;;;;;;;4CAG/K,SAAS,2BACR,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoN,UAAS;;;;;;;;;;;4CAG3P,SAAS,6BACR,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;kDAMd,8OAAC;wCAAE,WAAW,CAAC,oBAAoB,EAAE,aAAa,IAAI,EAAE;kDACrD;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;oCAEZ,WAAW,yBACV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAW,CAAC,mGAAmG,EAAE,aAAa,MAAM,EAAE;;4CAErI;0DACD,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;oCAM1E,6BACC,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,GAAG,aAAa,IAAI,CAAC,iDAAiD,CAAC;wCAClF,cAAW;kDAEX,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShF,SAAS,6BACR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,oBAAoB;4BAAC;4BAAS;yBAAY;oBAC5C;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;oBACA,OAAO;wBACL,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/FeatureIcons.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface Feature {\n  icon: ReactNode;\n  title: string;\n  description: string;\n  color?: string;\n}\n\ninterface FeatureIconsProps {\n  features: Feature[];\n  layout?: 'grid' | 'horizontal' | 'vertical';\n  animationDelay?: number;\n  hoverEffect?: boolean;\n  className?: string;\n}\n\nexport default function FeatureIcons({\n  features,\n  layout = 'grid',\n  animationDelay = 0.1,\n  hoverEffect = true,\n  className = ''\n}: FeatureIconsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: animationDelay,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20,\n      scale: 0.9\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const hoverVariants = hoverEffect ? {\n    hover: {\n      scale: 1.05,\n      y: -5,\n      transition: {\n        duration: 0.2,\n        ease: 'easeOut'\n      }\n    }\n  } : {};\n\n  const layoutClasses = {\n    grid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',\n    horizontal: 'flex flex-wrap justify-center gap-6',\n    vertical: 'flex flex-col space-y-6'\n  };\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`${layoutClasses[layout]} ${className}`}\n    >\n      {features.map((feature, index) => (\n        <motion.div\n          key={index}\n          variants={itemVariants}\n          whileHover=\"hover\"\n          className=\"group\"\n        >\n          <motion.div\n            variants={hoverVariants}\n            className=\"flex flex-col items-center text-center p-6 bg-cream-50 rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300\"\n          >\n            {/* Icon Container */}\n            <motion.div\n              className={`flex items-center justify-center w-16 h-16 rounded-full mb-4 ${\n                feature.color || 'bg-primary-100 text-primary-600'\n              }`}\n              whileHover={{\n                rotate: [0, -10, 10, -10, 0],\n                transition: { duration: 0.5 }\n              }}\n            >\n              <div className=\"text-2xl\">\n                {feature.icon}\n              </div>\n            </motion.div>\n\n            {/* Title */}\n            <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n              {feature.title}\n            </h3>\n\n            {/* Description */}\n            <p className=\"text-sm text-gray-600 leading-relaxed\">\n              {feature.description}\n            </p>\n\n            {/* Animated underline */}\n            <motion.div\n              className=\"w-0 h-0.5 bg-primary-500 mt-3 group-hover:w-12 transition-all duration-300\"\n              initial={{ width: 0 }}\n              whileHover={{ width: 48 }}\n            />\n          </motion.div>\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n}\n\n// Predefined icon components for common cannabis/apothecary features\nexport const CannabisIcons = {\n  Quality: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n    </svg>\n  ),\n  Lab: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M9 2v6l-4 4v8a2 2 0 002 2h10a2 2 0 002-2v-8l-4-4V2H9zm2 2h2v5.5l4 4V20H7v-6.5l4-4V4z\" />\n    </svg>\n  ),\n  Organic: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" />\n    </svg>\n  ),\n  Security: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z\" />\n    </svg>\n  ),\n  Fast: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M13 1v8h8l-8 14v-8H5l8-14z\" />\n    </svg>\n  ),\n  Support: (\n    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-6 h-6\">\n      <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" />\n    </svg>\n  )\n};\n\n// Example usage component\nexport function ExampleFeatureIcons() {\n  const features = [\n    {\n      icon: CannabisIcons.Quality,\n      title: 'Premium Quality',\n      description: 'Lab-tested products ensuring the highest standards of purity and potency.',\n      color: 'bg-primary-100 text-primary-600'\n    },\n    {\n      icon: CannabisIcons.Lab,\n      title: 'Lab Tested',\n      description: 'Every product undergoes rigorous testing for safety and consistency.',\n      color: 'bg-sage-100 text-sage-600'\n    },\n    {\n      icon: CannabisIcons.Organic,\n      title: 'Organic Grown',\n      description: 'Sustainably cultivated using organic farming practices.',\n      color: 'bg-gold-100 text-gold-600'\n    },\n    {\n      icon: CannabisIcons.Security,\n      title: 'Secure & Legal',\n      description: 'Fully compliant with state regulations and secure transactions.',\n      color: 'bg-cream-200 text-charcoal-600'\n    }\n  ];\n\n  return <FeatureIcons features={features} />;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAoBe,SAAS,aAAa,EACnC,QAAQ,EACR,SAAS,MAAM,EACf,iBAAiB,GAAG,EACpB,cAAc,IAAI,EAClB,YAAY,EAAE,EACI;IAClB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB,cAAc;QAClC,OAAO;YACL,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF,IAAI,CAAC;IAEL,MAAM,gBAAgB;QACpB,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW;kBAEjD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,UAAU;gBACV,YAAW;gBACX,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,CAAC,6DAA6D,EACvE,QAAQ,KAAK,IAAI,mCACjB;4BACF,YAAY;gCACV,QAAQ;oCAAC;oCAAG,CAAC;oCAAI;oCAAI,CAAC;oCAAI;iCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;sCAEA,cAAA,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI;;;;;;;;;;;sCAKjB,8OAAC;4BAAG,WAAU;sCACX,QAAQ,KAAK;;;;;;sCAIhB,8OAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW;;;;;;sCAItB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;4BAAG;;;;;;;;;;;;eAtCvB;;;;;;;;;;AA6Cf;AAGO,MAAM,gBAAgB;IAC3B,uBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,mBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,uBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,wBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,oBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,uBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAe,WAAU;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAGO,SAAS;IACd,MAAM,WAAW;QACf;YACE,MAAM,cAAc,OAAO;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,cAAc,GAAG;YACvB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,cAAc,OAAO;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,cAAc,QAAQ;YAC5B,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBAAO,8OAAC;QAAa,UAAU;;;;;;AACjC", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/Tabs.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, ReactNode } from 'react';\n\ninterface Tab {\n  id: string;\n  label: string;\n  content: ReactNode;\n  icon?: ReactNode;\n  badge?: string | number;\n}\n\ninterface TabsProps {\n  tabs: Tab[];\n  defaultTab?: string;\n  variant?: 'default' | 'soft' | 'pills' | 'underline';\n  orientation?: 'horizontal' | 'vertical';\n  className?: string;\n  onTabChange?: (tabId: string) => void;\n}\n\nexport default function Tabs({\n  tabs,\n  defaultTab,\n  variant = 'default',\n  orientation = 'horizontal',\n  className = '',\n  onTabChange\n}: TabsProps) {\n  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);\n\n  const handleTabChange = (tabId: string) => {\n    setActiveTab(tabId);\n    onTabChange?.(tabId);\n  };\n\n  const variantStyles = {\n    default: {\n      container: 'border-b border-cream-300',\n      tab: 'px-4 py-2 font-medium text-sm border-b-2 border-transparent hover:text-primary-600 hover:border-primary-300 transition-colors duration-200',\n      activeTab: 'text-primary-600 border-primary-600',\n      content: 'py-6'\n    },\n    soft: {\n      container: 'bg-cream-100 rounded-lg p-1',\n      tab: 'px-4 py-2 font-medium text-sm rounded-md hover:bg-cream-200 transition-colors duration-200',\n      activeTab: 'bg-cream-50 text-primary-600 shadow-soft',\n      content: 'py-6'\n    },\n    pills: {\n      container: 'space-x-2',\n      tab: 'px-4 py-2 font-medium text-sm rounded-full border border-cream-300 hover:border-primary-300 hover:text-primary-600 transition-colors duration-200',\n      activeTab: 'bg-primary-600 text-cream-50 border-primary-600',\n      content: 'py-6'\n    },\n    underline: {\n      container: 'space-x-6',\n      tab: 'px-0 py-2 font-medium text-sm relative hover:text-primary-600 transition-colors duration-200',\n      activeTab: 'text-primary-600',\n      content: 'py-6'\n    }\n  };\n\n  const styles = variantStyles[variant];\n  const isVertical = orientation === 'vertical';\n\n  const tabVariants = {\n    hidden: { opacity: 0, y: 10 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  const contentVariants = {\n    hidden: { opacity: 0, x: isVertical ? 20 : 0, y: isVertical ? 0 : 10 },\n    visible: { opacity: 1, x: 0, y: 0 },\n    exit: { opacity: 0, x: isVertical ? -20 : 0, y: isVertical ? 0 : -10 }\n  };\n\n  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;\n\n  return (\n    <div className={`${isVertical ? 'flex gap-6' : ''} ${className}`}>\n      {/* Tab Navigation */}\n      <div className={`${isVertical ? 'flex-shrink-0 w-64' : ''}`}>\n        <nav className={`${isVertical ? 'flex flex-col space-y-2' : `flex ${styles.container}`}`}>\n          {tabs.map((tab) => (\n            <motion.button\n              key={tab.id}\n              variants={tabVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              transition={{ duration: 0.2 }}\n              onClick={() => handleTabChange(tab.id)}\n              className={`\n                ${styles.tab}\n                ${activeTab === tab.id ? styles.activeTab : ''}\n                ${isVertical ? 'text-left justify-start' : ''}\n                flex items-center gap-2 relative\n              `}\n            >\n              {/* Icon */}\n              {tab.icon && (\n                <span className=\"flex-shrink-0\">\n                  {tab.icon}\n                </span>\n              )}\n\n              {/* Label */}\n              <span>{tab.label}</span>\n\n              {/* Badge */}\n              {tab.badge && (\n                <span className=\"ml-auto bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full\">\n                  {tab.badge}\n                </span>\n              )}\n\n              {/* Underline variant indicator */}\n              {variant === 'underline' && activeTab === tab.id && (\n                <motion.div\n                  layoutId=\"activeTabIndicator\"\n                  className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600\"\n                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}\n                />\n              )}\n\n              {/* Soft variant background */}\n              {variant === 'soft' && activeTab === tab.id && (\n                <motion.div\n                  layoutId=\"activeTabBackground\"\n                  className=\"absolute inset-0 bg-cream-50 rounded-md shadow-soft\"\n                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}\n                  style={{ zIndex: -1 }}\n                />\n              )}\n            </motion.button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className={`${isVertical ? 'flex-1' : ''} ${styles.content}`}>\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTab}\n            variants={contentVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"exit\"\n            transition={{ duration: 0.3, ease: 'easeInOut' }}\n          >\n            {activeTabContent}\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n}\n\n// Example usage with GridCards\nexport function ExampleTabbedGrid() {\n  const tabs = [\n    {\n      id: 'flower',\n      label: 'Flower',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n        </svg>\n      ),\n      badge: '12',\n      content: (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {/* Grid content would go here */}\n          <div className=\"bg-cream-50 p-6 rounded-lg\">Flower products...</div>\n        </div>\n      )\n    },\n    {\n      id: 'concentrates',\n      label: 'Concentrates',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n        </svg>\n      ),\n      badge: '8',\n      content: (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-cream-50 p-6 rounded-lg\">Concentrate products...</div>\n        </div>\n      )\n    },\n    {\n      id: 'edibles',\n      label: 'Edibles',\n      icon: (\n        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n        </svg>\n      ),\n      badge: '15',\n      content: (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div className=\"bg-cream-50 p-6 rounded-lg\">Edible products...</div>\n        </div>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <h2 className=\"text-3xl font-serif font-bold text-center mb-8\">Product Categories</h2>\n      <Tabs tabs={tabs} variant=\"soft\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AAHA;;;;AAsBe,SAAS,KAAK,EAC3B,IAAI,EACJ,UAAU,EACV,UAAU,SAAS,EACnB,cAAc,YAAY,EAC1B,YAAY,EAAE,EACd,WAAW,EACD;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE;IAElE,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,WAAW;YACX,KAAK;YACL,WAAW;YACX,SAAS;QACX;QACA,MAAM;YACJ,WAAW;YACX,KAAK;YACL,WAAW;YACX,SAAS;QACX;QACA,OAAO;YACL,WAAW;YACX,KAAK;YACL,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,KAAK;YACL,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,SAAS,aAAa,CAAC,QAAQ;IACrC,MAAM,aAAa,gBAAgB;IAEnC,MAAM,cAAc;QAClB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,GAAG,aAAa,KAAK;YAAG,GAAG,aAAa,IAAI;QAAG;QACrE,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,GAAG;QAAE;QAClC,MAAM;YAAE,SAAS;YAAG,GAAG,aAAa,CAAC,KAAK;YAAG,GAAG,aAAa,IAAI,CAAC;QAAG;IACvE;IAEA,MAAM,mBAAmB,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;IAEjE,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,eAAe,GAAG,CAAC,EAAE,WAAW;;0BAE9D,8OAAC;gBAAI,WAAW,GAAG,aAAa,uBAAuB,IAAI;0BACzD,cAAA,8OAAC;oBAAI,WAAW,GAAG,aAAa,4BAA4B,CAAC,KAAK,EAAE,OAAO,SAAS,EAAE,EAAE;8BACrF,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,SAAS,IAAM,gBAAgB,IAAI,EAAE;4BACrC,WAAW,CAAC;gBACV,EAAE,OAAO,GAAG,CAAC;gBACb,EAAE,cAAc,IAAI,EAAE,GAAG,OAAO,SAAS,GAAG,GAAG;gBAC/C,EAAE,aAAa,4BAA4B,GAAG;;cAEhD,CAAC;;gCAGA,IAAI,IAAI,kBACP,8OAAC;oCAAK,WAAU;8CACb,IAAI,IAAI;;;;;;8CAKb,8OAAC;8CAAM,IAAI,KAAK;;;;;;gCAGf,IAAI,KAAK,kBACR,8OAAC;oCAAK,WAAU;8CACb,IAAI,KAAK;;;;;;gCAKb,YAAY,eAAe,cAAc,IAAI,EAAE,kBAC9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;;;;;;gCAK7D,YAAY,UAAU,cAAc,IAAI,EAAE,kBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;oCAC1D,OAAO;wCAAE,QAAQ,CAAC;oCAAE;;;;;;;2BA7CnB,IAAI,EAAE;;;;;;;;;;;;;;;0BAsDnB,8OAAC;gBAAI,WAAW,GAAG,aAAa,WAAW,GAAG,CAAC,EAAE,OAAO,OAAO,EAAE;0BAC/D,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;kCAE9C;uBAPI;;;;;;;;;;;;;;;;;;;;;AAajB;AAGO,SAAS;IACd,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,uBACE,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;8BAA6B;;;;;;;;;;;QAGlD;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,uBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAA6B;;;;;;;;;;;QAGlD;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,uBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAA6B;;;;;;;;;;;QAGlD;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAiD;;;;;;0BAC/D,8OAAC;gBAAK,MAAM;gBAAM,SAAQ;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/GridCards.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\nimport Image from 'next/image';\n\ninterface GridCard {\n  id: string;\n  title: string;\n  description?: string;\n  image?: string;\n  icon?: ReactNode;\n  badge?: string;\n  price?: string;\n  originalPrice?: string;\n  features?: string[];\n  cta?: {\n    text: string;\n    action: () => void;\n  };\n  href?: string;\n}\n\ninterface GridCardsProps {\n  cards: GridCard[];\n  columns?: 1 | 2 | 3 | 4 | 5 | 6;\n  gap?: 'sm' | 'md' | 'lg';\n  cardVariant?: 'default' | 'elevated' | 'bordered' | 'minimal';\n  animationDelay?: number;\n  className?: string;\n}\n\nexport default function GridCards({\n  cards,\n  columns = 3,\n  gap = 'md',\n  cardVariant = 'default',\n  animationDelay = 0.1,\n  className = ''\n}: GridCardsProps) {\n  const columnClasses = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',\n    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',\n    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'\n  };\n\n  const gapClasses = {\n    sm: 'gap-4',\n    md: 'gap-6',\n    lg: 'gap-8'\n  };\n\n  const cardVariants = {\n    default: 'bg-cream-50 rounded-lg shadow-soft hover:shadow-medium',\n    elevated: 'bg-cream-50 rounded-xl shadow-medium hover:shadow-large',\n    bordered: 'bg-cream-50 rounded-lg border border-cream-300 hover:border-primary-300',\n    minimal: 'bg-transparent hover:bg-cream-50 rounded-lg'\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: animationDelay,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const cardItemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20,\n      scale: 0.95\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const hoverVariants = {\n    hover: {\n      y: -5,\n      transition: {\n        duration: 0.2,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`grid ${columnClasses[columns]} ${gapClasses[gap]} ${className}`}\n    >\n      {cards.map((card, index) => (\n        <motion.div\n          key={card.id}\n          variants={cardItemVariants}\n          whileHover=\"hover\"\n          className=\"group\"\n        >\n          <motion.div\n            variants={hoverVariants}\n            className={`${cardVariants[cardVariant]} overflow-hidden transition-all duration-300 cursor-pointer`}\n            onClick={card.cta?.action}\n          >\n            {/* Badge */}\n            {card.badge && (\n              <div className=\"absolute top-4 right-4 z-10\">\n                <span className=\"bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full\">\n                  {card.badge}\n                </span>\n              </div>\n            )}\n\n            {/* Image or Icon */}\n            {card.image ? (\n              <div className=\"relative h-48 w-full overflow-hidden\">\n                <Image\n                  src={card.image}\n                  alt={card.title}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n              </div>\n            ) : card.icon ? (\n              <div className=\"flex items-center justify-center h-24 text-primary-600\">\n                <div className=\"text-4xl\">\n                  {card.icon}\n                </div>\n              </div>\n            ) : null}\n\n            {/* Content */}\n            <div className=\"p-6\">\n              {/* Title */}\n              <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                {card.title}\n              </h3>\n\n              {/* Description */}\n              {card.description && (\n                <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                  {card.description}\n                </p>\n              )}\n\n              {/* Features */}\n              {card.features && card.features.length > 0 && (\n                <div className=\"mb-4\">\n                  <ul className=\"space-y-1\">\n                    {card.features.slice(0, 3).map((feature, idx) => (\n                      <li key={idx} className=\"flex items-center text-xs text-gray-600\">\n                        <svg className=\"w-3 h-3 text-primary-500 mr-2 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n\n              {/* Price */}\n              {card.price && (\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      {card.price}\n                    </span>\n                    {card.originalPrice && (\n                      <span className=\"text-sm text-gray-500 line-through\">\n                        {card.originalPrice}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* CTA Button */}\n              {card.cta && (\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={card.cta.action}\n                  className=\"w-full bg-primary-600 text-cream-50 py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\"\n                >\n                  {card.cta.text}\n                </motion.button>\n              )}\n            </div>\n\n            {/* Hover overlay */}\n            <motion.div\n              className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none\"\n              initial={{ opacity: 0 }}\n              whileHover={{ opacity: 0.05 }}\n            />\n          </motion.div>\n        </motion.div>\n      ))}\n    </motion.div>\n  );\n}\n\n// Example usage\nexport function ExampleGridCards() {\n  const sampleCards: GridCard[] = [\n    {\n      id: '1',\n      title: 'Premium OG Kush',\n      description: 'Classic indica strain with earthy, pine flavors and relaxing effects.',\n      image: '/assets/products/og-kush.jpg',\n      badge: 'Popular',\n      price: '$45',\n      originalPrice: '$55',\n      features: ['28% THC', 'Indoor Grown', 'Lab Tested'],\n      cta: {\n        text: 'Add to Cart',\n        action: () => console.log('Added to cart')\n      }\n    },\n    {\n      id: '2',\n      title: 'Blue Dream',\n      description: 'Balanced hybrid offering creative euphoria and gentle relaxation.',\n      image: '/assets/products/blue-dream.jpg',\n      price: '$40',\n      features: ['24% THC', 'Hybrid', 'Organic'],\n      cta: {\n        text: 'Add to Cart',\n        action: () => console.log('Added to cart')\n      }\n    },\n    {\n      id: '3',\n      title: 'Green Crack',\n      description: 'Energizing sativa perfect for daytime use and creative activities.',\n      image: '/assets/products/green-crack.jpg',\n      badge: 'New',\n      price: '$42',\n      features: ['26% THC', 'Sativa', 'Energizing'],\n      cta: {\n        text: 'Add to Cart',\n        action: () => console.log('Added to cart')\n      }\n    }\n  ];\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <h2 className=\"text-3xl font-serif font-bold text-center mb-8\">Featured Products</h2>\n      <GridCards cards={sampleCards} columns={3} cardVariant=\"elevated\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgCe,SAAS,UAAU,EAChC,KAAK,EACL,UAAU,CAAC,EACX,MAAM,IAAI,EACV,cAAc,SAAS,EACvB,iBAAiB,GAAG,EACpB,YAAY,EAAE,EACC;IACf,MAAM,gBAAgB;QACpB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,UAAU;QACV,UAAU;QACV,SAAS;IACX;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO;YACL,GAAG,CAAC;YACJ,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW;kBAE1E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,UAAU;gBACV,YAAW;gBACX,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC,2DAA2D,CAAC;oBACpG,SAAS,KAAK,GAAG,EAAE;;wBAGlB,KAAK,KAAK,kBACT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,KAAK,KAAK;;;;;;;;;;;wBAMhB,KAAK,KAAK,iBACT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,KAAK;gCACf,KAAK,KAAK,KAAK;gCACf,IAAI;gCACJ,WAAU;;;;;;;;;;mCAGZ,KAAK,IAAI,iBACX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI;;;;;;;;;;mCAGZ;sCAGJ,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;gCAIZ,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;gCAKpB,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,oBACvC,8OAAC;gDAAa,WAAU;;kEACtB,8OAAC;wDAAI,WAAU;wDAA8C,MAAK;wDAAe,SAAQ;kEACvF,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDAE1J;;+CAJM;;;;;;;;;;;;;;;gCAYhB,KAAK,KAAK,kBACT,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,KAAK,aAAa,kBACjB,8OAAC;gDAAK,WAAU;0DACb,KAAK,aAAa;;;;;;;;;;;;;;;;;gCAQ5B,KAAK,GAAG,kBACP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,KAAK,GAAG,CAAC,MAAM;oCACxB,WAAU;8CAET,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;sCAMpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,SAAS;4BAAK;;;;;;;;;;;;eApG3B,KAAK,EAAE;;;;;;;;;;AA2GtB;AAGO,SAAS;IACd,MAAM,cAA0B;QAC9B;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,eAAe;YACf,UAAU;gBAAC;gBAAW;gBAAgB;aAAa;YACnD,KAAK;gBACH,MAAM;gBACN,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC5B;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,UAAU;gBAAC;gBAAW;gBAAU;aAAU;YAC1C,KAAK;gBACH,MAAM;gBACN,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC5B;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,UAAU;gBAAC;gBAAW;gBAAU;aAAa;YAC7C,KAAK;gBACH,MAAM;gBACN,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC5B;QACF;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAiD;;;;;;0BAC/D,8OAAC;gBAAU,OAAO;gBAAa,SAAS;gBAAG,aAAY;;;;;;;;;;;;AAG7D", "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/SplitMediaText.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\nimport Image from 'next/image';\n\ninterface SplitMediaTextProps {\n  title: string;\n  subtitle?: string;\n  content: ReactNode;\n  media: {\n    type: 'image' | 'video';\n    src: string;\n    alt?: string;\n    poster?: string; // For video\n  };\n  layout?: 'default' | 'reversed';\n  mediaPosition?: 'left' | 'right';\n  backgroundColor?: string;\n  textAlign?: 'left' | 'center' | 'right';\n  cta?: {\n    text: string;\n    href?: string;\n    action?: () => void;\n    variant?: 'primary' | 'secondary' | 'outline';\n  };\n  className?: string;\n}\n\nexport default function SplitMediaText({\n  title,\n  subtitle,\n  content,\n  media,\n  layout = 'default',\n  mediaPosition = 'right',\n  backgroundColor = 'bg-cream-50',\n  textAlign = 'left',\n  cta,\n  className = ''\n}: SplitMediaTextProps) {\n  const isReversed = layout === 'reversed' || mediaPosition === 'left';\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const textVariants = {\n    hidden: {\n      opacity: 0,\n      x: isReversed ? 50 : -50,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      x: 0,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const mediaVariants = {\n    hidden: {\n      opacity: 0,\n      x: isReversed ? -50 : 50,\n      scale: 0.95\n    },\n    visible: {\n      opacity: 1,\n      x: 0,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const ctaVariants = {\n    primary: 'bg-primary-600 text-cream-50 hover:bg-primary-700',\n    secondary: 'bg-gold-500 text-primary-800 hover:bg-gold-400',\n    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-cream-50'\n  };\n\n  const textAlignClasses = {\n    left: 'text-left',\n    center: 'text-center',\n    right: 'text-right'\n  };\n\n  return (\n    <motion.section\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-100px' }}\n      className={`py-16 lg:py-24 ${backgroundColor} ${className}`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center ${isReversed ? 'lg:grid-flow-col-dense' : ''}`}>\n          \n          {/* Text Content */}\n          <motion.div\n            variants={textVariants}\n            className={`${isReversed ? 'lg:col-start-2' : ''} ${textAlignClasses[textAlign]}`}\n          >\n            {/* Subtitle */}\n            {subtitle && (\n              <motion.p\n                className=\"text-primary-600 font-medium text-sm uppercase tracking-wide mb-4\"\n                variants={{\n                  hidden: { opacity: 0, y: 10 },\n                  visible: { opacity: 1, y: 0 }\n                }}\n              >\n                {subtitle}\n              </motion.p>\n            )}\n\n            {/* Title */}\n            <motion.h2\n              className=\"text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-foreground mb-6 leading-tight\"\n              variants={{\n                hidden: { opacity: 0, y: 20 },\n                visible: { opacity: 1, y: 0 }\n              }}\n            >\n              {title}\n            </motion.h2>\n\n            {/* Content */}\n            <motion.div\n              className=\"text-gray-600 text-lg leading-relaxed mb-8\"\n              variants={{\n                hidden: { opacity: 0, y: 20 },\n                visible: { opacity: 1, y: 0 }\n              }}\n            >\n              {content}\n            </motion.div>\n\n            {/* CTA Button */}\n            {cta && (\n              <motion.div\n                variants={{\n                  hidden: { opacity: 0, y: 20 },\n                  visible: { opacity: 1, y: 0 }\n                }}\n              >\n                {cta.href ? (\n                  <a\n                    href={cta.href}\n                    className={`inline-flex items-center px-8 py-4 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${ctaVariants[cta.variant || 'primary']}`}\n                  >\n                    {cta.text}\n                    <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </a>\n                ) : (\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={cta.action}\n                    className={`inline-flex items-center px-8 py-4 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${ctaVariants[cta.variant || 'primary']}`}\n                  >\n                    {cta.text}\n                    <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </motion.button>\n                )}\n              </motion.div>\n            )}\n          </motion.div>\n\n          {/* Media Content */}\n          <motion.div\n            variants={mediaVariants}\n            className={`${isReversed ? 'lg:col-start-1' : ''} relative`}\n          >\n            <div className=\"relative overflow-hidden rounded-2xl shadow-large\">\n              {media.type === 'image' ? (\n                <div className=\"relative aspect-[4/3] w-full\">\n                  <Image\n                    src={media.src}\n                    alt={media.alt || title}\n                    fill\n                    className=\"object-cover\"\n                    sizes=\"(max-width: 768px) 100vw, 50vw\"\n                  />\n                </div>\n              ) : (\n                <div className=\"relative aspect-[16/9] w-full\">\n                  <video\n                    src={media.src}\n                    poster={media.poster}\n                    controls\n                    className=\"w-full h-full object-cover\"\n                    preload=\"metadata\"\n                  >\n                    Your browser does not support the video tag.\n                  </video>\n                </div>\n              )}\n\n              {/* Decorative overlay */}\n              <div className=\"absolute inset-0 bg-gradient-to-tr from-primary-600/10 to-transparent pointer-events-none\" />\n            </div>\n\n            {/* Floating decoration */}\n            <motion.div\n              className=\"absolute -top-6 -right-6 w-24 h-24 bg-gold-200 rounded-full opacity-20\"\n              animate={{\n                y: [0, -10, 0],\n                rotate: [0, 5, 0]\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: 'easeInOut'\n              }}\n            />\n            <motion.div\n              className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-primary-200 rounded-full opacity-30\"\n              animate={{\n                y: [0, 10, 0],\n                rotate: [0, -5, 0]\n              }}\n              transition={{\n                duration: 3,\n                repeat: Infinity,\n                ease: 'easeInOut',\n                delay: 1\n              }}\n            />\n          </motion.div>\n        </div>\n      </div>\n    </motion.section>\n  );\n}\n\n// Example usage\nexport function ExampleSplitMediaText() {\n  return (\n    <SplitMediaText\n      subtitle=\"Our Story\"\n      title=\"Crafting Excellence Since 2015\"\n      content={\n        <div>\n          <p className=\"mb-4\">\n            At Apothecary Extracts, we've been pioneering premium cannabis cultivation and extraction \n            techniques for nearly a decade. Our commitment to quality, innovation, and customer \n            satisfaction has made us Colorado's trusted source for exceptional cannabis products.\n          </p>\n          <p>\n            From our state-of-the-art facilities to our expert team of cultivators and extractors, \n            every step of our process is designed to deliver the purest, most potent products possible.\n          </p>\n        </div>\n      }\n      media={{\n        type: 'image',\n        src: '/assets/about/facility.jpg',\n        alt: 'Apothecary Extracts facility'\n      }}\n      layout=\"reversed\"\n      cta={{\n        text: 'Learn More About Us',\n        href: '/about',\n        variant: 'primary'\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AA6Be,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,SAAS,EAClB,gBAAgB,OAAO,EACvB,kBAAkB,aAAa,EAC/B,YAAY,MAAM,EAClB,GAAG,EACH,YAAY,EAAE,EACM;IACpB,MAAM,aAAa,WAAW,cAAc,kBAAkB;IAE9D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG,aAAa,KAAK,CAAC;YACtB,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ;YACN,SAAS;YACT,GAAG,aAAa,CAAC,KAAK;YACtB,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,mBAAmB;QACvB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,WAAW,CAAC,eAAe,EAAE,gBAAgB,CAAC,EAAE,WAAW;kBAE3D,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,aAAa,2BAA2B,IAAI;;kCAG3H,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAW,GAAG,aAAa,mBAAmB,GAAG,CAAC,EAAE,gBAAgB,CAAC,UAAU,EAAE;;4BAGhF,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,UAAU;oCACR,QAAQ;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC5B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;gCAC9B;0CAEC;;;;;;0CAKL,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,UAAU;oCACR,QAAQ;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC5B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;gCAC9B;0CAEC;;;;;;0CAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;oCACR,QAAQ;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC5B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;gCAC9B;0CAEC;;;;;;4BAIF,qBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;oCACR,QAAQ;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC5B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;gCAC9B;0CAEC,IAAI,IAAI,iBACP,8OAAC;oCACC,MAAM,IAAI,IAAI;oCACd,WAAW,CAAC,4IAA4I,EAAE,WAAW,CAAC,IAAI,OAAO,IAAI,UAAU,EAAE;;wCAEhM,IAAI,IAAI;sDACT,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;yDAIzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAI,MAAM;oCACnB,WAAW,CAAC,4IAA4I,EAAE,WAAW,CAAC,IAAI,OAAO,IAAI,UAAU,EAAE;;wCAEhM,IAAI,IAAI;sDACT,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAW,GAAG,aAAa,mBAAmB,GAAG,SAAS,CAAC;;0CAE3D,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,IAAI,KAAK,wBACd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,MAAM,GAAG;4CACd,KAAK,MAAM,GAAG,IAAI;4CAClB,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;;;;;6DAIV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,MAAM,GAAG;4CACd,QAAQ,MAAM,MAAM;4CACpB,QAAQ;4CACR,WAAU;4CACV,SAAQ;sDACT;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,QAAQ;wCAAC;wCAAG;wCAAG;qCAAE;gCACnB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;;;;;;0CAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;oCACb,QAAQ;wCAAC;wCAAG,CAAC;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;oCACN,OAAO;gCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;AAGO,SAAS;IACd,qBACE,8OAAC;QACC,UAAS;QACT,OAAM;QACN,uBACE,8OAAC;;8BACC,8OAAC;oBAAE,WAAU;8BAAO;;;;;;8BAKpB,8OAAC;8BAAE;;;;;;;;;;;;QAMP,OAAO;YACL,MAAM;YACN,KAAK;YACL,KAAK;QACP;QACA,QAAO;QACP,KAAK;YACH,MAAM;YACN,MAAM;YACN,SAAS;QACX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/FAQAccordion.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState } from 'react';\n\ninterface FAQ {\n  id: string;\n  question: string;\n  answer: string;\n  category?: string;\n}\n\ninterface FAQAccordionProps {\n  faqs: FAQ[];\n  allowMultiple?: boolean;\n  defaultOpen?: string[];\n  variant?: 'default' | 'bordered' | 'minimal';\n  className?: string;\n}\n\nexport default function FAQAccordion({\n  faqs,\n  allowMultiple = false,\n  defaultOpen = [],\n  variant = 'default',\n  className = ''\n}: FAQAccordionProps) {\n  const [openItems, setOpenItems] = useState<string[]>(defaultOpen);\n\n  const toggleItem = (id: string) => {\n    if (allowMultiple) {\n      setOpenItems(prev =>\n        prev.includes(id)\n          ? prev.filter(item => item !== id)\n          : [...prev, id]\n      );\n    } else {\n      setOpenItems(prev =>\n        prev.includes(id) ? [] : [id]\n      );\n    }\n  };\n\n  const variantStyles = {\n    default: {\n      container: 'bg-cream-50 rounded-lg shadow-soft',\n      item: 'border-b border-cream-200 last:border-b-0',\n      button: 'w-full px-6 py-4 text-left hover:bg-cream-100 transition-colors duration-200',\n      content: 'px-6 pb-4'\n    },\n    bordered: {\n      container: 'space-y-4',\n      item: 'border border-cream-300 rounded-lg overflow-hidden',\n      button: 'w-full px-6 py-4 text-left hover:bg-cream-50 transition-colors duration-200',\n      content: 'px-6 pb-4 bg-cream-25'\n    },\n    minimal: {\n      container: 'space-y-2',\n      item: 'border-b border-cream-200 pb-2',\n      button: 'w-full py-3 text-left hover:text-primary-600 transition-colors duration-200',\n      content: 'pb-3'\n    }\n  };\n\n  const styles = variantStyles[variant];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.4,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const contentVariants = {\n    hidden: {\n      height: 0,\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut'\n      }\n    },\n    visible: {\n      height: 'auto',\n      opacity: 1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  const iconVariants = {\n    closed: { rotate: 0 },\n    open: { rotate: 180 }\n  };\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`${styles.container} ${className}`}\n    >\n      {faqs.map((faq) => {\n        const isOpen = openItems.includes(faq.id);\n\n        return (\n          <motion.div\n            key={faq.id}\n            variants={itemVariants}\n            className={styles.item}\n          >\n            <motion.button\n              onClick={() => toggleItem(faq.id)}\n              className={`${styles.button} flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset`}\n              whileHover={{ backgroundColor: 'rgba(0,0,0,0.02)' }}\n            >\n              <span className=\"font-semibold text-foreground pr-4\">\n                {faq.question}\n              </span>\n              \n              <motion.div\n                variants={iconVariants}\n                animate={isOpen ? 'open' : 'closed'}\n                transition={{ duration: 0.2 }}\n                className=\"flex-shrink-0 text-primary-600\"\n              >\n                <svg\n                  className=\"w-5 h-5\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M19 9l-7 7-7-7\"\n                  />\n                </svg>\n              </motion.div>\n            </motion.button>\n\n            <AnimatePresence>\n              {isOpen && (\n                <motion.div\n                  variants={contentVariants}\n                  initial=\"hidden\"\n                  animate=\"visible\"\n                  exit=\"hidden\"\n                  className={styles.content}\n                >\n                  <div className=\"text-gray-600 leading-relaxed\">\n                    {faq.answer}\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        );\n      })}\n    </motion.div>\n  );\n}\n\n// Example usage with cannabis-related FAQs\nexport function ExampleFAQAccordion() {\n  const cannabisFAQs: FAQ[] = [\n    {\n      id: '1',\n      question: 'What are the legal requirements for purchasing cannabis in Colorado?',\n      answer: 'You must be 21 years or older with a valid government-issued ID. Medical marijuana patients with a valid medical marijuana card can purchase at 18. All purchases are subject to state and local taxes.',\n      category: 'Legal'\n    },\n    {\n      id: '2',\n      question: 'What\\'s the difference between indica, sativa, and hybrid strains?',\n      answer: 'Indica strains typically provide relaxing, sedating effects ideal for evening use. Sativa strains tend to be more energizing and uplifting, perfect for daytime activities. Hybrid strains combine characteristics of both, offering balanced effects.',\n      category: 'Education'\n    },\n    {\n      id: '3',\n      question: 'How should I store my cannabis products?',\n      answer: 'Store cannabis in a cool, dark, dry place away from children and pets. Use airtight containers to maintain freshness and potency. Avoid exposure to heat, light, and moisture which can degrade quality.',\n      category: 'Storage'\n    },\n    {\n      id: '4',\n      question: 'What payment methods do you accept?',\n      answer: 'We accept cash, debit cards, and CanPay. Due to federal banking regulations, we cannot accept credit cards. ATMs are available on-site for your convenience.',\n      category: 'Payment'\n    },\n    {\n      id: '5',\n      question: 'Do you offer delivery services?',\n      answer: 'Yes, we offer delivery within our service area. Delivery orders must meet minimum purchase requirements and are subject to delivery fees. Same-day delivery is available for orders placed before 2 PM.',\n      category: 'Delivery'\n    },\n    {\n      id: '6',\n      question: 'How do I know which product is right for me?',\n      answer: 'Our knowledgeable budtenders are here to help! We\\'ll discuss your experience level, desired effects, and preferences to recommend the perfect products. Start low and go slow, especially with edibles.',\n      category: 'Guidance'\n    }\n  ];\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <div className=\"text-center mb-12\">\n        <h2 className=\"text-3xl font-serif font-bold text-foreground mb-4\">\n          Frequently Asked Questions\n        </h2>\n        <p className=\"text-lg text-gray-600\">\n          Everything you need to know about cannabis and our services\n        </p>\n      </div>\n      \n      <FAQAccordion \n        faqs={cannabisFAQs} \n        variant=\"bordered\"\n        allowMultiple={true}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AAHA;;;;AAoBe,SAAS,aAAa,EACnC,IAAI,EACJ,gBAAgB,KAAK,EACrB,cAAc,EAAE,EAChB,UAAU,SAAS,EACnB,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAErD,MAAM,aAAa,CAAC;QAClB,IAAI,eAAe;YACjB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,MAC7B;uBAAI;oBAAM;iBAAG;QAErB,OAAO;YACL,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,MAAM,EAAE,GAAG;oBAAC;iBAAG;QAEjC;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,WAAW;YACX,MAAM;YACN,QAAQ;YACR,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,MAAM;YACN,QAAQ;YACR,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,MAAM;YACN,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,SAAS,aAAa,CAAC,QAAQ;IAErC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,QAAQ;YACR,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,SAAS;YACP,QAAQ;YACR,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,QAAQ;QAAE;QACpB,MAAM;YAAE,QAAQ;QAAI;IACtB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,GAAG,OAAO,SAAS,CAAC,CAAC,EAAE,WAAW;kBAE5C,KAAK,GAAG,CAAC,CAAC;YACT,MAAM,SAAS,UAAU,QAAQ,CAAC,IAAI,EAAE;YAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,UAAU;gBACV,WAAW,OAAO,IAAI;;kCAEtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS,IAAM,WAAW,IAAI,EAAE;wBAChC,WAAW,GAAG,OAAO,MAAM,CAAC,0GAA0G,CAAC;wBACvI,YAAY;4BAAE,iBAAiB;wBAAmB;;0CAElD,8OAAC;gCAAK,WAAU;0CACb,IAAI,QAAQ;;;;;;0CAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,SAAS,SAAS,SAAS;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAMV,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,WAAW,OAAO,OAAO;sCAEzB,cAAA,8OAAC;gCAAI,WAAU;0CACZ,IAAI,MAAM;;;;;;;;;;;;;;;;;eA7Cd,IAAI,EAAE;;;;;QAoDjB;;;;;;AAGN;AAGO,SAAS;IACd,MAAM,eAAsB;QAC1B;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC;gBACC,MAAM;gBACN,SAAQ;gBACR,eAAe;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/CardSlider.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useEffect, useRef } from 'react';\nimport Image from 'next/image';\n\ninterface SliderCard {\n  id: string;\n  title: string;\n  description?: string;\n  image?: string;\n  icon?: string;\n  emoji?: string;\n  badge?: string;\n  price?: string;\n  cta?: {\n    text: string;\n    action: () => void;\n  };\n}\n\ninterface CardSliderProps {\n  cards: SliderCard[];\n  autoScroll?: boolean;\n  autoScrollInterval?: number;\n  showDots?: boolean;\n  showArrows?: boolean;\n  slidesToShow?: number;\n  gap?: number;\n  className?: string;\n}\n\nexport default function CardSlider({\n  cards,\n  autoScroll = true,\n  autoScrollInterval = 4000,\n  showDots = true,\n  showArrows = true,\n  slidesToShow = 3,\n  gap = 24,\n  className = ''\n}: CardSliderProps) {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isHovered, setIsHovered] = useState(false);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  const maxIndex = Math.max(0, cards.length - slidesToShow);\n\n  // Auto-scroll functionality\n  useEffect(() => {\n    if (autoScroll && !isHovered && cards.length > slidesToShow) {\n      intervalRef.current = setInterval(() => {\n        setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));\n      }, autoScrollInterval);\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [autoScroll, isHovered, autoScrollInterval, maxIndex, cards.length, slidesToShow]);\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(Math.max(0, Math.min(index, maxIndex)));\n  };\n\n  const nextSlide = () => {\n    setCurrentIndex(prev => (prev >= maxIndex ? 0 : prev + 1));\n  };\n\n  const prevSlide = () => {\n    setCurrentIndex(prev => (prev <= 0 ? maxIndex : prev - 1));\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 20 },\n    visible: { opacity: 1, scale: 1, y: 0 },\n    hover: { y: -8, scale: 1.02 }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`relative ${className}`}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Slider Container */}\n      <div className=\"overflow-hidden\">\n        <motion.div\n          className=\"flex transition-transform duration-500 ease-out\"\n          style={{\n            transform: `translateX(-${currentIndex * (100 / slidesToShow)}%)`,\n            gap: `${gap}px`\n          }}\n        >\n          {cards.map((card, index) => (\n            <motion.div\n              key={card.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              className=\"flex-shrink-0\"\n              style={{ width: `calc(${100 / slidesToShow}% - ${gap * (slidesToShow - 1) / slidesToShow}px)` }}\n            >\n              <div className=\"bg-cream-50 rounded-xl shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden group\">\n                {/* Image/Icon/Emoji */}\n                <div className=\"relative h-48 bg-gradient-to-br from-primary-100 to-sage-100 flex items-center justify-center\">\n                  {card.image ? (\n                    <Image\n                      src={card.image}\n                      alt={card.title}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : card.emoji ? (\n                    <span className=\"text-6xl\">{card.emoji}</span>\n                  ) : card.icon ? (\n                    <div className=\"text-4xl text-primary-600\">{card.icon}</div>\n                  ) : (\n                    <div className=\"w-16 h-16 bg-primary-200 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-8 h-8 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M10 2L3 7v11h14V7l-7-5z\" />\n                      </svg>\n                    </div>\n                  )}\n\n                  {/* Badge */}\n                  {card.badge && (\n                    <div className=\"absolute top-3 right-3\">\n                      <span className=\"bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full\">\n                        {card.badge}\n                      </span>\n                    </div>\n                  )}\n\n                  {/* Hover overlay */}\n                  <div className=\"absolute inset-0 bg-primary-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\" />\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                    {card.title}\n                  </h3>\n\n                  {card.description && (\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                      {card.description}\n                    </p>\n                  )}\n\n                  <div className=\"flex items-center justify-between\">\n                    {card.price && (\n                      <span className=\"text-lg font-bold text-primary-600\">\n                        {card.price}\n                      </span>\n                    )}\n\n                    {card.cta && (\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={card.cta.action}\n                        className=\"bg-primary-600 text-cream-50 px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors duration-200\"\n                      >\n                        {card.cta.text}\n                      </motion.button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {showArrows && cards.length > slidesToShow && (\n        <>\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={prevSlide}\n            className=\"absolute left-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full shadow-medium flex items-center justify-center transition-colors duration-200 z-10\"\n            aria-label=\"Previous slide\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </motion.button>\n\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={nextSlide}\n            className=\"absolute right-4 top-1/2 -translate-y-1/2 bg-cream-50 hover:bg-primary-600 hover:text-cream-50 text-primary-600 w-10 h-10 rounded-full shadow-medium flex items-center justify-center transition-colors duration-200 z-10\"\n            aria-label=\"Next slide\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {showDots && cards.length > slidesToShow && (\n        <div className=\"flex justify-center mt-6 space-x-2\">\n          {Array.from({ length: maxIndex + 1 }).map((_, index) => (\n            <motion.button\n              key={index}\n              whileHover={{ scale: 1.2 }}\n              whileTap={{ scale: 0.8 }}\n              onClick={() => goToSlide(index)}\n              className={`w-3 h-3 rounded-full transition-colors duration-200 ${\n                index === currentIndex\n                  ? 'bg-primary-600'\n                  : 'bg-cream-300 hover:bg-primary-300'\n              }`}\n              aria-label={`Go to slide ${index + 1}`}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Progress Bar */}\n      {autoScroll && !isHovered && (\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-cream-200\">\n          <motion.div\n            className=\"h-full bg-primary-600\"\n            initial={{ width: '0%' }}\n            animate={{ width: '100%' }}\n            transition={{\n              duration: autoScrollInterval / 1000,\n              ease: 'linear',\n              repeat: Infinity\n            }}\n          />\n        </div>\n      )}\n    </motion.div>\n  );\n}\n\n// Example usage\nexport function ExampleCardSlider() {\n  const sampleCards: SliderCard[] = [\n    {\n      id: '1',\n      title: '🔥 Live Resin Hits',\n      description: 'High-terpene, high-vibes. Dabs for days.',\n      emoji: '🔥',\n      badge: 'Hot',\n      price: '$45',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Live Resin') }\n    },\n    {\n      id: '2',\n      title: '🌿 Rare Drops',\n      description: 'Limited strains you\\'ll brag about.',\n      emoji: '🌿',\n      badge: 'Limited',\n      price: '$60',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Rare Drops') }\n    },\n    {\n      id: '3',\n      title: '🛍️ Daily Deals',\n      description: 'Fresh savings, every single day.',\n      emoji: '🛍️',\n      badge: 'Sale',\n      price: '$25',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Daily Deals') }\n    },\n    {\n      id: '4',\n      title: '💎 Premium Extracts',\n      description: 'The finest concentrates available.',\n      emoji: '💎',\n      badge: 'Premium',\n      price: '$80',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Premium') }\n    },\n    {\n      id: '5',\n      title: '🍃 Organic Flower',\n      description: 'Sustainably grown, naturally potent.',\n      emoji: '🍃',\n      badge: 'Organic',\n      price: '$35',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Organic') }\n    }\n  ];\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <h2 className=\"text-3xl font-serif font-bold text-center mb-8\">🔥 What's Hot</h2>\n      <CardSlider \n        cards={sampleCards} \n        autoScroll={true}\n        slidesToShow={3}\n        autoScrollInterval={5000}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAgCe,SAAS,WAAW,EACjC,KAAK,EACL,aAAa,IAAI,EACjB,qBAAqB,IAAI,EACzB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,eAAe,CAAC,EAChB,MAAM,EAAE,EACR,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG;IAE5C,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,CAAC,aAAa,MAAM,MAAM,GAAG,cAAc;YAC3D,YAAY,OAAO,GAAG,YAAY;gBAChC,gBAAgB,CAAA,OAAS,QAAQ,WAAW,IAAI,OAAO;YACzD,GAAG;QACL;QAEA,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;QACF;IACF,GAAG;QAAC;QAAY;QAAW;QAAoB;QAAU,MAAM,MAAM;QAAE;KAAa;IAEpF,MAAM,YAAY,CAAC;QACjB,gBAAgB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;IAC9C;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAS,QAAQ,WAAW,IAAI,OAAO;IACzD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAA,OAAS,QAAQ,IAAI,WAAW,OAAO;IACzD;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAK,GAAG;QAAG;QACxC,SAAS;YAAE,SAAS;YAAG,OAAO;YAAG,GAAG;QAAE;QACtC,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;IAC9B;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAGjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,YAAY,EAAE,EAAE,CAAC;wBACjE,KAAK,GAAG,IAAI,EAAE,CAAC;oBACjB;8BAEC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,WAAU;4BACV,OAAO;gCAAE,OAAO,CAAC,KAAK,EAAE,MAAM,aAAa,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,IAAI,aAAa,GAAG,CAAC;4BAAC;sCAE9F,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,KAAK;gDACf,IAAI;gDACJ,WAAU;;;;;uDAEV,KAAK,KAAK,iBACZ,8OAAC;gDAAK,WAAU;0DAAY,KAAK,KAAK;;;;;uDACpC,KAAK,IAAI,iBACX,8OAAC;gDAAI,WAAU;0DAA6B,KAAK,IAAI;;;;;qEAErD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA2B,MAAK;oDAAe,SAAQ;8DACpE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;4CAMb,KAAK,KAAK,kBACT,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,KAAK,KAAK;;;;;;;;;;;0DAMjB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;4CAGZ,KAAK,WAAW,kBACf,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,kBACT,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;oDAId,KAAK,GAAG,kBACP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,SAAS,KAAK,GAAG,CAAC,MAAM;wDACxB,WAAU;kEAET,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;2BAnEnB,KAAK,EAAE;;;;;;;;;;;;;;;YA+EnB,cAAc,MAAM,MAAM,GAAG,8BAC5B;;kCACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAIzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;YAO5E,YAAY,MAAM,MAAM,GAAG,8BAC1B,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ,WAAW;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS,IAAM,UAAU;wBACzB,WAAW,CAAC,oDAAoD,EAC9D,UAAU,eACN,mBACA,qCACJ;wBACF,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;uBATjC;;;;;;;;;;YAgBZ,cAAc,CAAC,2BACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;oBAAK;oBACvB,SAAS;wBAAE,OAAO;oBAAO;oBACzB,YAAY;wBACV,UAAU,qBAAqB;wBAC/B,MAAM;wBACN,QAAQ;oBACV;;;;;;;;;;;;;;;;;AAMZ;AAGO,SAAS;IACd,MAAM,cAA4B;QAChC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAmB;QACxE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAmB;QACxE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAoB;QACzE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAgB;QACrE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAgB;QACrE;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAiD;;;;;;0BAC/D,8OAAC;gBACC,OAAO;gBACP,YAAY;gBACZ,cAAc;gBACd,oBAAoB;;;;;;;;;;;;AAI5B", "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/FilterableStrainCards.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useState, useMemo } from 'react';\nimport Image from 'next/image';\n\ninterface Strain {\n  id: string;\n  name: string;\n  type: 'indica' | 'sativa' | 'hybrid';\n  thc: number;\n  cbd: number;\n  price: number;\n  image?: string;\n  description: string;\n  effects: string[];\n  flavors: string[];\n  genetics?: string;\n  flowering?: string;\n  yield?: string;\n  difficulty?: 'beginner' | 'intermediate' | 'advanced';\n  inStock: boolean;\n  featured?: boolean;\n}\n\ninterface FilterableStrainCardsProps {\n  strains: Strain[];\n  showFilters?: boolean;\n  defaultFilter?: string;\n  sortBy?: 'name' | 'thc' | 'price' | 'type';\n  className?: string;\n}\n\nexport default function FilterableStrainCards({\n  strains,\n  showFilters = true,\n  defaultFilter = 'all',\n  sortBy = 'name',\n  className = ''\n}: FilterableStrainCardsProps) {\n  const [activeFilter, setActiveFilter] = useState(defaultFilter);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortOption, setSortOption] = useState(sortBy);\n\n  const filters = [\n    { id: 'all', label: 'All Strains', count: strains.length },\n    { id: 'indica', label: 'Indica', count: strains.filter(s => s.type === 'indica').length },\n    { id: 'sativa', label: 'Sativa', count: strains.filter(s => s.type === 'sativa').length },\n    { id: 'hybrid', label: 'Hybrid', count: strains.filter(s => s.type === 'hybrid').length },\n    { id: 'featured', label: 'Featured', count: strains.filter(s => s.featured).length },\n    { id: 'in-stock', label: 'In Stock', count: strains.filter(s => s.inStock).length }\n  ];\n\n  const filteredAndSortedStrains = useMemo(() => {\n    let filtered = strains.filter(strain => {\n      // Filter by category\n      const categoryMatch = \n        activeFilter === 'all' ||\n        (activeFilter === 'featured' && strain.featured) ||\n        (activeFilter === 'in-stock' && strain.inStock) ||\n        strain.type === activeFilter;\n\n      // Filter by search term\n      const searchMatch = \n        strain.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        strain.effects.some(effect => effect.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        strain.flavors.some(flavor => flavor.toLowerCase().includes(searchTerm.toLowerCase()));\n\n      return categoryMatch && searchMatch;\n    });\n\n    // Sort\n    filtered.sort((a, b) => {\n      switch (sortOption) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'thc':\n          return b.thc - a.thc;\n        case 'price':\n          return a.price - b.price;\n        case 'type':\n          return a.type.localeCompare(b.type);\n        default:\n          return 0;\n      }\n    });\n\n    return filtered;\n  }, [strains, activeFilter, searchTerm, sortOption]);\n\n  const typeColors = {\n    indica: 'from-purple-500 to-purple-600',\n    sativa: 'from-green-500 to-green-600',\n    hybrid: 'from-blue-500 to-blue-600'\n  };\n\n  const typeIcons = {\n    indica: '🌙',\n    sativa: '☀️',\n    hybrid: '⚖️'\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 20, scale: 0.9 },\n    visible: { opacity: 1, y: 0, scale: 1 },\n    hover: { y: -8, scale: 1.02 },\n    exit: { opacity: 0, y: -20, scale: 0.9 }\n  };\n\n  return (\n    <div className={className}>\n      {/* Filters and Search */}\n      {showFilters && (\n        <div className=\"mb-8 space-y-4\">\n          {/* Search Bar */}\n          <div className=\"relative max-w-md mx-auto\">\n            <input\n              type=\"text\"\n              placeholder=\"Search strains, effects, or flavors...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200\"\n            />\n            <svg className=\"absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n\n          {/* Filter Tabs */}\n          <div className=\"flex flex-wrap justify-center gap-2\">\n            {filters.map((filter) => (\n              <motion.button\n                key={filter.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setActiveFilter(filter.id)}\n                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${\n                  activeFilter === filter.id\n                    ? 'bg-primary-600 text-cream-50'\n                    : 'bg-cream-100 text-gray-700 hover:bg-primary-100'\n                }`}\n              >\n                {filter.label} ({filter.count})\n              </motion.button>\n            ))}\n          </div>\n\n          {/* Sort Options */}\n          <div className=\"flex justify-center\">\n            <select\n              value={sortOption}\n              onChange={(e) => setSortOption(e.target.value as any)}\n              className=\"px-4 py-2 border border-cream-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"name\">Sort by Name</option>\n              <option value=\"thc\">Sort by THC %</option>\n              <option value=\"price\">Sort by Price</option>\n              <option value=\"type\">Sort by Type</option>\n            </select>\n          </div>\n        </div>\n      )}\n\n      {/* Results Count */}\n      <div className=\"text-center mb-6\">\n        <p className=\"text-gray-600\">\n          Showing {filteredAndSortedStrains.length} of {strains.length} strains\n        </p>\n      </div>\n\n      {/* Strain Cards Grid */}\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\"\n      >\n        <AnimatePresence mode=\"popLayout\">\n          {filteredAndSortedStrains.map((strain) => (\n            <motion.div\n              key={strain.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              exit=\"exit\"\n              layout\n              className=\"group\"\n            >\n              <div className=\"bg-cream-50 rounded-xl shadow-soft hover:shadow-large transition-shadow duration-300 overflow-hidden\">\n                {/* Image */}\n                <div className=\"relative h-48 bg-gradient-to-br from-primary-100 to-sage-100\">\n                  {strain.image ? (\n                    <Image\n                      src={strain.image}\n                      alt={strain.name}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"flex items-center justify-center h-full\">\n                      <span className=\"text-6xl\">{typeIcons[strain.type]}</span>\n                    </div>\n                  )}\n\n                  {/* Type Badge */}\n                  <div className=\"absolute top-3 left-3\">\n                    <span className={`bg-gradient-to-r ${typeColors[strain.type]} text-white text-xs font-semibold px-2 py-1 rounded-full`}>\n                      {strain.type.toUpperCase()}\n                    </span>\n                  </div>\n\n                  {/* Stock Status */}\n                  <div className=\"absolute top-3 right-3\">\n                    <span className={`text-xs font-semibold px-2 py-1 rounded-full ${\n                      strain.inStock \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {strain.inStock ? 'In Stock' : 'Out of Stock'}\n                    </span>\n                  </div>\n\n                  {/* Featured Badge */}\n                  {strain.featured && (\n                    <div className=\"absolute bottom-3 left-3\">\n                      <span className=\"bg-gold-500 text-primary-800 text-xs font-semibold px-2 py-1 rounded-full\">\n                        ⭐ Featured\n                      </span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  {/* Name and Price */}\n                  <div className=\"flex justify-between items-start mb-3\">\n                    <h3 className=\"text-lg font-semibold text-foreground group-hover:text-primary-600 transition-colors duration-200\">\n                      {strain.name}\n                    </h3>\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      ${strain.price}\n                    </span>\n                  </div>\n\n                  {/* THC/CBD */}\n                  <div className=\"flex space-x-4 mb-3\">\n                    <div className=\"text-center\">\n                      <div className=\"text-xs text-gray-500\">THC</div>\n                      <div className=\"font-semibold text-primary-600\">{strain.thc}%</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xs text-gray-500\">CBD</div>\n                      <div className=\"font-semibold text-sage-600\">{strain.cbd}%</div>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                    {strain.description}\n                  </p>\n\n                  {/* Effects */}\n                  <div className=\"mb-4\">\n                    <div className=\"text-xs text-gray-500 mb-1\">Effects</div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {strain.effects.slice(0, 3).map((effect, index) => (\n                        <span key={index} className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded\">\n                          {effect}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    disabled={!strain.inStock}\n                    className={`w-full py-2 px-4 rounded-md font-medium transition-colors duration-200 ${\n                      strain.inStock\n                        ? 'bg-primary-600 text-cream-50 hover:bg-primary-700'\n                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                    }`}\n                  >\n                    {strain.inStock ? 'Add to Cart' : 'Out of Stock'}\n                  </motion.button>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </motion.div>\n\n      {/* No Results */}\n      {filteredAndSortedStrains.length === 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center py-12\"\n        >\n          <div className=\"text-6xl mb-4\">🔍</div>\n          <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">No strains found</h3>\n          <p className=\"text-gray-500\">Try adjusting your filters or search terms</p>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAiCe,SAAS,sBAAsB,EAC5C,OAAO,EACP,cAAc,IAAI,EAClB,gBAAgB,KAAK,EACrB,SAAS,MAAM,EACf,YAAY,EAAE,EACa;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd;YAAE,IAAI;YAAO,OAAO;YAAe,OAAO,QAAQ,MAAM;QAAC;QACzD;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;QAAC;QACxF;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;QAAC;QACxF;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;QAAC;QACxF;YAAE,IAAI;YAAY,OAAO;YAAY,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAAC;QACnF;YAAE,IAAI;YAAY,OAAO;YAAY,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;QAAC;KACnF;IAED,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,IAAI,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC5B,qBAAqB;YACrB,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,cAAc,OAAO,QAAQ,IAC9C,iBAAiB,cAAc,OAAO,OAAO,IAC9C,OAAO,IAAI,KAAK;YAElB,wBAAwB;YACxB,MAAM,cACJ,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QAClF,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAEpF,OAAO,iBAAiB;QAC1B;QAEA,OAAO;QACP,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,OAAQ;gBACN,KAAK;oBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACpC,KAAK;oBACH,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG;gBACtB,KAAK;oBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B,KAAK;oBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACpC;oBACE,OAAO;YACX;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;QAAc;QAAY;KAAW;IAElD,MAAM,aAAa;QACjB,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,YAAY;QAChB,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACxC,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,OAAO;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;YAAI,OAAO;QAAI;IACzC;IAEA,qBACE,8OAAC;QAAI,WAAW;;YAEb,6BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;gCAAiE,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACxH,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;kCAKzE,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,gBAAgB,OAAO,EAAE;gCACxC,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,OAAO,EAAE,GACtB,iCACA,mDACJ;;oCAED,OAAO,KAAK;oCAAC;oCAAG,OAAO,KAAK;oCAAC;;+BAVzB,OAAO,EAAE;;;;;;;;;;kCAgBpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAgB;wBAClB,yBAAyB,MAAM;wBAAC;wBAAK,QAAQ,MAAM;wBAAC;;;;;;;;;;;;0BAKjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;0BAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAW;4BACX,MAAK;4BACL,MAAM;4BACN,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,KAAK,iBACX,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,OAAO,KAAK;gDACjB,KAAK,OAAO,IAAI;gDAChB,IAAI;gDACJ,WAAU;;;;;qEAGZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAY,SAAS,CAAC,OAAO,IAAI,CAAC;;;;;;;;;;;0DAKtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAW,CAAC,iBAAiB,EAAE,UAAU,CAAC,OAAO,IAAI,CAAC,CAAC,wDAAwD,CAAC;8DACnH,OAAO,IAAI,CAAC,WAAW;;;;;;;;;;;0DAK5B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAW,CAAC,6CAA6C,EAC7D,OAAO,OAAO,GACV,gCACA,2BACJ;8DACC,OAAO,OAAO,GAAG,aAAa;;;;;;;;;;;4CAKlC,OAAO,QAAQ,kBACd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA4E;;;;;;;;;;;;;;;;;kDAQlG,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAK,WAAU;;4DAAqC;4DACjD,OAAO,KAAK;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,8OAAC;gEAAI,WAAU;;oEAAkC,OAAO,GAAG;oEAAC;;;;;;;;;;;;;kEAE9D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,8OAAC;gEAAI,WAAU;;oEAA+B,OAAO,GAAG;oEAAC;;;;;;;;;;;;;;;;;;;0DAK7D,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACvC,8OAAC;gEAAiB,WAAU;0EACzB;+DADQ;;;;;;;;;;;;;;;;0DAQjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,UAAU,CAAC,OAAO,OAAO;gDACzB,WAAW,CAAC,uEAAuE,EACjF,OAAO,OAAO,GACV,sDACA,gDACJ;0DAED,OAAO,OAAO,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;2BAvGnC,OAAO,EAAE;;;;;;;;;;;;;;;YAiHrB,yBAAyB,MAAM,KAAK,mBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 3623, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/MasonryGrid.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect, useRef } from 'react';\nimport Image from 'next/image';\n\ninterface MasonryItem {\n  id: string;\n  title: string;\n  description?: string;\n  image: string;\n  category?: string;\n  height?: 'small' | 'medium' | 'large';\n  featured?: boolean;\n  cta?: {\n    text: string;\n    action: () => void;\n  };\n}\n\ninterface MasonryGridProps {\n  items: MasonryItem[];\n  columns?: number;\n  gap?: number;\n  className?: string;\n}\n\nexport default function MasonryGrid({\n  items,\n  columns = 3,\n  gap = 16,\n  className = ''\n}: MasonryGridProps) {\n  const [columnHeights, setColumnHeights] = useState<number[]>([]);\n  const [itemPositions, setItemPositions] = useState<{ [key: string]: { x: number; y: number; width: number } }>({});\n  const containerRef = useRef<HTMLDivElement>(null);\n  const itemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});\n\n  const heightClasses = {\n    small: 'h-48',\n    medium: 'h-64',\n    large: 'h-80'\n  };\n\n  useEffect(() => {\n    const calculateLayout = () => {\n      if (!containerRef.current) return;\n\n      const containerWidth = containerRef.current.offsetWidth;\n      const columnWidth = (containerWidth - gap * (columns - 1)) / columns;\n      const heights = new Array(columns).fill(0);\n      const positions: { [key: string]: { x: number; y: number; width: number } } = {};\n\n      items.forEach((item) => {\n        const shortestColumnIndex = heights.indexOf(Math.min(...heights));\n        const x = shortestColumnIndex * (columnWidth + gap);\n        const y = heights[shortestColumnIndex];\n\n        // Get actual height of the item\n        const itemElement = itemRefs.current[item.id];\n        const itemHeight = itemElement ? itemElement.offsetHeight : 300; // fallback height\n\n        positions[item.id] = { x, y, width: columnWidth };\n        heights[shortestColumnIndex] += itemHeight + gap;\n      });\n\n      setColumnHeights(heights);\n      setItemPositions(positions);\n    };\n\n    // Initial calculation\n    calculateLayout();\n\n    // Recalculate on window resize\n    const handleResize = () => {\n      setTimeout(calculateLayout, 100); // Debounce\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [items, columns, gap]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 50,\n      scale: 0.8\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const hoverVariants = {\n    hover: {\n      scale: 1.02,\n      y: -5,\n      transition: {\n        duration: 0.2,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const maxHeight = Math.max(...columnHeights);\n\n  return (\n    <motion.div\n      ref={containerRef}\n      variants={containerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-50px' }}\n      className={`relative ${className}`}\n      style={{ height: maxHeight }}\n    >\n      {items.map((item, index) => {\n        const position = itemPositions[item.id];\n        if (!position) return null;\n\n        return (\n          <motion.div\n            key={item.id}\n            ref={(el) => { itemRefs.current[item.id] = el; }}\n            variants={itemVariants}\n            whileHover=\"hover\"\n            className=\"absolute group cursor-pointer\"\n            style={{\n              left: position.x,\n              top: position.y,\n              width: position.width\n            }}\n          >\n            <motion.div\n              variants={hoverVariants}\n              className=\"bg-cream-50 rounded-xl shadow-soft hover:shadow-large transition-shadow duration-300 overflow-hidden\"\n            >\n              {/* Image */}\n              <div className={`relative ${item.height ? heightClasses[item.height] : 'h-64'} overflow-hidden`}>\n                <Image\n                  src={item.image}\n                  alt={item.title}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                />\n\n                {/* Category Badge */}\n                {item.category && (\n                  <div className=\"absolute top-3 left-3\">\n                    <span className=\"bg-primary-600 text-cream-50 text-xs font-semibold px-2 py-1 rounded-full\">\n                      {item.category}\n                    </span>\n                  </div>\n                )}\n\n                {/* Featured Badge */}\n                {item.featured && (\n                  <div className=\"absolute top-3 right-3\">\n                    <span className=\"bg-gold-500 text-primary-800 text-xs font-semibold px-2 py-1 rounded-full\">\n                      ⭐ Featured\n                    </span>\n                  </div>\n                )}\n\n                {/* Hover Overlay */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n                {/* Hover Content */}\n                <div className=\"absolute inset-0 flex items-end p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <div className=\"text-white\">\n                    <h3 className=\"text-lg font-semibold mb-1\">{item.title}</h3>\n                    {item.description && (\n                      <p className=\"text-sm text-gray-200 line-clamp-2\">\n                        {item.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-4\">\n                <h3 className=\"text-lg font-semibold text-foreground mb-2 group-hover:text-primary-600 transition-colors duration-200\">\n                  {item.title}\n                </h3>\n\n                {item.description && (\n                  <p className=\"text-sm text-gray-600 mb-4 line-clamp-3\">\n                    {item.description}\n                  </p>\n                )}\n\n                {/* CTA Button */}\n                {item.cta && (\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={item.cta.action}\n                    className=\"w-full bg-primary-600 text-cream-50 py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200\"\n                  >\n                    {item.cta.text}\n                  </motion.button>\n                )}\n              </div>\n            </motion.div>\n          </motion.div>\n        );\n      })}\n    </motion.div>\n  );\n}\n\n// Example usage\nexport function ExampleMasonryGrid() {\n  const sampleItems: MasonryItem[] = [\n    {\n      id: '1',\n      title: 'Premium OG Kush',\n      description: 'Classic indica strain with earthy, pine flavors and deeply relaxing effects perfect for evening use.',\n      image: '/assets/products/og-kush.jpg',\n      category: 'Indica',\n      height: 'medium',\n      featured: true,\n      cta: { text: 'Shop Now', action: () => console.log('Shop OG Kush') }\n    },\n    {\n      id: '2',\n      title: 'Blue Dream',\n      description: 'Balanced hybrid offering creative euphoria and gentle relaxation.',\n      image: '/assets/products/blue-dream.jpg',\n      category: 'Hybrid',\n      height: 'small',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Blue Dream') }\n    },\n    {\n      id: '3',\n      title: 'Green Crack',\n      description: 'Energizing sativa perfect for daytime use and creative activities. Known for its sweet, fruity flavor profile.',\n      image: '/assets/products/green-crack.jpg',\n      category: 'Sativa',\n      height: 'large',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Green Crack') }\n    },\n    {\n      id: '4',\n      title: 'Purple Haze',\n      description: 'Legendary sativa with psychedelic effects and sweet berry flavors.',\n      image: '/assets/products/purple-haze.jpg',\n      category: 'Sativa',\n      height: 'medium',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Purple Haze') }\n    },\n    {\n      id: '5',\n      title: 'Granddaddy Purple',\n      description: 'Potent indica with grape and berry flavors, perfect for relaxation.',\n      image: '/assets/products/granddaddy-purple.jpg',\n      category: 'Indica',\n      height: 'small',\n      featured: true,\n      cta: { text: 'Shop Now', action: () => console.log('Shop GDP') }\n    },\n    {\n      id: '6',\n      title: 'Sour Diesel',\n      description: 'Fast-acting sativa with diesel-like aroma and energizing effects that promote creativity and focus.',\n      image: '/assets/products/sour-diesel.jpg',\n      category: 'Sativa',\n      height: 'large',\n      cta: { text: 'Shop Now', action: () => console.log('Shop Sour Diesel') }\n    }\n  ];\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6\">\n      <div className=\"text-center mb-12\">\n        <h2 className=\"text-3xl font-serif font-bold text-foreground mb-4\">\n          Featured Products\n        </h2>\n        <p className=\"text-lg text-gray-600\">\n          Discover our curated selection of premium cannabis strains\n        </p>\n      </div>\n      \n      <MasonryGrid items={sampleItems} columns={3} gap={20} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AA2Be,SAAS,YAAY,EAClC,KAAK,EACL,UAAU,CAAC,EACX,MAAM,EAAE,EACR,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D,CAAC;IAChH,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4C,CAAC;IAEnE,MAAM,gBAAgB;QACpB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,MAAM,iBAAiB,aAAa,OAAO,CAAC,WAAW;YACvD,MAAM,cAAc,CAAC,iBAAiB,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI;YAC7D,MAAM,UAAU,IAAI,MAAM,SAAS,IAAI,CAAC;YACxC,MAAM,YAAwE,CAAC;YAE/E,MAAM,OAAO,CAAC,CAAC;gBACb,MAAM,sBAAsB,QAAQ,OAAO,CAAC,KAAK,GAAG,IAAI;gBACxD,MAAM,IAAI,sBAAsB,CAAC,cAAc,GAAG;gBAClD,MAAM,IAAI,OAAO,CAAC,oBAAoB;gBAEtC,gCAAgC;gBAChC,MAAM,cAAc,SAAS,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC7C,MAAM,aAAa,cAAc,YAAY,YAAY,GAAG,KAAK,kBAAkB;gBAEnF,SAAS,CAAC,KAAK,EAAE,CAAC,GAAG;oBAAE;oBAAG;oBAAG,OAAO;gBAAY;gBAChD,OAAO,CAAC,oBAAoB,IAAI,aAAa;YAC/C;YAEA,iBAAiB;YACjB,iBAAiB;QACnB;QAEA,sBAAsB;QACtB;QAEA,+BAA+B;QAC/B,MAAM,eAAe;YACnB,WAAW,iBAAiB,MAAM,WAAW;QAC/C;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAO;QAAS;KAAI;IAExB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO;YACL,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,YAAY,KAAK,GAAG,IAAI;IAE9B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAQ;QACxC,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,OAAO;YAAE,QAAQ;QAAU;kBAE1B,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,WAAW,aAAa,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,UAAU,OAAO;YAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,KAAK,CAAC;oBAAS,SAAS,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG;gBAAI;gBAC/C,UAAU;gBACV,YAAW;gBACX,WAAU;gBACV,OAAO;oBACL,MAAM,SAAS,CAAC;oBAChB,KAAK,SAAS,CAAC;oBACf,OAAO,SAAS,KAAK;gBACvB;0BAEA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;;sCAGV,8OAAC;4BAAI,WAAW,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,aAAa,CAAC,KAAK,MAAM,CAAC,GAAG,OAAO,gBAAgB,CAAC;;8CAC7F,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,KAAK;oCACf,KAAK,KAAK,KAAK;oCACf,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;gCAIP,KAAK,QAAQ,kBACZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ;;;;;;;;;;;gCAMnB,KAAK,QAAQ,kBACZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA4E;;;;;;;;;;;8CAOhG,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B,KAAK,KAAK;;;;;;4CACrD,KAAK,WAAW,kBACf,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;sCAQ3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;gCAGZ,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;gCAKpB,KAAK,GAAG,kBACP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,KAAK,GAAG,CAAC,MAAM;oCACxB,WAAU;8CAET,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;;;;;;;eA/EjB,KAAK,EAAE;;;;;QAsFlB;;;;;;AAGN;AAGO,SAAS;IACd,MAAM,cAA6B;QACjC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAgB;QACrE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAmB;QACxE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAoB;QACzE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAoB;QACzE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAY;QACjE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;YACV,QAAQ;YACR,KAAK;gBAAE,MAAM;gBAAY,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAAoB;QACzE;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC;gBAAY,OAAO;gBAAa,SAAS;gBAAG,KAAK;;;;;;;;;;;;AAGxD", "debugId": null}}, {"offset": {"line": 4028, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/VerticalTimeline.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface TimelineEvent {\n  id: string;\n  date: string;\n  title: string;\n  description: string;\n  icon?: ReactNode;\n  image?: string;\n  category?: string;\n  featured?: boolean;\n}\n\ninterface VerticalTimelineProps {\n  events: TimelineEvent[];\n  animate?: boolean;\n  alternating?: boolean;\n  showImages?: boolean;\n  className?: string;\n}\n\nexport default function VerticalTimeline({\n  events,\n  animate = true,\n  alternating = true,\n  showImages = true,\n  className = ''\n}: VerticalTimelineProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const eventVariants = {\n    hidden: (isRight: boolean) => ({\n      opacity: 0,\n      x: isRight ? 50 : -50,\n      y: 30\n    }),\n    visible: {\n      opacity: 1,\n      x: 0,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const iconVariants = {\n    hidden: { scale: 0, rotate: -180 },\n    visible: {\n      scale: 1,\n      rotate: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 500,\n        damping: 30,\n        delay: 0.2\n      }\n    }\n  };\n\n  const lineVariants = {\n    hidden: { height: 0 },\n    visible: {\n      height: '100%',\n      transition: {\n        duration: 1.5,\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={animate ? containerVariants : undefined}\n      initial={animate ? 'hidden' : undefined}\n      whileInView={animate ? 'visible' : undefined}\n      viewport={{ once: true, margin: '-100px' }}\n      className={`relative ${className}`}\n    >\n      {/* Timeline Line */}\n      <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 bg-cream-300 h-full\">\n        {animate && (\n          <motion.div\n            variants={lineVariants}\n            className=\"w-full bg-primary-600 origin-top\"\n          />\n        )}\n      </div>\n\n      {/* Timeline Events */}\n      <div className=\"space-y-12\">\n        {events.map((event, index) => {\n          const isRight = alternating ? index % 2 === 1 : false;\n          const isLeft = alternating ? index % 2 === 0 : true;\n\n          return (\n            <motion.div\n              key={event.id}\n              custom={isRight}\n              variants={animate ? eventVariants : undefined}\n              className=\"relative flex items-center\"\n            >\n              {/* Left Content */}\n              {isLeft && (\n                <div className=\"w-1/2 pr-8 text-right\">\n                  <div className=\"bg-cream-50 rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300\">\n                    {/* Date */}\n                    <div className=\"text-primary-600 font-semibold text-sm mb-2\">\n                      {event.date}\n                    </div>\n\n                    {/* Category */}\n                    {event.category && (\n                      <div className=\"inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3\">\n                        {event.category}\n                      </div>\n                    )}\n\n                    {/* Title */}\n                    <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>\n                      {event.title}\n                    </h3>\n\n                    {/* Description */}\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {event.description}\n                    </p>\n\n                    {/* Image */}\n                    {showImages && event.image && (\n                      <div className=\"mt-4 rounded-lg overflow-hidden\">\n                        <img\n                          src={event.image}\n                          alt={event.title}\n                          className=\"w-full h-32 object-cover\"\n                        />\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Center Icon */}\n              <div className=\"absolute left-1/2 transform -translate-x-1/2 z-10\">\n                <motion.div\n                  variants={animate ? iconVariants : undefined}\n                  className={`w-12 h-12 rounded-full flex items-center justify-center shadow-medium ${\n                    event.featured\n                      ? 'bg-gold-500 text-primary-800'\n                      : 'bg-primary-600 text-cream-50'\n                  }`}\n                >\n                  {event.icon || (\n                    <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                </motion.div>\n\n                {/* Pulse Animation */}\n                {event.featured && animate && (\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full bg-gold-400\"\n                    animate={{\n                      scale: [1, 1.5, 1],\n                      opacity: [0.7, 0, 0.7]\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: 'easeInOut'\n                    }}\n                  />\n                )}\n              </div>\n\n              {/* Right Content */}\n              {isRight && (\n                <div className=\"w-1/2 pl-8\">\n                  <div className=\"bg-cream-50 rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300\">\n                    {/* Date */}\n                    <div className=\"text-primary-600 font-semibold text-sm mb-2\">\n                      {event.date}\n                    </div>\n\n                    {/* Category */}\n                    {event.category && (\n                      <div className=\"inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3\">\n                        {event.category}\n                      </div>\n                    )}\n\n                    {/* Title */}\n                    <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>\n                      {event.title}\n                    </h3>\n\n                    {/* Description */}\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {event.description}\n                    </p>\n\n                    {/* Image */}\n                    {showImages && event.image && (\n                      <div className=\"mt-4 rounded-lg overflow-hidden\">\n                        <img\n                          src={event.image}\n                          alt={event.title}\n                          className=\"w-full h-32 object-cover\"\n                        />\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Mobile Layout (Full Width) */}\n              <div className=\"md:hidden w-full pl-16\">\n                <div className=\"bg-cream-50 rounded-xl shadow-soft p-6\">\n                  <div className=\"text-primary-600 font-semibold text-sm mb-2\">\n                    {event.date}\n                  </div>\n                  \n                  {event.category && (\n                    <div className=\"inline-block bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full mb-3\">\n                      {event.category}\n                    </div>\n                  )}\n                  \n                  <h3 className={`text-xl font-serif font-bold text-foreground mb-3 ${event.featured ? 'text-gold-600' : ''}`}>\n                    {event.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {event.description}\n                  </p>\n\n                  {showImages && event.image && (\n                    <div className=\"mt-4 rounded-lg overflow-hidden\">\n                      <img\n                        src={event.image}\n                        alt={event.title}\n                        className=\"w-full h-32 object-cover\"\n                      />\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          );\n        })}\n      </div>\n    </motion.div>\n  );\n}\n\n// Example usage with cannabis company timeline\nexport function ExampleVerticalTimeline() {\n  const companyEvents: TimelineEvent[] = [\n    {\n      id: '1',\n      date: '2015',\n      title: 'Company Founded',\n      description: 'Apothecary Extracts was established with a vision to provide premium cannabis products to Colorado residents.',\n      category: 'Milestone',\n      featured: true,\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\" />\n        </svg>\n      )\n    },\n    {\n      id: '2',\n      date: '2017',\n      title: 'First Retail Location',\n      description: 'Opened our flagship dispensary in Colorado Springs, bringing premium cannabis directly to our community.',\n      category: 'Expansion',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 2h6v4H7V6zm8 8v2a1 1 0 01-1 1H6a1 1 0 01-1-1v-2h8z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '3',\n      date: '2019',\n      title: 'Extraction Facility Launch',\n      description: 'Launched our state-of-the-art extraction facility, enabling us to produce premium concentrates and extracts.',\n      category: 'Innovation',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '4',\n      date: '2021',\n      title: 'Organic Certification',\n      description: 'Achieved organic certification for our cultivation practices, demonstrating our commitment to sustainable, natural growing methods.',\n      category: 'Achievement',\n      featured: true,\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '5',\n      date: '2023',\n      title: 'Second Location Opening',\n      description: 'Expanded to Denver Metro area, bringing our premium products and expert service to a wider Colorado community.',\n      category: 'Expansion',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    },\n    {\n      id: '6',\n      date: '2024',\n      title: 'Innovation Lab',\n      description: 'Launched our research and development lab focused on developing new extraction techniques and product formulations.',\n      category: 'Innovation',\n      featured: true,\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fillRule=\"evenodd\" d=\"M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <div className=\"text-center mb-12\">\n        <h2 className=\"text-3xl font-serif font-bold text-foreground mb-4\">\n          Our Journey\n        </h2>\n        <p className=\"text-lg text-gray-600\">\n          Nearly a decade of innovation, growth, and commitment to excellence\n        </p>\n      </div>\n      \n      <VerticalTimeline events={companyEvents} animate={true} alternating={true} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAwBe,SAAS,iBAAiB,EACvC,MAAM,EACN,UAAU,IAAI,EACd,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,EAAE,EACQ;IACtB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ,CAAC,UAAqB,CAAC;gBAC7B,SAAS;gBACT,GAAG,UAAU,KAAK,CAAC;gBACnB,GAAG;YACL,CAAC;QACD,SAAS;YACP,SAAS;YACT,GAAG;YACH,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QACjC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,QAAQ;QAAE;QACpB,SAAS;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU,UAAU,oBAAoB;QACxC,SAAS,UAAU,WAAW;QAC9B,aAAa,UAAU,YAAY;QACnC,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAGlC,8OAAC;gBAAI,WAAU;0BACZ,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO;oBAClB,MAAM,UAAU,cAAc,QAAQ,MAAM,IAAI;oBAChD,MAAM,SAAS,cAAc,QAAQ,MAAM,IAAI;oBAE/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,QAAQ;wBACR,UAAU,UAAU,gBAAgB;wBACpC,WAAU;;4BAGT,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI;;;;;;wCAIZ,MAAM,QAAQ,kBACb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ;;;;;;sDAKnB,8OAAC;4CAAG,WAAW,CAAC,kDAAkD,EAAE,MAAM,QAAQ,GAAG,kBAAkB,IAAI;sDACxG,MAAM,KAAK;;;;;;sDAId,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;wCAInB,cAAc,MAAM,KAAK,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,MAAM,KAAK;gDAChB,KAAK,MAAM,KAAK;gDAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAStB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,UAAU,UAAU,eAAe;wCACnC,WAAW,CAAC,sEAAsE,EAChF,MAAM,QAAQ,GACV,iCACA,gCACJ;kDAED,MAAM,IAAI,kBACT,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAwI,UAAS;;;;;;;;;;;;;;;;oCAMjL,MAAM,QAAQ,IAAI,yBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;;;;;;;4BAML,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI;;;;;;wCAIZ,MAAM,QAAQ,kBACb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ;;;;;;sDAKnB,8OAAC;4CAAG,WAAW,CAAC,kDAAkD,EAAE,MAAM,QAAQ,GAAG,kBAAkB,IAAI;sDACxG,MAAM,KAAK;;;;;;sDAId,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;wCAInB,cAAc,MAAM,KAAK,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,MAAM,KAAK;gDAChB,KAAK,MAAM,KAAK;gDAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAStB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI;;;;;;wCAGZ,MAAM,QAAQ,kBACb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ;;;;;;sDAInB,8OAAC;4CAAG,WAAW,CAAC,kDAAkD,EAAE,MAAM,QAAQ,GAAG,kBAAkB,IAAI;sDACxG,MAAM,KAAK;;;;;;sDAGd,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;wCAGnB,cAAc,MAAM,KAAK,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,MAAM,KAAK;gDAChB,KAAK,MAAM,KAAK;gDAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBAjJf,MAAM,EAAE;;;;;gBAyJnB;;;;;;;;;;;;AAIR;AAGO,SAAS;IACd,MAAM,gBAAiC;QACrC;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAwH,UAAS;;;;;;;;;;;QAGlK;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAA0c,UAAS;;;;;;;;;;;QAGpf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAkiB,UAAS;;;;;;;;;;;QAG5kB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAA8F,UAAS;;;;;;;;;;;QAGxI;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAoT,UAAS;;;;;;;;;;;QAG9V;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC;gBAAiB,QAAQ;gBAAe,SAAS;gBAAM,aAAa;;;;;;;;;;;;AAG3E", "debugId": null}}, {"offset": {"line": 4603, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/HeroVideoLoop.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\n\ninterface CTAButton {\n  text: string;\n  href?: string;\n  action?: () => void;\n  variant?: 'primary' | 'secondary' | 'outline';\n}\n\ninterface HeroVideoLoopProps {\n  headline: string;\n  subheadline?: string;\n  videoSrc: string;\n  posterImage?: string;\n  ctas?: CTAButton[];\n  overlayOpacity?: number;\n  textPosition?: 'center' | 'left' | 'right';\n  showScrollIndicator?: boolean;\n  autoPlay?: boolean;\n  className?: string;\n}\n\nexport default function HeroVideoLoop({\n  headline,\n  subheadline,\n  videoSrc,\n  posterImage,\n  ctas = [],\n  overlayOpacity = 0.4,\n  textPosition = 'center',\n  showScrollIndicator = true,\n  autoPlay = true,\n  className = ''\n}: HeroVideoLoopProps) {\n  const [isVideoLoaded, setIsVideoLoaded] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    const video = videoRef.current;\n    if (video && autoPlay) {\n      video.play().catch(() => {\n        // Auto-play failed, user interaction required\n        setIsPlaying(false);\n      });\n    }\n  }, [autoPlay]);\n\n  const handleVideoLoad = () => {\n    setIsVideoLoaded(true);\n  };\n\n  const handlePlayPause = () => {\n    const video = videoRef.current;\n    if (video) {\n      if (video.paused) {\n        video.play();\n        setIsPlaying(true);\n      } else {\n        video.pause();\n        setIsPlaying(false);\n      }\n    }\n  };\n\n  const ctaVariants = {\n    primary: 'bg-gold-500 text-primary-800 hover:bg-gold-400 focus:ring-gold-400',\n    secondary: 'bg-primary-600 text-cream-50 hover:bg-primary-700 focus:ring-primary-500',\n    outline: 'border-2 border-cream-50 text-cream-50 hover:bg-cream-50 hover:text-primary-800 focus:ring-cream-50'\n  };\n\n  const textPositionClasses = {\n    center: 'text-center items-center',\n    left: 'text-left items-start',\n    right: 'text-right items-end'\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const scrollIndicatorVariants = {\n    animate: {\n      y: [0, 10, 0],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    }\n  };\n\n  return (\n    <section className={`relative h-screen overflow-hidden bg-primary-800 ${className}`}>\n      {/* Video Background */}\n      <div className=\"absolute inset-0\">\n        <video\n          ref={videoRef}\n          autoPlay={autoPlay}\n          muted\n          loop\n          playsInline\n          poster={posterImage}\n          onLoadedData={handleVideoLoad}\n          onPlay={() => setIsPlaying(true)}\n          onPause={() => setIsPlaying(false)}\n          className={`w-full h-full object-cover transition-opacity duration-1000 ${\n            isVideoLoaded ? 'opacity-100' : 'opacity-0'\n          }`}\n        >\n          <source src={videoSrc} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n\n        {/* Video Overlay */}\n        <div \n          className=\"absolute inset-0 bg-black transition-opacity duration-300\"\n          style={{ opacity: overlayOpacity }}\n        />\n\n        {/* Gradient Overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\" />\n      </div>\n\n      {/* Content */}\n      <div className={`relative z-10 h-full flex flex-col justify-center px-6 sm:px-8 lg:px-12 ${textPositionClasses[textPosition]}`}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"max-w-4xl\"\n        >\n          {/* Headline */}\n          <motion.h1\n            variants={itemVariants}\n            className=\"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-cream-50 leading-tight mb-6\"\n          >\n            {headline.split(' ').map((word, index) => (\n              <motion.span\n                key={index}\n                variants={{\n                  hidden: { opacity: 0, y: 50 },\n                  visible: {\n                    opacity: 1,\n                    y: 0,\n                    transition: {\n                      duration: 0.6,\n                      delay: index * 0.1,\n                      ease: 'easeOut'\n                    }\n                  }\n                }}\n                className=\"inline-block mr-3\"\n              >\n                {word}\n              </motion.span>\n            ))}\n          </motion.h1>\n\n          {/* Subheadline */}\n          {subheadline && (\n            <motion.p\n              variants={itemVariants}\n              className=\"text-xl sm:text-2xl md:text-3xl text-cream-200 mb-8 leading-relaxed max-w-3xl\"\n            >\n              {subheadline}\n            </motion.p>\n          )}\n\n          {/* CTA Buttons */}\n          {ctas.length > 0 && (\n            <motion.div\n              variants={itemVariants}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center sm:justify-start\"\n            >\n              {ctas.map((cta, index) => (\n                <motion.div\n                  key={index}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {cta.href ? (\n                    <Link\n                      href={cta.href}\n                      className={`inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${\n                        ctaVariants[cta.variant || 'primary']\n                      }`}\n                    >\n                      {cta.text}\n                      <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  ) : (\n                    <button\n                      onClick={cta.action}\n                      className={`inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent ${\n                        ctaVariants[cta.variant || 'primary']\n                      }`}\n                    >\n                      {cta.text}\n                      <svg className=\"ml-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </button>\n                  )}\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Video Controls */}\n      <div className=\"absolute bottom-6 left-6 z-20\">\n        <motion.button\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          onClick={handlePlayPause}\n          className=\"bg-black/50 hover:bg-black/70 text-white w-12 h-12 rounded-full flex items-center justify-center transition-colors duration-200\"\n          aria-label={isPlaying ? 'Pause video' : 'Play video'}\n        >\n          {isPlaying ? (\n            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          ) : (\n            <svg className=\"w-6 h-6 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n            </svg>\n          )}\n        </motion.button>\n      </div>\n\n      {/* Scroll Indicator */}\n      {showScrollIndicator && (\n        <motion.div\n          variants={scrollIndicatorVariants}\n          animate=\"animate\"\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\"\n        >\n          <div className=\"flex flex-col items-center text-cream-50\">\n            <span className=\"text-sm font-medium mb-2\">Scroll</span>\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 right-0 z-20\">\n        <svg viewBox=\"0 0 1440 120\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path \n            d=\"M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z\" \n            fill=\"var(--background)\"\n          />\n        </svg>\n      </div>\n    </section>\n  );\n}\n\n// Example usage\nexport function ExampleHeroVideoLoop() {\n  const ctas: CTAButton[] = [\n    {\n      text: 'Browse Products',\n      href: '/products',\n      variant: 'primary'\n    },\n    {\n      text: 'Find Your Store',\n      href: '/locations',\n      variant: 'outline'\n    },\n    {\n      text: 'Explore Extracts',\n      href: '/products?category=extracts',\n      variant: 'secondary'\n    }\n  ];\n\n  return (\n    <HeroVideoLoop\n      headline=\"Craft Cannabis. Next Level.\"\n      subheadline=\"Award-winning extracts. Rare strains. Daily deals. Let's vibe.\"\n      videoSrc=\"/assets/hero/loop-vapor.mp4\"\n      posterImage=\"/assets/hero/hero-poster.jpg\"\n      ctas={ctas}\n      overlayOpacity={0.3}\n      textPosition=\"center\"\n      showScrollIndicator={true}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AA0Be,SAAS,cAAc,EACpC,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,OAAO,EAAE,EACT,iBAAiB,GAAG,EACpB,eAAe,QAAQ,EACvB,sBAAsB,IAAI,EAC1B,WAAW,IAAI,EACf,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,SAAS,UAAU;YACrB,MAAM,IAAI,GAAG,KAAK,CAAC;gBACjB,8CAA8C;gBAC9C,aAAa;YACf;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,IAAI,MAAM,MAAM,EAAE;gBAChB,MAAM,IAAI;gBACV,aAAa;YACf,OAAO;gBACL,MAAM,KAAK;gBACX,aAAa;YACf;QACF;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,sBAAsB;QAC1B,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,0BAA0B;QAC9B,SAAS;YACP,GAAG;gBAAC;gBAAG;gBAAI;aAAE;YACb,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAC,iDAAiD,EAAE,WAAW;;0BAEjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,UAAU;wBACV,KAAK;wBACL,IAAI;wBACJ,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,QAAQ,IAAM,aAAa;wBAC3B,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,4DAA4D,EACtE,gBAAgB,gBAAgB,aAChC;;0CAEF,8OAAC;gCAAO,KAAK;gCAAU,MAAK;;;;;;4BAAc;;;;;;;kCAK5C,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;wBAAe;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAW,CAAC,wEAAwE,EAAE,mBAAmB,CAAC,aAAa,EAAE;0BAC5H,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAET,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,UAAU;wCACR,QAAQ;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC5B,SAAS;4CACP,SAAS;4CACT,GAAG;4CACH,YAAY;gDACV,UAAU;gDACV,OAAO,QAAQ;gDACf,MAAM;4CACR;wCACF;oCACF;oCACA,WAAU;8CAET;mCAfI;;;;;;;;;;wBAqBV,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET;;;;;;wBAKJ,KAAK,MAAM,GAAG,mBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,IAAI,IAAI,iBACP,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,IAAI,IAAI;wCACd,WAAW,CAAC,iMAAiM,EAC3M,WAAW,CAAC,IAAI,OAAO,IAAI,UAAU,EACrC;;4CAED,IAAI,IAAI;0DACT,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;6DAIzE,8OAAC;wCACC,SAAS,IAAI,MAAM;wCACnB,WAAW,CAAC,iMAAiM,EAC3M,WAAW,CAAC,IAAI,OAAO,IAAI,UAAU,EACrC;;4CAED,IAAI,IAAI;0DACT,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;mCAzBtE;;;;;;;;;;;;;;;;;;;;;0BAqCjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAI;oBACvB,SAAS;oBACT,WAAU;oBACV,cAAY,YAAY,gBAAgB;8BAEvC,0BACC,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAe,SAAQ;kCACnD,cAAA,8OAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAsH,UAAS;;;;;;;;;;6CAG5J,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAe,SAAQ;kCACxD,cAAA,8OAAC;4BAAK,UAAS;4BAAU,GAAE;4BAA0G,UAAS;;;;;;;;;;;;;;;;;;;;;YAOrJ,qCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA2B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,SAAQ;oBAAe,MAAK;oBAAO,OAAM;8BAC5C,cAAA,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB;AAGO,SAAS;IACd,MAAM,OAAoB;QACxB;YACE,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QACC,UAAS;QACT,aAAY;QACZ,UAAS;QACT,aAAY;QACZ,MAAM;QACN,gBAAgB;QAChB,cAAa;QACb,qBAAqB;;;;;;AAG3B", "debugId": null}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ui/ParallaxBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { useRef, ReactNode } from 'react';\nimport Image from 'next/image';\n\ninterface ParallaxBannerProps {\n  backgroundImage: string;\n  height?: string;\n  children?: ReactNode;\n  parallaxSpeed?: number;\n  overlayOpacity?: number;\n  overlayColor?: string;\n  className?: string;\n}\n\nexport default function ParallaxBanner({\n  backgroundImage,\n  height = 'h-screen',\n  children,\n  parallaxSpeed = 0.5,\n  overlayOpacity = 0.4,\n  overlayColor = 'black',\n  className = ''\n}: ParallaxBannerProps) {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: ['start end', 'end start']\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], ['0%', `${parallaxSpeed * 100}%`]);\n  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0.8, 1, 0.8]);\n\n  return (\n    <div ref={ref} className={`relative overflow-hidden ${height} ${className}`}>\n      {/* Parallax Background */}\n      <motion.div\n        style={{ y }}\n        className=\"absolute inset-0 w-full h-[120%] -top-[10%]\"\n      >\n        <Image\n          src={backgroundImage}\n          alt=\"Parallax background\"\n          fill\n          className=\"object-cover\"\n          sizes=\"100vw\"\n          priority\n        />\n      </motion.div>\n\n      {/* Overlay */}\n      <div \n        className=\"absolute inset-0\"\n        style={{ \n          backgroundColor: overlayColor,\n          opacity: overlayOpacity \n        }}\n      />\n\n      {/* Content */}\n      <motion.div\n        style={{ opacity }}\n        className=\"relative z-10 h-full flex items-center justify-center\"\n      >\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n\n// ScrollFadeIn Component\ninterface ScrollFadeInProps {\n  children: ReactNode;\n  direction?: 'up' | 'down' | 'left' | 'right';\n  distance?: number;\n  duration?: number;\n  delay?: number;\n  className?: string;\n}\n\nexport function ScrollFadeIn({\n  children,\n  direction = 'up',\n  distance = 50,\n  duration = 0.6,\n  delay = 0,\n  className = ''\n}: ScrollFadeInProps) {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: ['start 0.8', 'start 0.2']\n  });\n\n  const directionMap = {\n    up: { y: distance, x: 0 },\n    down: { y: -distance, x: 0 },\n    left: { y: 0, x: distance },\n    right: { y: 0, x: -distance }\n  };\n\n  const initialOffset = directionMap[direction];\n\n  const x = useTransform(scrollYProgress, [0, 1], [initialOffset.x, 0]);\n  const y = useTransform(scrollYProgress, [0, 1], [initialOffset.y, 0]);\n  const opacity = useTransform(scrollYProgress, [0, 1], [0, 1]);\n\n  return (\n    <motion.div\n      ref={ref}\n      style={{ x, y, opacity }}\n      transition={{ duration, delay, ease: 'easeOut' }}\n      className={className}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\n// Example usage components\nexport function ExampleParallaxBanner() {\n  return (\n    <ParallaxBanner\n      backgroundImage=\"/assets/backgrounds/cannabis-field.jpg\"\n      height=\"h-96\"\n      parallaxSpeed={0.3}\n      overlayOpacity={0.5}\n    >\n      <div className=\"text-center text-white px-6\">\n        <motion.h2\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-4xl md:text-5xl font-serif font-bold mb-4\"\n        >\n          Sustainably Grown\n        </motion.h2>\n        <motion.p\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"text-xl md:text-2xl text-cream-200 max-w-2xl mx-auto\"\n        >\n          Our organic cultivation practices ensure the highest quality cannabis while protecting the environment\n        </motion.p>\n      </div>\n    </ParallaxBanner>\n  );\n}\n\nexport function ExampleScrollFadeIn() {\n  return (\n    <div className=\"space-y-12 py-16\">\n      <ScrollFadeIn direction=\"up\" delay={0}>\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-serif font-bold text-foreground mb-4\">\n            Premium Quality\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Every product undergoes rigorous testing to ensure purity, potency, and safety.\n          </p>\n        </div>\n      </ScrollFadeIn>\n\n      <ScrollFadeIn direction=\"left\" delay={0.2}>\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-8 h-8 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n          <div>\n            <h3 className=\"text-xl font-semibold text-foreground mb-2\">Lab Tested</h3>\n            <p className=\"text-gray-600\">Comprehensive testing for cannabinoids, terpenes, and contaminants.</p>\n          </div>\n        </div>\n      </ScrollFadeIn>\n\n      <ScrollFadeIn direction=\"right\" delay={0.4}>\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"w-16 h-16 bg-sage-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-8 h-8 text-sage-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n          <div>\n            <h3 className=\"text-xl font-semibold text-foreground mb-2\">Organic Certified</h3>\n            <p className=\"text-gray-600\">Sustainably grown using organic farming practices.</p>\n          </div>\n        </div>\n      </ScrollFadeIn>\n    </div>\n  );\n}\n\n// Combined Parallax Section with ScrollFadeIn elements\nexport function ParallaxSection() {\n  return (\n    <div className=\"space-y-0\">\n      {/* First parallax banner */}\n      <ParallaxBanner\n        backgroundImage=\"/assets/backgrounds/extraction-lab.jpg\"\n        height=\"h-96\"\n        parallaxSpeed={0.4}\n        overlayOpacity={0.6}\n      >\n        <ScrollFadeIn direction=\"up\">\n          <div className=\"text-center text-white px-6\">\n            <h2 className=\"text-4xl md:text-5xl font-serif font-bold mb-4\">\n              State-of-the-Art Extraction\n            </h2>\n            <p className=\"text-xl text-cream-200 max-w-2xl mx-auto\">\n              Advanced CO2 extraction techniques preserve the full spectrum of cannabinoids and terpenes\n            </p>\n          </div>\n        </ScrollFadeIn>\n      </ParallaxBanner>\n\n      {/* Content section with scroll animations */}\n      <section className=\"py-16 bg-cream-50\">\n        <div className=\"max-w-6xl mx-auto px-6\">\n          <ExampleScrollFadeIn />\n        </div>\n      </section>\n\n      {/* Second parallax banner */}\n      <ParallaxBanner\n        backgroundImage=\"/assets/backgrounds/dispensary-interior.jpg\"\n        height=\"h-80\"\n        parallaxSpeed={0.3}\n        overlayOpacity={0.5}\n      >\n        <ScrollFadeIn direction=\"down\">\n          <div className=\"text-center text-white px-6\">\n            <h2 className=\"text-3xl md:text-4xl font-serif font-bold mb-4\">\n              Expert Guidance\n            </h2>\n            <p className=\"text-lg text-cream-200 max-w-xl mx-auto\">\n              Our knowledgeable budtenders help you find the perfect products for your needs\n            </p>\n          </div>\n        </ScrollFadeIn>\n      </ParallaxBanner>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAgBe,SAAS,eAAe,EACrC,eAAe,EACf,SAAS,UAAU,EACnB,QAAQ,EACR,gBAAgB,GAAG,EACnB,iBAAiB,GAAG,EACpB,eAAe,OAAO,EACtB,YAAY,EAAE,EACM;IACpB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM,GAAG,gBAAgB,IAAI,CAAC,CAAC;KAAC;IACjF,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;KAAE,EAAE;QAAC;QAAK;QAAG;KAAI;IAExE,qBACE,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAC,yBAAyB,EAAE,OAAO,CAAC,EAAE,WAAW;;0BAEzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE;gBAAE;gBACX,WAAU;0BAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,OAAM;oBACN,QAAQ;;;;;;;;;;;0BAKZ,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,SAAS;gBACX;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE;gBAAQ;gBACjB,WAAU;0BAET;;;;;;;;;;;;AAIT;AAYO,SAAS,aAAa,EAC3B,QAAQ,EACR,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,YAAY,EAAE,EACI;IAClB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,eAAe;QACnB,IAAI;YAAE,GAAG;YAAU,GAAG;QAAE;QACxB,MAAM;YAAE,GAAG,CAAC;YAAU,GAAG;QAAE;QAC3B,MAAM;YAAE,GAAG;YAAG,GAAG;QAAS;QAC1B,OAAO;YAAE,GAAG;YAAG,GAAG,CAAC;QAAS;IAC9B;IAEA,MAAM,gBAAgB,YAAY,CAAC,UAAU;IAE7C,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC,cAAc,CAAC;QAAE;KAAE;IACpE,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC,cAAc,CAAC;QAAE;KAAE;IACpE,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG;KAAE;IAE5D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,OAAO;YAAE;YAAG;YAAG;QAAQ;QACvB,YAAY;YAAE;YAAU;YAAO,MAAM;QAAU;QAC/C,WAAW;kBAEV;;;;;;AAGP;AAGO,SAAS;IACd,qBACE,8OAAC;QACC,iBAAgB;QAChB,QAAO;QACP,eAAe;QACf,gBAAgB;kBAEhB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BACX;;;;;;8BAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAa,WAAU;gBAAK,OAAO;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAa,WAAU;gBAAO,OAAO;0BACpC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAA2B,MAAK;gCAAe,SAAQ;0CACpE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0c,UAAS;;;;;;;;;;;;;;;;sCAGlf,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAKnC,8OAAC;gBAAa,WAAU;gBAAQ,OAAO;0BACrC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAkiB,UAAS;;;;;;;;;;;;;;;;sCAG1kB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,iBAAgB;gBAChB,QAAO;gBACP,eAAe;gBACf,gBAAgB;0BAEhB,cAAA,8OAAC;oBAAa,WAAU;8BACtB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;;;;;;;;;;;;;;0BAKL,8OAAC;gBACC,iBAAgB;gBAChB,QAAO;gBACP,eAAe;gBACf,gBAAgB;0BAEhB,cAAA,8OAAC;oBAAa,WAAU;8BACtB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE", "debugId": null}}, {"offset": {"line": 5588, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/data/sampleStrains.ts"], "sourcesContent": ["export interface Strain {\n  id: string;\n  name: string;\n  type: 'indica' | 'sativa' | 'hybrid';\n  thc: number;\n  cbd: number;\n  price: number;\n  image?: string;\n  description: string;\n  effects: string[];\n  flavors: string[];\n  genetics?: string;\n  flowering?: string;\n  yield?: string;\n  difficulty?: 'beginner' | 'intermediate' | 'advanced';\n  inStock: boolean;\n  featured?: boolean;\n}\n\nexport const sampleStrains: Strain[] = [\n  {\n    id: '1',\n    name: 'O<PERSON>',\n    type: 'indica',\n    thc: 28,\n    cbd: 0.5,\n    price: 45,\n    image: '/assets/strains/og-kush.jpg',\n    description: 'A legendary indica strain with earthy, pine flavors and deeply relaxing effects. Perfect for evening use and stress relief.',\n    effects: ['Relaxed', 'Happy', 'Sleepy', 'Euphoric'],\n    flavors: ['Earthy', 'Pine', 'Woody', 'Citrus'],\n    genetics: 'Chemdawg x Lemon Thai x Pakistani Kush',\n    flowering: '8-9 weeks',\n    yield: 'Medium',\n    difficulty: 'intermediate',\n    inStock: true,\n    featured: true\n  },\n  {\n    id: '2',\n    name: 'Blue Dream',\n    type: 'hybrid',\n    thc: 24,\n    cbd: 1.2,\n    price: 40,\n    image: '/assets/strains/blue-dream.jpg',\n    description: 'A balanced hybrid offering creative euphoria and gentle relaxation. Great for daytime use and social activities.',\n    effects: ['Creative', 'Uplifted', 'Relaxed', 'Focused'],\n    flavors: ['Berry', 'Sweet', 'Vanilla', 'Herbal'],\n    genetics: 'Blueberry x Haze',\n    flowering: '9-10 weeks',\n    yield: 'High',\n    difficulty: 'beginner',\n    inStock: true,\n    featured: false\n  },\n  {\n    id: '3',\n    name: 'Green Crack',\n    type: 'sativa',\n    thc: 26,\n    cbd: 0.3,\n    price: 42,\n    image: '/assets/strains/green-crack.jpg',\n    description: 'Energizing sativa perfect for daytime use and creative activities. Known for its sweet, fruity flavor profile.',\n    effects: ['Energetic', 'Focused', 'Creative', 'Uplifted'],\n    flavors: ['Sweet', 'Fruity', 'Citrus', 'Tropical'],\n    genetics: 'Skunk #1 x Unknown Indica',\n    flowering: '7-9 weeks',\n    yield: 'Medium-High',\n    difficulty: 'intermediate',\n    inStock: true,\n    featured: true\n  },\n  {\n    id: '4',\n    name: 'Purple Haze',\n    type: 'sativa',\n    thc: 22,\n    cbd: 0.8,\n    price: 38,\n    image: '/assets/strains/purple-haze.jpg',\n    description: 'Legendary sativa with psychedelic effects and sweet berry flavors. A classic strain with uplifting properties.',\n    effects: ['Euphoric', 'Creative', 'Energetic', 'Happy'],\n    flavors: ['Berry', 'Sweet', 'Grape', 'Floral'],\n    genetics: 'Purple Thai x Haze',\n    flowering: '9-11 weeks',\n    yield: 'Medium',\n    difficulty: 'advanced',\n    inStock: false,\n    featured: false\n  },\n  {\n    id: '5',\n    name: 'Granddaddy Purple',\n    type: 'indica',\n    thc: 25,\n    cbd: 0.7,\n    price: 43,\n    image: '/assets/strains/granddaddy-purple.jpg',\n    description: 'Potent indica with grape and berry flavors, perfect for relaxation and pain relief. Ideal for evening use.',\n    effects: ['Relaxed', 'Sleepy', 'Happy', 'Euphoric'],\n    flavors: ['Grape', 'Berry', 'Sweet', 'Earthy'],\n    genetics: 'Big Bud x Purple Urkle',\n    flowering: '8-11 weeks',\n    yield: 'High',\n    difficulty: 'beginner',\n    inStock: true,\n    featured: true\n  },\n  {\n    id: '6',\n    name: 'Sour Diesel',\n    type: 'sativa',\n    thc: 27,\n    cbd: 0.4,\n    price: 44,\n    image: '/assets/strains/sour-diesel.jpg',\n    description: 'Fast-acting sativa with diesel-like aroma and energizing effects that promote creativity and focus.',\n    effects: ['Energetic', 'Creative', 'Uplifted', 'Focused'],\n    flavors: ['Diesel', 'Pungent', 'Citrus', 'Herbal'],\n    genetics: 'Chemdawg 91 x Super Skunk',\n    flowering: '10-11 weeks',\n    yield: 'Medium',\n    difficulty: 'intermediate',\n    inStock: true,\n    featured: false\n  },\n  {\n    id: '7',\n    name: 'White Widow',\n    type: 'hybrid',\n    thc: 23,\n    cbd: 1.0,\n    price: 41,\n    image: '/assets/strains/white-widow.jpg',\n    description: 'Balanced hybrid known for its resin production and euphoric effects. Great for both recreational and medicinal use.',\n    effects: ['Euphoric', 'Relaxed', 'Creative', 'Happy'],\n    flavors: ['Earthy', 'Woody', 'Spicy', 'Pine'],\n    genetics: 'Brazilian x South Indian',\n    flowering: '8-9 weeks',\n    yield: 'High',\n    difficulty: 'beginner',\n    inStock: true,\n    featured: false\n  },\n  {\n    id: '8',\n    name: 'Girl Scout Cookies',\n    type: 'hybrid',\n    thc: 29,\n    cbd: 0.6,\n    price: 48,\n    image: '/assets/strains/girl-scout-cookies.jpg',\n    description: 'Potent hybrid with sweet and earthy flavors. Provides full-body relaxation with cerebral euphoria.',\n    effects: ['Euphoric', 'Relaxed', 'Happy', 'Creative'],\n    flavors: ['Sweet', 'Earthy', 'Mint', 'Cherry'],\n    genetics: 'OG Kush x Durban Poison',\n    flowering: '9-10 weeks',\n    yield: 'Medium',\n    difficulty: 'advanced',\n    inStock: true,\n    featured: true\n  },\n  {\n    id: '9',\n    name: 'Jack Herer',\n    type: 'sativa',\n    thc: 24,\n    cbd: 0.9,\n    price: 39,\n    image: '/assets/strains/jack-herer.jpg',\n    description: 'Award-winning sativa named after the cannabis activist. Provides clear-headed and creative effects.',\n    effects: ['Creative', 'Energetic', 'Focused', 'Uplifted'],\n    flavors: ['Pine', 'Spicy', 'Woody', 'Earthy'],\n    genetics: 'Haze x Northern Lights #5 x Shiva Skunk',\n    flowering: '8-10 weeks',\n    yield: 'Medium-High',\n    difficulty: 'intermediate',\n    inStock: false,\n    featured: false\n  },\n  {\n    id: '10',\n    name: 'Northern Lights',\n    type: 'indica',\n    thc: 21,\n    cbd: 1.5,\n    price: 36,\n    image: '/assets/strains/northern-lights.jpg',\n    description: 'Classic indica strain known for its fast flowering and resilient growth. Provides deep relaxation and pain relief.',\n    effects: ['Relaxed', 'Sleepy', 'Happy', 'Euphoric'],\n    flavors: ['Sweet', 'Spicy', 'Earthy', 'Pine'],\n    genetics: 'Afghani x Thai',\n    flowering: '6-7 weeks',\n    yield: 'High',\n    difficulty: 'beginner',\n    inStock: true,\n    featured: false\n  },\n  {\n    id: '11',\n    name: 'Pineapple Express',\n    type: 'hybrid',\n    thc: 26,\n    cbd: 0.8,\n    price: 46,\n    image: '/assets/strains/pineapple-express.jpg',\n    description: 'Tropical hybrid with pineapple and citrus flavors. Provides energetic and creative effects with mild relaxation.',\n    effects: ['Energetic', 'Creative', 'Happy', 'Focused'],\n    flavors: ['Pineapple', 'Citrus', 'Sweet', 'Tropical'],\n    genetics: 'Trainwreck x Hawaiian',\n    flowering: '7-8 weeks',\n    yield: 'Medium',\n    difficulty: 'intermediate',\n    inStock: true,\n    featured: true\n  },\n  {\n    id: '12',\n    name: 'Gorilla Glue #4',\n    type: 'hybrid',\n    thc: 30,\n    cbd: 0.5,\n    price: 50,\n    image: '/assets/strains/gorilla-glue-4.jpg',\n    description: 'Extremely potent hybrid known for its sticky resin production. Provides heavy-handed euphoria and relaxation.',\n    effects: ['Euphoric', 'Relaxed', 'Happy', 'Sleepy'],\n    flavors: ['Earthy', 'Sour', 'Pine', 'Diesel'],\n    genetics: 'Chem\\'s Sister x Sour Dubb x Chocolate Diesel',\n    flowering: '8-9 weeks',\n    yield: 'High',\n    difficulty: 'advanced',\n    inStock: true,\n    featured: true\n  }\n];\n\nexport default sampleStrains;\n"], "names": [], "mappings": ";;;;AAmBO,MAAM,gBAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAW;YAAS;YAAU;SAAW;QACnD,SAAS;YAAC;YAAU;YAAQ;YAAS;SAAS;QAC9C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAY;YAAW;SAAU;QACvD,SAAS;YAAC;YAAS;YAAS;YAAW;SAAS;QAChD,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAa;YAAW;YAAY;SAAW;QACzD,SAAS;YAAC;YAAS;YAAU;YAAU;SAAW;QAClD,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAY;YAAa;SAAQ;QACvD,SAAS;YAAC;YAAS;YAAS;YAAS;SAAS;QAC9C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAW;YAAU;YAAS;SAAW;QACnD,SAAS;YAAC;YAAS;YAAS;YAAS;SAAS;QAC9C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAa;YAAY;YAAY;SAAU;QACzD,SAAS;YAAC;YAAU;YAAW;YAAU;SAAS;QAClD,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAW;YAAY;SAAQ;QACrD,SAAS;YAAC;YAAU;YAAS;YAAS;SAAO;QAC7C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAW;YAAS;SAAW;QACrD,SAAS;YAAC;YAAS;YAAU;YAAQ;SAAS;QAC9C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAa;YAAW;SAAW;QACzD,SAAS;YAAC;YAAQ;YAAS;YAAS;SAAS;QAC7C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAW;YAAU;YAAS;SAAW;QACnD,SAAS;YAAC;YAAS;YAAS;YAAU;SAAO;QAC7C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAa;YAAY;YAAS;SAAU;QACtD,SAAS;YAAC;YAAa;YAAU;YAAS;SAAW;QACrD,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;YAAC;YAAY;YAAW;YAAS;SAAS;QACnD,SAAS;YAAC;YAAU;YAAQ;YAAQ;SAAS;QAC7C,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,SAAS;QACT,UAAU;IACZ;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 5937, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/app/components-demo/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Navigation from '@/components/Navigation';\nimport AgeVerification from '@/components/AgeVerification';\n\n// Import all our new components\nimport TextWithGlitchEffect from '@/components/ui/TextWithGlitchEffect';\nimport AnnouncementBar from '@/components/ui/AnnouncementBar';\nimport { ExampleFeatureIcons } from '@/components/ui/FeatureIcons';\nimport { ExampleTabbedGrid } from '@/components/ui/Tabs';\nimport { ExampleGridCards } from '@/components/ui/GridCards';\nimport { ExampleSplitMediaText } from '@/components/ui/SplitMediaText';\nimport { ExampleFAQAccordion } from '@/components/ui/FAQAccordion';\nimport { ExampleCardSlider } from '@/components/ui/CardSlider';\nimport FilterableStrainCards from '@/components/ui/FilterableStrainCards';\nimport { ExampleMasonryGrid } from '@/components/ui/MasonryGrid';\nimport { ExampleVerticalTimeline } from '@/components/ui/VerticalTimeline';\nimport { ExampleHeroVideoLoop } from '@/components/ui/HeroVideoLoop';\nimport { ParallaxSection } from '@/components/ui/ParallaxBanner';\nimport { sampleStrains } from '@/data/sampleStrains';\n\nexport default function ComponentsDemo() {\n  const [isAgeVerified, setIsAgeVerified] = useState(false);\n\n  return (\n    <>\n      {!isAgeVerified && (\n        <AgeVerification onVerified={() => setIsAgeVerified(true)} />\n      )}\n\n      {isAgeVerified && (\n        <div className=\"min-h-screen bg-cream-100\">\n          {/* Announcement Bar */}\n          <AnnouncementBar\n            message=\"🎉 New UI Components Demo! Explore our latest interactive elements and animations.\"\n            ctaText=\"Learn More\"\n            ctaLink=\"#components\"\n            type=\"promotion\"\n            dismissible={true}\n          />\n\n          <Navigation />\n\n          <main>\n            {/* Hero Video Loop Section */}\n            <section id=\"hero\" className=\"mb-16\">\n              <ExampleHeroVideoLoop />\n            </section>\n\n            {/* Components Showcase */}\n            <section id=\"components\" className=\"py-16\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <div className=\"text-center mb-16\">\n                  <h1 className=\"text-4xl sm:text-5xl font-serif font-bold text-foreground mb-4\">\n                    <TextWithGlitchEffect \n                      text=\"UI Components Demo\"\n                      glitchIntensity=\"medium\"\n                      triggerOnHover={true}\n                      className=\"text-primary-600\"\n                    />\n                  </h1>\n                  <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                    Explore our comprehensive collection of interactive React components designed for modern cannabis businesses.\n                  </p>\n                </div>\n\n                {/* Feature Icons */}\n                <div className=\"mb-20\">\n                  <h2 className=\"text-3xl font-serif font-bold text-center mb-8\">Feature Icons</h2>\n                  <ExampleFeatureIcons />\n                </div>\n\n                {/* Card Slider */}\n                <div className=\"mb-20\">\n                  <ExampleCardSlider />\n                </div>\n\n                {/* Tabbed Grid */}\n                <div className=\"mb-20\">\n                  <ExampleTabbedGrid />\n                </div>\n\n                {/* Grid Cards */}\n                <div className=\"mb-20\">\n                  <ExampleGridCards />\n                </div>\n\n                {/* Filterable Strain Cards */}\n                <div className=\"mb-20\">\n                  <h2 className=\"text-3xl font-serif font-bold text-center mb-8\">Filterable Strain Cards</h2>\n                  <FilterableStrainCards\n                    strains={sampleStrains}\n                    showFilters={true}\n                    defaultFilter=\"all\"\n                    sortBy=\"name\"\n                  />\n                </div>\n\n                {/* Split Media Text */}\n                <div className=\"mb-20\">\n                  <ExampleSplitMediaText />\n                </div>\n\n                {/* Masonry Grid */}\n                <div className=\"mb-20\">\n                  <ExampleMasonryGrid />\n                </div>\n\n                {/* FAQ Accordion */}\n                <div className=\"mb-20\">\n                  <ExampleFAQAccordion />\n                </div>\n\n                {/* Timeline */}\n                <div className=\"mb-20\">\n                  <ExampleVerticalTimeline />\n                </div>\n              </div>\n            </section>\n\n            {/* Parallax Section */}\n            <ParallaxSection />\n\n            {/* Component Features Overview */}\n            <section className=\"py-16 bg-cream-50\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <div className=\"text-center mb-12\">\n                  <h2 className=\"text-3xl font-serif font-bold text-foreground mb-4\">\n                    Component Features\n                  </h2>\n                  <p className=\"text-lg text-gray-600\">\n                    Built with modern technologies and best practices\n                  </p>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">Framer Motion</h3>\n                    <p className=\"text-gray-600\">Smooth animations and micro-interactions powered by Framer Motion.</p>\n                  </div>\n\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-sage-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">TypeScript</h3>\n                    <p className=\"text-gray-600\">Fully typed components with excellent developer experience.</p>\n                  </div>\n\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-gold-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">Responsive</h3>\n                    <p className=\"text-gray-600\">Mobile-first design that works perfectly on all devices.</p>\n                  </div>\n\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-primary-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">Customizable</h3>\n                    <p className=\"text-gray-600\">Highly configurable with props for different use cases.</p>\n                  </div>\n\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-sage-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-sage-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">Accessible</h3>\n                    <p className=\"text-gray-600\">Built with accessibility in mind, following WCAG guidelines.</p>\n                  </div>\n\n                  <div className=\"bg-cream-50 p-6 rounded-xl shadow-soft\">\n                    <div className=\"w-12 h-12 bg-gold-100 rounded-lg flex items-center justify-center mb-4\">\n                      <svg className=\"w-6 h-6 text-gold-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-foreground mb-2\">Performance</h3>\n                    <p className=\"text-gray-600\">Optimized for performance with lazy loading and efficient animations.</p>\n                  </div>\n                </div>\n              </div>\n            </section>\n\n            {/* Call to Action */}\n            <section className=\"py-16 bg-primary-600 text-cream-50\">\n              <div className=\"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\">\n                <h2 className=\"text-3xl font-serif font-bold mb-4\">\n                  Ready to Implement These Components?\n                </h2>\n                <p className=\"text-xl text-cream-200 mb-8\">\n                  All components are production-ready and can be easily integrated into your cannabis business website.\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <button className=\"bg-gold-500 text-primary-800 px-8 py-3 rounded-lg font-semibold hover:bg-gold-400 transition-colors duration-200\">\n                    View Documentation\n                  </button>\n                  <button className=\"border-2 border-cream-50 text-cream-50 px-8 py-3 rounded-lg font-semibold hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200\">\n                    Download Components\n                  </button>\n                </div>\n              </div>\n            </section>\n          </main>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE;;YACG,CAAC,+BACA,8OAAC,qIAAA,CAAA,UAAe;gBAAC,YAAY,IAAM,iBAAiB;;;;;;YAGrD,+BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,2IAAA,CAAA,UAAe;wBACd,SAAQ;wBACR,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,aAAa;;;;;;kCAGf,8OAAC,gIAAA,CAAA,UAAU;;;;;kCAEX,8OAAC;;0CAEC,8OAAC;gCAAQ,IAAG;gCAAO,WAAU;0CAC3B,cAAA,8OAAC,yIAAA,CAAA,uBAAoB;;;;;;;;;;0CAIvB,8OAAC;gCAAQ,IAAG;gCAAa,WAAU;0CACjC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,gJAAA,CAAA,UAAoB;wDACnB,MAAK;wDACL,iBAAgB;wDAChB,gBAAgB;wDAChB,WAAU;;;;;;;;;;;8DAGd,8OAAC;oDAAE,WAAU;8DAA0C;;;;;;;;;;;;sDAMzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiD;;;;;;8DAC/D,8OAAC,wIAAA,CAAA,sBAAmB;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gIAAA,CAAA,oBAAiB;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qIAAA,CAAA,mBAAgB;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAiD;;;;;;8DAC/D,8OAAC,iJAAA,CAAA,UAAqB;oDACpB,SAAS,4HAAA,CAAA,gBAAa;oDACtB,aAAa;oDACb,eAAc;oDACd,QAAO;;;;;;;;;;;;sDAKX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0IAAA,CAAA,wBAAqB;;;;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;;;;;;;;;;sDAIrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wIAAA,CAAA,sBAAmB;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4IAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,0IAAA,CAAA,kBAAe;;;;;0CAGhB,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqD;;;;;;8DAGnE,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA2B,MAAK;gEAAe,SAAQ;0EACpE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;sEAGZ,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAoT,UAAS;;;;;;;;;;;;;;;;sEAG5V,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA+L,UAAS;;;;;;;;;;;;;;;;sEAGvO,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA2B,MAAK;gEAAe,SAAQ;0EACpE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA+f,UAAS;;;;;;;;;;;;;;;;sEAGviB,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAkiB,UAAS;;;;;;;;;;;;;;;;sEAG1kB,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAG/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqG,UAAS;;;;;;;;;;;;;;;;sEAG7I,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOrC,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;sDAG3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;8DAAmH;;;;;;8DAGrI,8OAAC;oDAAO,WAAU;8DAAoJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxL", "debugId": null}}]}