'use client';

import React from 'react';
import ComponentDemo from './ComponentDemo';
import ScrollTriggeredFadeIn from '../ScrollTriggeredFadeIn';
import ImageZoomOnHover from '../ImageZoomOnHover';
import SectionRevealWipe from '../SectionRevealWipe';
import NoiseOverlayGrain from '../NoiseOverlayGrain';
import DynamicGradientText from '../DynamicGradientText';
import Tilt3DCardEffect from '../Tilt3DCardEffect';
import TypewriterIntroText from '../TypewriterIntroText';
import ParticleCanvasBackground from '../ParticleCanvasBackground';
import LottieIconAnimations from '../LottieIconAnimations';
import MorphingBlobHero from '../MorphingBlobHero';
import ClientWrapper from '../ClientWrapper';

/**
 * Visual Components Showcase
 * 
 * Comprehensive demo page showcasing all Apothecary Farms visual components
 */
const VisualComponentsShowcase: React.FC = () => {
  return (
    <div className="visual-components-showcase max-w-6xl mx-auto p-8">
      {/* Header */}
      <div className="showcase-header text-center mb-12">
        <h1 className="text-5xl font-serif font-bold text-charcoal-800 mb-4">
          Apothecary Farms Visual Components
        </h1>
        <p className="text-xl text-charcoal-600 max-w-3xl mx-auto">
          A production-ready component library featuring immersive animations and interactions 
          infused with cannabis industry aesthetics and modern web technologies.
        </p>
      </div>

      {/* ScrollTriggeredFadeIn Demo */}
      <ComponentDemo
        name="ScrollTriggeredFadeIn"
        description="Animates elements to rise and fade in when they enter the viewport with mint-green glow effects."
        component={
          <ScrollTriggeredFadeIn>
            <div className="bg-primary-100 p-6 rounded-lg text-center">
              <h4 className="text-lg font-semibold text-primary-800">
                Scroll-triggered content
              </h4>
              <p className="text-primary-600">This card fades in with a gentle rise animation</p>
            </div>
          </ScrollTriggeredFadeIn>
        }
        codeExample={`<ScrollTriggeredFadeIn>
  <div className="card">Content that fades in on scroll</div>
</ScrollTriggeredFadeIn>`}
        propsDoc={{
          delay: 'Animation delay in ms (default: 0)',
          duration: 'Animation duration in ms (default: 500)',
          translateY: 'Distance to translate from in px (default: 40)',
          threshold: 'Intersection observer threshold (default: 0.1)',
          once: 'Animate only once (default: true)',
        }}
      />

      {/* ImageZoomOnHover Demo */}
      <ComponentDemo
        name="ImageZoomOnHover"
        description="Makes product imagery feel tactile and reactive with scale and brightness effects on hover."
        component={
          <div className="flex justify-center">
            <div className="w-48 h-48 relative">
              <ImageZoomOnHover
                src="/api/placeholder/300/300"
                alt="Cannabis Product"
                addBrandShadow={true}
                shadowTheme="apothecary"
              />
            </div>
          </div>
        }
        codeExample={`<ImageZoomOnHover 
  src="/products/rosin.jpg" 
  alt="Premium Rosin Extract"
  addBrandShadow={true}
  shadowTheme="apothecary"
/>`}
        propsDoc={{
          scale: 'Scale factor on hover (default: 1.07)',
          brightness: 'Brightness factor on hover (default: 1.1)',
          addBrandShadow: 'Add brand-themed shadow (default: true)',
          shadowTheme: 'Shadow color theme: apothecary | gold | sage',
        }}
      />

      {/* SectionRevealWipe Demo */}
      <ComponentDemo
        name="SectionRevealWipe"
        description="Delivers editorial polish with side-entrance curtain wipe effects using clip-path animations."
        component={
          <SectionRevealWipe 
            direction="left-to-right" 
            gradientTheme="cannabis"
            addEdgeAccents={true}
          >
            <div className="text-center py-8 text-white">
              <h4 className="text-2xl font-bold mb-2">Hero Section</h4>
              <p>Content revealed with curtain wipe effect</p>
            </div>
          </SectionRevealWipe>
        }
        codeExample={`<SectionRevealWipe 
  direction="left-to-right" 
  gradientTheme="cannabis"
  addEdgeAccents={true}
>
  <h2>Hero Section Content</h2>
</SectionRevealWipe>`}
        propsDoc={{
          direction: 'Wipe direction: left-to-right | right-to-left | top-to-bottom | bottom-to-top',
          gradientTheme: 'Background gradient: cannabis | gold | sage | hero',
          addEdgeAccents: 'Add light edge accents (default: true)',
          trigger: 'Animation trigger: scroll | load | click | manual',
        }}
      />

      {/* NoiseOverlayGrain Demo */}
      <ComponentDemo
        name="NoiseOverlayGrain"
        description="Adds subtle texture and analog warmth to backgrounds with animated film-grain effects."
        component={
          <div className="relative bg-primary-800 rounded-lg h-32 overflow-hidden">
            <ClientWrapper>
              <NoiseOverlayGrain opacity={0.1} tintColor="#2FB886" />
            </ClientWrapper>
            <div className="relative z-10 flex items-center justify-center h-full text-white">
              <p>Background with animated grain texture</p>
            </div>
          </div>
        }
        codeExample={`<div className="hero-section relative">
  <NoiseOverlayGrain opacity={0.05} tintColor="#2FB886" />
  <h1>Hero Content</h1>
</div>`}
        propsDoc={{
          opacity: 'Grain opacity 0-1 (default: 0.04)',
          blendMode: 'CSS blend mode (default: overlay)',
          tintColor: 'Grain tint color (default: #2FB886)',
          grainSize: 'Grain pattern size: small | medium | large',
        }}
      />

      {/* DynamicGradientText Demo */}
      <ComponentDemo
        name="DynamicGradientText"
        description="Draws attention to headlines using animated gradient fills with terpene-themed colors."
        component={
          <div className="text-center">
            <DynamicGradientText 
              text="Premium Cannabis Extracts" 
              theme="rosin"
              fontSize="2.5rem"
              animate={true}
            />
          </div>
        }
        codeExample={`<DynamicGradientText 
  text="Premium Cannabis Extracts" 
  theme="rosin"
  fontSize="3rem"
  animate={true}
/>`}
        propsDoc={{
          theme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',
          customGradient: 'Custom CSS gradient string',
          animationSpeed: 'Animation speed in ms (default: 3000)',
          animate: 'Enable gradient animation (default: true)',
        }}
      />

      {/* Tilt3DCardEffect Demo */}
      <ComponentDemo
        name="Tilt3DCardEffect"
        description="Increases interactivity and realism for cards with 3D tilt effects based on mouse position."
        component={
          <div className="flex justify-center">
            <Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>
              <div className="bg-gradient-to-br from-gold-200 to-gold-400 p-6 rounded-lg w-64 text-center">
                <h4 className="text-lg font-semibold text-charcoal-800 mb-2">
                  Blue Dream
                </h4>
                <p className="text-charcoal-600">Premium Sativa Hybrid</p>
                <div className="mt-4 text-2xl">🌿</div>
              </div>
            </Tilt3DCardEffect>
          </div>
        }
        codeExample={`<Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>
  <div className="strain-card">
    <h3>Blue Dream</h3>
    <p>Premium Sativa Hybrid</p>
  </div>
</Tilt3DCardEffect>`}
        propsDoc={{
          maxTilt: 'Maximum tilt angle in degrees (default: 10)',
          perspective: 'CSS perspective value (default: 1000)',
          scale: 'Scale factor on hover (default: 1.02)',
          addDynamicShadow: 'Add dynamic shadows (default: true)',
        }}
      />

      {/* TypewriterIntroText Demo */}
      <ComponentDemo
        name="TypewriterIntroText"
        description="Adds nostalgic, cinematic intros with letter-by-letter typing and blinking cursor."
        component={
          <div className="text-center">
            <ClientWrapper fallback={<div className="text-2xl font-serif">Welcome to Apothecary Farms</div>}>
              <TypewriterIntroText
                text={["Welcome to", "Apothecary Farms", "Premium Cannabis Extracts"]}
                charDelay={50}
                cursor="✨"
                fontFamily="serif"
              />
            </ClientWrapper>
          </div>
        }
        codeExample={`<TypewriterIntroText 
  text={["Welcome to", "Apothecary Farms", "Premium Cannabis Extracts"]}
  charDelay={50}
  cursor="✨"
  loop={false}
/>`}
        propsDoc={{
          text: 'String or array of strings to type',
          charDelay: 'Delay between characters in ms (default: 50)',
          wordDelay: 'Delay between words in ms (default: 1000)',
          cursor: 'Cursor character or element (default: |)',
          loop: 'Loop the animation (default: false)',
        }}
      />

      {/* ParticleCanvasBackground Demo */}
      <ComponentDemo
        name="ParticleCanvasBackground"
        description="Creates immersive backgrounds with floating, interactive particles that react to cursor proximity."
        component={
          <div className="relative bg-charcoal-800 rounded-lg h-64 overflow-hidden">
            <ClientWrapper>
              <ParticleCanvasBackground
                particleCount={50}
                colors={['#fff9c4', '#d4edda', '#e2d5f1']}
                interactive={true}
              />
            </ClientWrapper>
            <div className="relative z-10 flex items-center justify-center h-full text-white">
              <p>Move your mouse to interact with particles</p>
            </div>
          </div>
        }
        codeExample={`<div className="hero-section relative">
  <ParticleCanvasBackground 
    particleCount={100}
    colors={['#fff9c4', '#d4edda', '#e2d5f1']}
    interactive={true}
  />
  <h1>Hero Content</h1>
</div>`}
        propsDoc={{
          particleCount: 'Number of particles (default: 80)',
          colors: 'Array of particle colors',
          interactive: 'React to mouse movement (default: true)',
          connectionDistance: 'Distance for particle connections (default: 100)',
        }}
      />

      {/* LottieIconAnimations Demo */}
      <ComponentDemo
        name="LottieIconAnimations"
        description="Makes key icons come alive with smooth microinteractions for extraction tech and product categories."
        component={
          <div className="flex justify-center gap-8">
            <LottieIconAnimations 
              animationData={{}}
              trigger="hover"
              category="beaker"
              width={64}
              height={64}
            />
            <LottieIconAnimations 
              animationData={{}}
              trigger="hover"
              category="trichome"
              width={64}
              height={64}
            />
            <LottieIconAnimations 
              animationData={{}}
              trigger="hover"
              category="flame"
              width={64}
              height={64}
            />
          </div>
        }
        codeExample={`<LottieIconAnimations 
  animationData={beakerAnimation}
  trigger="hover"
  category="beaker"
  width={64}
  height={64}
/>`}
        propsDoc={{
          animationData: 'Lottie animation data or URL',
          trigger: 'Animation trigger: hover | click | load | manual',
          category: 'Icon category: beaker | trichome | flame | extraction | flower | product',
          loop: 'Loop animation (default: true)',
        }}
      />

      {/* MorphingBlobHero Demo */}
      <ComponentDemo
        name="MorphingBlobHero"
        description="Adds fluid, organic SVG blob animations that morph behind content in hero sections."
        component={
          <div className="relative h-64 bg-charcoal-100 rounded-lg overflow-hidden">
            <MorphingBlobHero 
              gradientTheme="rosin"
              size={200}
              position="center"
              animate={true}
            />
            <div className="relative z-10 flex items-center justify-center h-full">
              <h4 className="text-2xl font-bold text-charcoal-800">
                Hero Content
              </h4>
            </div>
          </div>
        }
        codeExample={`<div className="hero-section relative">
  <MorphingBlobHero 
    gradientTheme="rosin"
    size={400}
    position="center"
    animate={true}
  />
  <h1 className="relative z-10">Hero Content</h1>
</div>`}
        propsDoc={{
          gradientTheme: 'Terpene theme: pineapple | gmo | rosin | flower | extract',
          size: 'Blob size in pixels (default: 300)',
          position: 'Position: center | top-left | top-right | bottom-left | bottom-right',
          animate: 'Enable morphing animation (default: true)',
        }}
      />
    </div>
  );
};

export default VisualComponentsShowcase;
