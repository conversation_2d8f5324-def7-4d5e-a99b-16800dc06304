[{"O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx": "1", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\page.tsx": "2", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\products\\page.tsx": "3", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\AgeVerification.tsx": "4", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\ComplianceDisclaimer.tsx": "5", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\Hero.tsx": "6", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\LocationInfo.tsx": "7", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\Navigation.tsx": "8", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\ProductShowcase.tsx": "9", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\about\\page.tsx": "10", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\locations\\page.tsx": "11", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\cultivars\\page.tsx": "12", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\deals\\page.tsx": "13", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\demo\\page.tsx": "14", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\SEOHead.tsx": "15", "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\content\\site-config.ts": "16"}, {"size": 1457, "mtime": 1752178506126, "results": "17", "hashOfConfig": "18"}, {"size": 6744, "mtime": 1752178937045, "results": "19", "hashOfConfig": "18"}, {"size": 10671, "mtime": 1752177486944, "results": "20", "hashOfConfig": "18"}, {"size": 2777, "mtime": 1752178619859, "results": "21", "hashOfConfig": "18"}, {"size": 1887, "mtime": 1752177507793, "results": "22", "hashOfConfig": "18"}, {"size": 4069, "mtime": 1752180803001, "results": "23", "hashOfConfig": "18"}, {"size": 7860, "mtime": 1752178665233, "results": "24", "hashOfConfig": "18"}, {"size": 3796, "mtime": 1752183300085, "results": "25", "hashOfConfig": "18"}, {"size": 7263, "mtime": 1752180864910, "results": "26", "hashOfConfig": "18"}, {"size": 9022, "mtime": 1752179031266, "results": "27", "hashOfConfig": "18"}, {"size": 7285, "mtime": 1752178994295, "results": "28", "hashOfConfig": "18"}, {"size": 16277, "mtime": 1752183481699, "results": "29", "hashOfConfig": "18"}, {"size": 8918, "mtime": 1752183549238, "results": "30", "hashOfConfig": "18"}, {"size": 3487, "mtime": 1752181088138, "results": "31", "hashOfConfig": "18"}, {"size": 3074, "mtime": 1752183411376, "results": "32", "hashOfConfig": "18"}, {"size": 7697, "mtime": 1752183340531, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jz4u5i", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\layout.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\products\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\AgeVerification.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\ComplianceDisclaimer.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\Hero.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\LocationInfo.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\Navigation.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\ProductShowcase.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\about\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\locations\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\cultivars\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\deals\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\app\\demo\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\components\\SEOHead.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\Apothecary\\apothecary-extracts\\src\\content\\site-config.ts", [], []]