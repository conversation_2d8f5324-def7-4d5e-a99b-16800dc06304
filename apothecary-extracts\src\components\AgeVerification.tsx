'use client';

import { useState, useEffect } from 'react';

interface AgeVerificationProps {
  onVerified: () => void;
}

export default function AgeVerification({ onVerified }: AgeVerificationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already verified their age
    const hasVerified = localStorage.getItem('ageVerified');
    if (!hasVerified) {
      setIsVisible(true);
    } else {
      onVerified();
    }
  }, [onVerified]);

  const handleVerification = (isOfAge: boolean) => {
    if (isOfAge) {
      localStorage.setItem('ageVerified', 'true');
      setIsVisible(false);
      onVerified();
    } else {
      // Redirect to educational resources or show message
      window.location.href = 'https://www.samhsa.gov/marijuana';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4">
      <div className="bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium">
        <div className="mb-6">
          <h2 className="text-2xl font-serif font-bold text-primary-800 mb-2">
            Age Verification Required
          </h2>
          <p className="text-charcoal-700 text-sm leading-relaxed">
            You must be 21 years of age or older to view this website and purchase cannabis products.
            Please verify your age to continue.
          </p>
        </div>
        
        <div className="space-y-3">
          <button
            onClick={() => handleVerification(true)}
            className="w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2"
          >
            Yes, I&apos;m 21 or older
          </button>
          
          <button
            onClick={() => handleVerification(false)}
            className="w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2"
          >
            No, I&apos;m under 21
          </button>
        </div>
        
        <div className="mt-6 pt-4 border-t border-charcoal-200">
          <p className="text-xs text-charcoal-500 leading-relaxed">
            By entering this website, you certify that you are of legal age to purchase cannabis products 
            in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended 
            to diagnose, treat, cure, or prevent any disease.
          </p>
        </div>
      </div>
    </div>
  );
}
