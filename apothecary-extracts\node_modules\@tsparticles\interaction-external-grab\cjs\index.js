"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadExternalGrabInteraction = loadExternalGrabInteraction;
const Grabber_js_1 = require("./Grabber.js");
async function loadExternalGrabInteraction(engine, refresh = true) {
    engine.checkVersion("3.8.1");
    await engine.addInteractor("externalGrab", container => {
        return Promise.resolve(new Grabber_js_1.Grabber(container, engine));
    }, refresh);
}
__exportStar(require("./Options/Classes/Grab.js"), exports);
__exportStar(require("./Options/Classes/GrabLinks.js"), exports);
__exportStar(require("./Options/Interfaces/IGrab.js"), exports);
__exportStar(require("./Options/Interfaces/IGrabLinks.js"), exports);
