'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Head from 'next/head';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';
import ComplianceDisclaimer from '@/components/ComplianceDisclaimer';
import { weeklyDeals, siteConfig } from '@/content/site-config';



export default function DealsPage() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);
  const { deals: pageConfig } = siteConfig.pages;

  return (
    <>
      <Head>
        <title>{pageConfig.seo.title}</title>
        <meta name="description" content={pageConfig.seo.description} />
        <meta property="og:title" content={pageConfig.seo.title} />
        <meta property="og:description" content={pageConfig.seo.description} />
        <meta name="age-restriction" content="21+" />
      </Head>

      {!isAgeVerified && (
        <AgeVerification onVerified={() => setIsAgeVerified(true)} />
      )}

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          <Navigation />

          <main>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden py-24 lg:py-32">
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-primary-900"></div>
              </div>
              
              <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <motion.h1 
                  className="text-4xl md:text-6xl font-serif font-bold leading-tight mb-6"
                  initial={{ opacity: 0, y: -20 }} 
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  {pageConfig.hero.headline}
                </motion.h1>

                <motion.p
                  className="text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  {pageConfig.hero.subheadline}
                </motion.p>
              </div>
            </section>

            {/* Compliance Disclaimer */}
            <section className="py-8 bg-cream-100">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <ComplianceDisclaimer />
              </div>
            </section>

            {/* Weekly Deals Section */}
            <section className="py-20 bg-cream-100">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                  className="text-center mb-16"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-4xl font-serif font-bold text-primary-800 mb-4">
                    Weekly Deal Schedule
                  </h2>
                  <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
                    Plan your visits around our rotating daily specials. Each day brings new opportunities to save on premium products.
                  </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {weeklyDeals.map((deal, index) => (
                    <motion.div
                      key={deal.day}
                      className="bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      whileHover={{ scale: 1.02, y: -4 }}
                      transition={{ 
                        duration: 0.3,
                        delay: index * 0.1 
                      }}
                      viewport={{ once: true }}
                    >
                      <div className={`h-24 bg-gradient-to-br ${deal.color} relative overflow-hidden flex items-center justify-center`}>
                        <div className="text-center text-cream-50">
                          <div className="text-3xl mb-1">{deal.icon}</div>
                          <div className="text-sm font-medium">{deal.day}</div>
                        </div>
                      </div>

                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-primary-800 mb-2">
                          {deal.title}
                        </h3>
                        <div className="text-lg font-medium text-gold-600 mb-3">
                          {deal.discount}
                        </div>
                        <p className="text-charcoal-600 text-sm leading-relaxed">
                          {deal.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </section>

            {/* Terms and Conditions */}
            <section className="py-16 bg-cream-200">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-2xl font-serif font-bold text-primary-800 mb-6 text-center">
                    Deal Terms & Conditions
                  </h3>
                  <div className="bg-cream-50 p-6 rounded-lg border border-gold-200">
                    <ul className="text-charcoal-600 space-y-2 text-sm">
                      <li>• Deals valid during regular business hours on specified days</li>
                      <li>• Cannot be combined with other offers or promotions</li>
                      <li>• Subject to product availability and inventory limits</li>
                      <li>• Valid Colorado ID and medical card (where applicable) required</li>
                      <li>• Deals may change without notice - visit store for current offers</li>
                      <li>• First-timer deals require proof of first-time customer status</li>
                    </ul>
                  </div>
                </motion.div>
              </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-primary-800">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-serif font-bold text-cream-50 mb-4">
                    Visit Us Today
                  </h2>
                  <p className="text-xl text-cream-200 mb-8">
                    Stop by our dispensary to take advantage of today&apos;s deals and discover your new favorites.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="/locations"
                      className="inline-flex items-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800"
                    >
                      Find Our Locations
                    </a>
                    <a
                      href="/products"
                      className="inline-flex items-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200"
                    >
                      Browse Products
                    </a>
                  </div>
                </motion.div>
              </div>
            </section>
          </main>

          {/* Footer */}
          <footer className="bg-charcoal-800 text-cream-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <p className="text-cream-400 text-sm">
                © 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase
              </p>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}
