'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { ParallaxSectionProps } from '../../../types/ui';
import { brandColors } from '../../../styles/brand';

/**
 * ParallaxSection Component
 * 
 * Creates parallax scrolling sections for immersive storytelling about cultivation and extraction processes.
 * Features customizable parallax speed, overlay effects, and responsive design.
 * 
 * @example
 * ```tsx
 * <ParallaxSection
 *   backgroundImage="/images/cultivation-process.jpg"
 *   speed={0.5}
 *   height="100vh"
 *   overlay={true}
 *   textTheme="light"
 * >
 *   <div className="text-center">
 *     <h2 className="text-4xl font-bold mb-4">Our Cultivation Process</h2>
 *     <p className="text-xl">From seed to harvest, every step is carefully monitored</p>
 *   </div>
 * </ParallaxSection>
 * ```
 */
const ParallaxSection: React.FC<ParallaxSectionProps> = ({
  backgroundImage,
  speed = 0.5,
  height = '100vh',
  overlay = true,
  overlayOpacity = 0.4,
  textTheme = 'light',
  className = '',
  style,
  children,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isClient, setIsClient] = useState(false);
  
  // Ensure we're on the client side to prevent hydration mismatches
  useEffect(() => {
    setIsClient(true);
  }, []);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start']
  });

  // Transform scroll progress to parallax movement
  const y = useTransform(scrollYProgress, [0, 1], ['0%', `${speed * 100}%`]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  // Get text color based on theme
  const getTextColor = () => {
    return textTheme === 'light' ? 'text-cream-50' : 'text-charcoal-800';
  };

  // Get overlay color
  const getOverlayColor = () => {
    return textTheme === 'light' 
      ? `rgba(0, 0, 0, ${overlayOpacity})` 
      : `rgba(255, 255, 255, ${overlayOpacity})`;
  };

  if (!isClient) {
    // Server-side fallback
    return (
      <section
        ref={containerRef}
        className={`relative overflow-hidden ${className}`}
        style={{
          height,
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          ...style,
        }}
      >
        {overlay && (
          <div 
            className="absolute inset-0"
            style={{ backgroundColor: getOverlayColor() }}
          />
        )}
        <div className={`relative z-10 h-full flex items-center justify-center px-6 ${getTextColor()}`}>
          {children}
        </div>
      </section>
    );
  }

  return (
    <section
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ height, ...style }}
    >
      {/* Parallax Background */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        style={{
          y,
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          scale: 1.1, // Slightly larger to prevent gaps during parallax
        }}
      />

      {/* Overlay */}
      {overlay && (
        <motion.div 
          className="absolute inset-0 z-10"
          style={{ 
            backgroundColor: getOverlayColor(),
            opacity,
          }}
        />
      )}

      {/* Content */}
      <motion.div 
        className={`relative z-20 h-full flex items-center justify-center px-6 ${getTextColor()}`}
        style={{ opacity }}
      >
        <div className="max-w-4xl mx-auto text-center">
          {children}
        </div>
      </motion.div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 z-30">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z" 
            fill="var(--background)"
          />
        </svg>
      </div>
    </section>
  );
};

export default ParallaxSection;
