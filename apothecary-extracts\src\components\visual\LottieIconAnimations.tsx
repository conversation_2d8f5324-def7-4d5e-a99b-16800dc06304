'use client';

import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { LottieIconAnimationsProps } from '../../types/visual';
import { brandColors, brandShadows } from '../../styles/brand';

/**
 * LottieIconAnimations Component
 * 
 * Makes key icons come alive via smooth, relevant microinteractions.
 * Features animated icons for extraction tech, flower types, and product categories.
 * 
 * Note: This is a placeholder implementation. In production, you would use:
 * import Lottie from 'react-lottie-player'
 * 
 * @example
 * ```tsx
 * <LottieIconAnimations 
 *   animationData={beakerAnimation}
 *   trigger="hover"
 *   category="beaker"
 *   width={64}
 *   height={64}
 * />
 * ```
 */
const LottieIconAnimations: React.FC<LottieIconAnimationsProps> = ({
  animationData,
  className = '',
  style,
  trigger = 'hover',
  loop = true,
  speed = 1,
  width = 48,
  height = 48,
  category = 'beaker',
}) => {
  const [isPlaying, setIsPlaying] = useState(trigger === 'load');
  const [isHovered, setIsHovered] = useState(false);

  // Get category-specific styling
  const getCategoryStyles = () => {
    switch (category) {
      case 'beaker':
        return {
          color: brandColors.primary[600],
          glow: brandShadows.apothecaryGlow,
        };
      case 'trichome':
        return {
          color: brandColors.gold[400],
          glow: brandShadows.goldGlow,
        };
      case 'flame':
        return {
          color: '#ff8c00',
          glow: '0 0 20px rgba(255, 140, 0, 0.3)',
        };
      case 'extraction':
        return {
          color: brandColors.sage[500],
          glow: brandShadows.medium,
        };
      case 'flower':
        return {
          color: brandColors.primary[500],
          glow: brandShadows.apothecaryGlow,
        };
      case 'product':
        return {
          color: brandColors.charcoal[600],
          glow: brandShadows.medium,
        };
      default:
        return {
          color: brandColors.primary[600],
          glow: brandShadows.apothecaryGlow,
        };
    }
  };

  const categoryStyles = getCategoryStyles();

  // Handle animation triggers
  const handleMouseEnter = () => {
    setIsHovered(true);
    if (trigger === 'hover') {
      setIsPlaying(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (trigger === 'hover' && !loop) {
      setIsPlaying(false);
    }
  };

  const handleClick = () => {
    if (trigger === 'click') {
      setIsPlaying(!isPlaying);
    }
  };

  // Placeholder SVG icons for different categories
  const getPlaceholderIcon = () => {
    switch (category) {
      case 'beaker':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <path d="M9 2v6.5L4.5 14c-.83 1.24-.5 2.91.74 3.74.45.3.96.45 1.51.45h9.5c.55 0 1.05-.15 1.5-.45 1.24-.83 1.57-2.5.74-3.74L13 8.5V2h-4zm2 2h2v5.5l4.5 5.5H6.5L11 9.5V4z"/>
          </svg>
        );
      case 'trichome':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <circle cx="12" cy="12" r="3"/>
            <circle cx="6" cy="6" r="2"/>
            <circle cx="18" cy="6" r="2"/>
            <circle cx="6" cy="18" r="2"/>
            <circle cx="18" cy="18" r="2"/>
            <line x1="12" y1="9" x2="12" y2="15"/>
            <line x1="9" y1="12" x2="15" y2="12"/>
          </svg>
        );
      case 'flame':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <path d="M12 2c1.5 3 4 6 4 10a4 4 0 0 1-8 0c0-4 2.5-7 4-10z"/>
            <path d="M12 7c.5 1.5 2 3 2 5a2 2 0 1 1-4 0c0-2 1.5-3.5 2-5z"/>
          </svg>
        );
      case 'extraction':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
            <path d="M9 12l2 2 4-4"/>
          </svg>
        );
      case 'flower':
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <path d="M12 2a3 3 0 0 0-3 3c0 1.12.61 2.1 1.5 2.61L9 9.5l-2.5-1.5C5.61 7.1 5 6.12 5 5a3 3 0 0 0-6 0c0 1.31.84 2.41 2 2.83v2.34C.84 10.59 0 11.69 0 13a3 3 0 0 0 6 0c0-1.12-.61-2.1-1.5-2.61L7 8.5l2.5 1.5c.89-.51 1.5-1.49 1.5-2.61a3 3 0 0 0-6 0z"/>
          </svg>
        );
      default:
        return (
          <svg viewBox="0 0 24 24" fill="currentColor" width={width} height={height}>
            <circle cx="12" cy="12" r="10"/>
            <path d="M8 12l2 2 4-4"/>
          </svg>
        );
    }
  };

  return (
    <motion.div
      className={`lottie-icon-animations inline-block cursor-pointer ${className}`}
      style={{
        color: categoryStyles.color,
        ...style,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      whileHover={{
        scale: 1.1,
        filter: 'brightness(1.2)',
        boxShadow: isHovered ? categoryStyles.glow : 'none',
      }}
      whileTap={{ scale: 0.95 }}
      animate={{
        rotate: isPlaying ? [0, 360] : 0,
      }}
      transition={{
        rotate: {
          duration: 2 / speed,
          repeat: loop && isPlaying ? Infinity : 0,
          ease: 'linear',
        },
        scale: { duration: 0.2 },
        filter: { duration: 0.2 },
      }}
    >
      {/* 
        In production, replace this with:
        <Lottie
          animationData={animationData}
          play={isPlaying}
          loop={loop}
          speed={speed}
          style={{ width, height }}
        />
      */}
      {getPlaceholderIcon()}
    </motion.div>
  );
};

export default LottieIconAnimations;
