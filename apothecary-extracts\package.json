{"name": "apothecary-extracts", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tsparticles/basic": "^3.8.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/interaction-external-attract": "^3.8.1", "@tsparticles/interaction-external-repulse": "^3.8.1", "@tsparticles/react": "^3.0.0", "framer-motion": "^12.23.3", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "react-lottie-player": "^2.1.0", "react-parallax-tilt": "^1.7.300", "tsparticles": "^3.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}