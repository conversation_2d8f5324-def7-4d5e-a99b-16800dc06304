/*! For license information please see tsparticles.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/plugin-absorbers"),require("@tsparticles/updater-destroy"),require("@tsparticles/plugin-emitters"),require("@tsparticles/plugin-emitters-shape-circle"),require("@tsparticles/plugin-emitters-shape-square"),require("@tsparticles/interaction-external-trail"),require("@tsparticles/updater-roll"),require("@tsparticles/slim"),require("@tsparticles/shape-text"),require("@tsparticles/updater-tilt"),require("@tsparticles/updater-twinkle"),require("@tsparticles/updater-wobble"));else if("function"==typeof define&&define.amd)define(["@tsparticles/plugin-absorbers","@tsparticles/updater-destroy","@tsparticles/plugin-emitters","@tsparticles/plugin-emitters-shape-circle","@tsparticles/plugin-emitters-shape-square","@tsparticles/interaction-external-trail","@tsparticles/updater-roll","@tsparticles/slim","@tsparticles/shape-text","@tsparticles/updater-tilt","@tsparticles/updater-twinkle","@tsparticles/updater-wobble"],t);else{var r="object"==typeof exports?t(require("@tsparticles/plugin-absorbers"),require("@tsparticles/updater-destroy"),require("@tsparticles/plugin-emitters"),require("@tsparticles/plugin-emitters-shape-circle"),require("@tsparticles/plugin-emitters-shape-square"),require("@tsparticles/interaction-external-trail"),require("@tsparticles/updater-roll"),require("@tsparticles/slim"),require("@tsparticles/shape-text"),require("@tsparticles/updater-tilt"),require("@tsparticles/updater-twinkle"),require("@tsparticles/updater-wobble")):t(e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window);for(var i in r)("object"==typeof exports?exports:e)[i]=r[i]}}(this,((e,t,r,i,a,s,l,p,o,u,n,d)=>(()=>{var c={242:e=>{e.exports=s},78:t=>{t.exports=e},526:e=>{e.exports=r},193:e=>{e.exports=i},140:e=>{e.exports=a},808:e=>{e.exports=o},962:e=>{e.exports=p},173:e=>{e.exports=t},544:e=>{e.exports=l},844:e=>{e.exports=u},999:e=>{e.exports=n},238:e=>{e.exports=d}},w={};function x(e){var t=w[e];if(void 0!==t)return t.exports;var r=w[e]={exports:{}};return c[e](r,r.exports,x),r.exports}x.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return x.d(t,{a:t}),t},x.d=(e,t)=>{for(var r in t)x.o(t,r)&&!x.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},x.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),x.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var b={};x.r(b),x.d(b,{loadFull:()=>O});var q=x(78),f=x(173),m=x(526),g=x(193),y=x(140),h=x(242),v=x(544),S=x(962),j=x(808),P=x(844),T=x(999),k=x(238);async function O(e,t=!0){e.checkVersion("3.8.1"),await(0,f.loadDestroyUpdater)(e,!1),await(0,v.loadRollUpdater)(e,!1),await(0,P.loadTiltUpdater)(e,!1),await(0,T.loadTwinkleUpdater)(e,!1),await(0,k.loadWobbleUpdater)(e,!1),await(0,j.loadTextShape)(e,!1),await(0,h.loadExternalTrailInteraction)(e,!1),await(0,q.loadAbsorbersPlugin)(e,!1),await(0,m.loadEmittersPlugin)(e,!1),await(0,g.loadEmittersShapeCircle)(e,!1),await(0,y.loadEmittersShapeSquare)(e,!1),await(0,S.loadSlim)(e,t)}return b})()));