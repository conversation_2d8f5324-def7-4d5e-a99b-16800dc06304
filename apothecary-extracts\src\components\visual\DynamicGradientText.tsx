'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { DynamicGradientTextProps } from '../../types/visual';
import { terpeneColors, brandTypography } from '../../styles/brand';

/**
 * DynamicGradientText Component
 * 
 * Draws attention to headlines using animated gradient fills.
 * Features terpene-themed gradients with background-clip: text.
 * 
 * @example
 * ```tsx
 * <DynamicGradientText 
 *   text="Premium Cannabis Extracts" 
 *   theme="rosin"
 *   fontSize="3rem"
 *   animate={true}
 * />
 * ```
 */
const DynamicGradientText: React.FC<DynamicGradientTextProps> = ({
  text,
  className = '',
  style,
  theme = 'pineapple',
  customGradient,
  animationSpeed = 3000,
  fontSize = '2rem',
  fontWeight = 700,
  animate = true,
}) => {
  const [gradientPosition, setGradientPosition] = useState(0);

  // Get gradient based on theme or custom gradient
  const getGradient = () => {
    if (customGradient) return customGradient;
    
    const terpene = terpeneColors[theme];
    if (!terpene) return terpeneColors.pineapple.gradient;
    
    return terpene.gradient;
  };

  // Create animated gradient with moving position
  const getAnimatedGradient = () => {
    if (!animate) return getGradient();
    
    const baseGradient = getGradient();
    
    // Extract colors from gradient and create animated version
    if (theme === 'pineapple') {
      return `linear-gradient(45deg, 
        #ff8c00 ${gradientPosition - 50}%, 
        #ffd700 ${gradientPosition}%, 
        #ff8c00 ${gradientPosition + 50}%)`;
    } else if (theme === 'gmo') {
      return `linear-gradient(45deg, 
        #4a90e2 ${gradientPosition - 50}%, 
        #87ceeb ${gradientPosition}%, 
        #4a90e2 ${gradientPosition + 50}%)`;
    } else if (theme === 'rosin') {
      return `linear-gradient(45deg, 
        #d4a574 ${gradientPosition - 50}%, 
        #ff8c00 ${gradientPosition}%, 
        #d4a574 ${gradientPosition + 50}%)`;
    } else if (theme === 'flower') {
      return `linear-gradient(45deg, 
        #359a5a ${gradientPosition - 50}%, 
        #ffd700 ${gradientPosition}%, 
        #359a5a ${gradientPosition + 50}%)`;
    } else if (theme === 'extract') {
      return `linear-gradient(45deg, 
        #ff8c00 ${gradientPosition - 50}%, 
        #9370db ${gradientPosition}%, 
        #ff8c00 ${gradientPosition + 50}%)`;
    }
    
    return baseGradient;
  };

  // Animate gradient position
  useEffect(() => {
    if (!animate) return;

    const interval = setInterval(() => {
      setGradientPosition(prev => (prev + 1) % 200);
    }, animationSpeed / 200);

    return () => clearInterval(interval);
  }, [animate, animationSpeed]);

  return (
    <motion.h1
      className={`dynamic-gradient-text ${className}`}
      style={{
        backgroundImage: getAnimatedGradient(),
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        color: 'transparent',
        fontSize,
        fontWeight,
        fontFamily: brandTypography.fonts.serif,
        backgroundSize: '200% 200%',
        display: 'inline-block',
        lineHeight: 1.2,
        ...style,
      }}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, ease: brandAnimations.easing.smooth }}
    >
      {text}
    </motion.h1>
  );
};

export default DynamicGradientText;
