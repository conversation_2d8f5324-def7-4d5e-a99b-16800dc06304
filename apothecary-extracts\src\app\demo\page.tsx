'use client';

import { useState } from 'react';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';
import Hero from '@/components/Hero';
import ProductShowcase from '@/components/ProductShowcase';

export default function DemoPage() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);

  return (
    <>
      {!isAgeVerified && (
        <AgeVerification onVerified={() => setIsAgeVerified(true)} />
      )}

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          <Navigation />

          <main>
            {/* Enhanced Hero Section with Video */}
            <Hero 
              headline="Premium Cannabis Excellence"
              subheadline="Experience Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity with cutting-edge extraction techniques."
              ctas={["Explore Products", "Visit Locations"]}
              // videoSrc="/assets/hero/cannabis-video.mp4" // Uncomment when video is available
            />

            {/* Enhanced Product Categories Section with Animations */}
            <ProductShowcase />

            {/* Demo Information */}
            <section className="py-20 bg-primary-800">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 className="text-3xl font-serif font-bold text-cream-50 mb-4">
                  Enhanced Components Demo
                </h2>
                <p className="text-xl text-cream-200 mb-8">
                  This page demonstrates the enhanced Hero and ProductShowcase components with framer-motion animations.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
                  <div className="bg-cream-50 p-6 rounded-lg">
                    <h3 className="text-xl font-semibold text-primary-800 mb-3">Hero Enhancements</h3>
                    <ul className="text-charcoal-600 space-y-2">
                      <li>• Video background support</li>
                      <li>• Smooth fade-in animations</li>
                      <li>• Customizable headlines and CTAs</li>
                      <li>• Professional design system integration</li>
                      <li>• Full-screen responsive layout</li>
                    </ul>
                  </div>
                  <div className="bg-cream-50 p-6 rounded-lg">
                    <h3 className="text-xl font-semibold text-primary-800 mb-3">ProductShowcase Enhancements</h3>
                    <ul className="text-charcoal-600 space-y-2">
                      <li>• Staggered card animations</li>
                      <li>• Hover scale effects</li>
                      <li>• Scroll-triggered animations</li>
                      <li>• Maintained professional styling</li>
                      <li>• Improved user engagement</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>
          </main>

          {/* Simple Footer */}
          <footer className="bg-charcoal-800 text-cream-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <p className="text-cream-400 text-sm">
                Demo Page - Enhanced Components | Apothecary Extracts
              </p>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}
