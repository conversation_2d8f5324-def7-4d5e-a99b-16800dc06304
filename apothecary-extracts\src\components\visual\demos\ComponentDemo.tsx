'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ComponentDemoProps } from '../../../types/visual';
import { brandColors, brandShadows } from '../../../styles/brand';

/**
 * ComponentDemo Wrapper
 * 
 * A reusable demo wrapper for showcasing visual components
 * with code examples and interactive controls.
 */
const ComponentDemo: React.FC<ComponentDemoProps> = ({
  name,
  description,
  component,
  codeExample,
  propsDoc,
}) => {
  const [showCode, setShowCode] = useState(false);
  const [showProps, setShowProps] = useState(false);

  return (
    <div className="component-demo bg-cream-100 rounded-lg p-6 mb-8 border border-sage-200">
      {/* Header */}
      <div className="demo-header mb-6">
        <h3 className="text-2xl font-serif font-bold text-charcoal-800 mb-2">
          {name}
        </h3>
        <p className="text-charcoal-600 leading-relaxed">
          {description}
        </p>
      </div>

      {/* Demo Area */}
      <div 
        className="demo-area bg-white rounded-lg p-8 mb-4 relative overflow-hidden"
        style={{
          minHeight: '200px',
          boxShadow: brandShadows.medium,
        }}
      >
        {component}
      </div>

      {/* Controls */}
      <div className="demo-controls flex gap-4 mb-4">
        <button
          onClick={() => setShowCode(!showCode)}
          className="btn-secondary text-sm"
          style={{
            backgroundColor: showCode ? brandColors.primary[600] : 'transparent',
            color: showCode ? brandColors.cream[50] : brandColors.primary[600],
            border: `2px solid ${brandColors.primary[600]}`,
            padding: '0.5rem 1rem',
            borderRadius: '0.375rem',
            fontWeight: 600,
            transition: 'all 0.2s ease-in-out',
          }}
        >
          {showCode ? 'Hide Code' : 'Show Code'}
        </button>

        {propsDoc && (
          <button
            onClick={() => setShowProps(!showProps)}
            className="btn-secondary text-sm"
            style={{
              backgroundColor: showProps ? brandColors.sage[500] : 'transparent',
              color: showProps ? brandColors.cream[50] : brandColors.sage[500],
              border: `2px solid ${brandColors.sage[500]}`,
              padding: '0.5rem 1rem',
              borderRadius: '0.375rem',
              fontWeight: 600,
              transition: 'all 0.2s ease-in-out',
            }}
          >
            {showProps ? 'Hide Props' : 'Show Props'}
          </button>
        )}
      </div>

      {/* Code Example */}
      {showCode && codeExample && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="code-example bg-charcoal-800 rounded-lg p-4 mb-4"
        >
          <pre className="text-cream-100 text-sm overflow-x-auto">
            <code>{codeExample}</code>
          </pre>
        </motion.div>
      )}

      {/* Props Documentation */}
      {showProps && propsDoc && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="props-doc bg-sage-50 rounded-lg p-4"
        >
          <h4 className="font-semibold text-charcoal-800 mb-3">Props</h4>
          <div className="space-y-2">
            {Object.entries(propsDoc).map(([prop, info]) => (
              <div key={prop} className="prop-item">
                <span className="font-mono text-sm text-primary-600 font-semibold">
                  {prop}
                </span>
                <span className="text-charcoal-600 ml-2">
                  {typeof info === 'string' ? info : JSON.stringify(info)}
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ComponentDemo;
