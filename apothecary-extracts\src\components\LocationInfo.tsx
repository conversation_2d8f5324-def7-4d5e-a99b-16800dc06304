import Link from 'next/link';

const locations = [
  {
    name: 'Colorado Springs - Garden of the Gods',
    address: '414 Garden of the Gods Road, Colorado Springs, CO 80907',
    phone: '(*************',
    hours: {
      weekdays: '9:00 AM - 9:00 PM',
      saturday: '9:00 AM - 9:00 PM',
      sunday: '10:00 AM - 8:00 PM'
    },
    services: ['Medical', 'Recreational', 'Curbside Pickup'],
    isNew: true
  },
  {
    name: 'Colorado Springs - Downtown',
    address: '123 Main Street, Colorado Springs, CO 80903',
    phone: '(*************',
    hours: {
      weekdays: '8:00 AM - 10:00 PM',
      saturday: '8:00 AM - 10:00 PM',
      sunday: '9:00 AM - 9:00 PM'
    },
    services: ['Medical', 'Recreational', 'Delivery', 'Consultation'],
    isNew: false
  }
];

export default function LocationInfo() {
  return (
    <section className="py-20 bg-cream-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-serif font-bold text-primary-800 mb-4">
            Visit Our Dispensaries
          </h2>
          <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
            Find us at convenient locations throughout Colorado Springs. 
            Each dispensary offers the full Apothecary Extracts experience.
          </p>
        </div>

        {/* Locations Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {locations.map((location) => (
            <div
              key={location.name}
              className="bg-cream-100 rounded-xl p-8 shadow-soft hover:shadow-medium transition-shadow duration-300 relative overflow-hidden"
            >
              {/* New Badge */}
              {location.isNew && (
                <div className="absolute top-4 right-4">
                  <span className="bg-gold-500 text-primary-800 px-3 py-1 rounded-full text-sm font-semibold">
                    New Location
                  </span>
                </div>
              )}

              <div className="mb-6">
                <h3 className="text-2xl font-semibold text-primary-800 mb-2">
                  {location.name}
                </h3>
                <p className="text-charcoal-600 mb-4 flex items-start">
                  <svg className="w-5 h-5 text-primary-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                  </svg>
                  {location.address}
                </p>
                <p className="text-charcoal-600 mb-4 flex items-center">
                  <svg className="w-5 h-5 text-primary-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
                  </svg>
                  {location.phone}
                </p>
              </div>

              {/* Hours */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-primary-800 mb-3">Store Hours</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-charcoal-600">Monday - Friday:</span>
                    <span className="text-charcoal-800 font-medium">{location.hours.weekdays}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-charcoal-600">Saturday:</span>
                    <span className="text-charcoal-800 font-medium">{location.hours.saturday}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-charcoal-600">Sunday:</span>
                    <span className="text-charcoal-800 font-medium">{location.hours.sunday}</span>
                  </div>
                </div>
              </div>

              {/* Services */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-primary-800 mb-3">Services</h4>
                <div className="flex flex-wrap gap-2">
                  {location.services.map((service) => (
                    <span
                      key={service}
                      className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full"
                    >
                      {service}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Link
                  href={`/locations/${location.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}`}
                  className="flex-1 bg-primary-800 text-cream-50 py-3 px-4 rounded-lg text-center font-medium hover:bg-primary-700 transition-colors duration-200"
                >
                  View Details
                </Link>
                <a
                  href={`https://maps.google.com/?q=${encodeURIComponent(location.address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-transparent text-primary-800 py-3 px-4 rounded-lg text-center font-medium border border-primary-800 hover:bg-primary-50 transition-colors duration-200"
                >
                  Get Directions
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="bg-gold-100 rounded-xl p-8 text-center">
          <h3 className="text-2xl font-serif font-bold text-primary-800 mb-4">
            Can&apos;t Make It to Our Store?
          </h3>
          <p className="text-charcoal-700 mb-6 max-w-2xl mx-auto">
            We offer curbside pickup and delivery services (where legally permitted) 
            to make your cannabis shopping experience as convenient as possible.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/delivery"
              className="inline-flex items-center px-6 py-3 bg-primary-800 text-cream-50 font-semibold rounded-lg hover:bg-primary-700 transition-colors duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
                <path d="M5 6h5v2H5zm11.5 9c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"/>
              </svg>
              Delivery Info
            </Link>
            <Link
              href="/curbside"
              className="inline-flex items-center px-6 py-3 bg-transparent text-primary-800 font-semibold rounded-lg border border-primary-800 hover:bg-primary-50 transition-colors duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
              </svg>
              Curbside Pickup
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
