'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

interface TextWithGlitchEffectProps {
  text: string;
  className?: string;
  glitchIntensity?: 'low' | 'medium' | 'high';
  triggerOnHover?: boolean;
  autoGlitch?: boolean;
  autoGlitchInterval?: number;
}

export default function TextWithGlitchEffect({
  text,
  className = '',
  glitchIntensity = 'medium',
  triggerOnHover = false,
  autoGlitch = false,
  autoGlitchInterval = 3000
}: TextWithGlitchEffectProps) {
  const [isGlitching, setIsGlitching] = useState(false);
  const [displayText, setDisplayText] = useState(text);

  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const intensitySettings = {
    low: { duration: 0.1, iterations: 2 },
    medium: { duration: 0.15, iterations: 3 },
    high: { duration: 0.2, iterations: 5 }
  };

  const settings = intensitySettings[glitchIntensity];

  const triggerGlitch = () => {
    if (isGlitching) return;
    
    setIsGlitching(true);
    let iteration = 0;

    const glitchInterval = setInterval(() => {
      setDisplayText(
        text
          .split('')
          .map((char, index) => {
            if (Math.random() < 0.3) {
              return glitchChars[Math.floor(Math.random() * glitchChars.length)];
            }
            return char;
          })
          .join('')
      );

      iteration++;
      if (iteration >= settings.iterations) {
        clearInterval(glitchInterval);
        setDisplayText(text);
        setIsGlitching(false);
      }
    }, settings.duration * 100);
  };

  useEffect(() => {
    if (autoGlitch) {
      const interval = setInterval(triggerGlitch, autoGlitchInterval);
      return () => clearInterval(interval);
    }
  }, [autoGlitch, autoGlitchInterval]);

  const glitchVariants = {
    normal: {
      x: 0,
      textShadow: 'none',
      filter: 'none'
    },
    glitch: {
      x: [0, -2, 2, -1, 1, 0],
      textShadow: [
        'none',
        '2px 0 #ff0000, -2px 0 #00ff00',
        '-2px 0 #ff0000, 2px 0 #00ff00',
        '1px 0 #ff0000, -1px 0 #00ff00',
        'none'
      ],
      filter: [
        'none',
        'hue-rotate(90deg)',
        'hue-rotate(180deg)',
        'hue-rotate(270deg)',
        'none'
      ],
      transition: {
        duration: settings.duration,
        times: [0, 0.2, 0.4, 0.6, 0.8, 1],
        ease: 'easeInOut'
      }
    }
  };

  return (
    <motion.span
      className={`inline-block relative ${className}`}
      variants={glitchVariants}
      animate={isGlitching ? 'glitch' : 'normal'}
      onMouseEnter={triggerOnHover ? triggerGlitch : undefined}
      style={{ cursor: triggerOnHover ? 'pointer' : 'default' }}
    >
      {displayText}
      
      {/* Glitch overlay effects */}
      {isGlitching && (
        <>
          <motion.span
            className="absolute top-0 left-0 text-red-500 opacity-70"
            style={{ clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)' }}
            animate={{
              x: [-2, 2, -1],
              transition: { duration: 0.1, repeat: Infinity }
            }}
          >
            {displayText}
          </motion.span>
          <motion.span
            className="absolute top-0 left-0 text-green-500 opacity-70"
            style={{ clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)' }}
            animate={{
              x: [2, -2, 1],
              transition: { duration: 0.1, repeat: Infinity }
            }}
          >
            {displayText}
          </motion.span>
        </>
      )}
    </motion.span>
  );
}
