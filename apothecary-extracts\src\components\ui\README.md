# UI Components Library

A comprehensive collection of React components built with TypeScript, Framer Motion, and Tailwind CSS for modern cannabis business websites.

## 🚀 Features

- **Fully Typed**: Complete TypeScript support with proper interfaces
- **Animated**: Smooth animations powered by Framer Motion
- **Responsive**: Mobile-first design that works on all devices
- **Accessible**: Built with accessibility best practices
- **Customizable**: Highly configurable with props
- **Performance Optimized**: Efficient animations and lazy loading

## 📦 Components

### Foundation Components

#### TextWithGlitchEffect
Animated text component with customizable glitch effects.

```tsx
<TextWithGlitchEffect 
  text="Glitch Text"
  glitchIntensity="medium"
  triggerOnHover={true}
  autoGlitch={false}
/>
```

#### AnnouncementBar
Dismissible announcement bar with different variants and CTA support.

```tsx
<AnnouncementBar
  message="Special offer available!"
  ctaText="Shop Now"
  ctaLink="/products"
  type="promotion"
  dismissible={true}
/>
```

#### FeatureIcons
Grid of animated feature icons with hover effects.

```tsx
<FeatureIcons 
  features={features}
  layout="grid"
  animationDelay={0.1}
  hoverEffect={true}
/>
```

### Layout Components

#### Tabs
Flexible tabbed interface with multiple variants.

```tsx
<Tabs 
  tabs={tabs}
  variant="soft"
  orientation="horizontal"
  onTabChange={(tabId) => console.log(tabId)}
/>
```

#### GridCards
Responsive grid of cards with animations and hover effects.

```tsx
<GridCards 
  cards={cards}
  columns={3}
  cardVariant="elevated"
  animationDelay={0.1}
/>
```

#### SplitMediaText
Split layout component with media and text content.

```tsx
<SplitMediaText
  title="Our Story"
  subtitle="About Us"
  content={<p>Content here...</p>}
  media={{ type: 'image', src: '/image.jpg' }}
  layout="reversed"
  cta={{ text: 'Learn More', href: '/about' }}
/>
```

#### FAQAccordion
Animated accordion component for FAQs.

```tsx
<FAQAccordion 
  faqs={faqs}
  allowMultiple={true}
  variant="bordered"
/>
```

### Interactive Components

#### CardSlider
Auto-scrolling card slider with navigation controls.

```tsx
<CardSlider 
  cards={cards}
  autoScroll={true}
  slidesToShow={3}
  showDots={true}
  showArrows={true}
/>
```

#### FilterableStrainCards
Advanced filterable and sortable card grid for cannabis strains.

```tsx
<FilterableStrainCards 
  strains={strains}
  showFilters={true}
  defaultFilter="all"
  sortBy="name"
/>
```

### Advanced Showcase Components

#### MasonryGrid
Pinterest-style masonry grid layout.

```tsx
<MasonryGrid 
  items={items}
  columns={3}
  gap={16}
/>
```

#### CarouselWithThumbnails
Full-featured carousel with thumbnail navigation.

```tsx
<CarouselWithThumbnails 
  items={items}
  autoPlay={true}
  showThumbnails={true}
  showArrows={true}
/>
```

#### VerticalTimeline
Animated timeline component for company history or processes.

```tsx
<VerticalTimeline 
  events={events}
  animate={true}
  alternating={true}
  showImages={true}
/>
```

### Hero & Parallax Components

#### HeroVideoLoop
Full-screen hero section with video background and CTAs.

```tsx
<HeroVideoLoop
  headline="Your Headline"
  subheadline="Your subheadline"
  videoSrc="/video.mp4"
  ctas={[
    { text: 'Shop Now', href: '/products', variant: 'primary' },
    { text: 'Learn More', href: '/about', variant: 'outline' }
  ]}
  overlayOpacity={0.4}
  showScrollIndicator={true}
/>
```

#### ParallaxBanner
Parallax scrolling banner with content overlay.

```tsx
<ParallaxBanner
  backgroundImage="/background.jpg"
  height="h-96"
  parallaxSpeed={0.5}
  overlayOpacity={0.4}
>
  <div>Your content here</div>
</ParallaxBanner>
```

#### ScrollFadeIn
Scroll-triggered fade-in animations.

```tsx
<ScrollFadeIn 
  direction="up"
  distance={50}
  duration={0.6}
  delay={0}
>
  <div>Content to animate</div>
</ScrollFadeIn>
```

## 🎨 Design System

### Colors
- **Primary**: Deep Forest Green (#1B4332)
- **Background**: Warm Cream (#F8F6F0)
- **Accent**: Rich Gold (#D4A574)
- **Text**: Charcoal (#2D3436)
- **Supporting**: Sage Green (#95A99C)

### Typography
- **Primary Font**: Inter (sans-serif)
- **Secondary Font**: Playfair Display (serif)

### Spacing
- Uses Tailwind's spacing scale with custom CSS variables
- Consistent gap and padding throughout components

## 🛠 Installation

1. Ensure you have the required dependencies:
```bash
npm install framer-motion next react react-dom
npm install -D @types/react @types/react-dom typescript tailwindcss
```

2. Copy the components from `/src/components/ui/` to your project

3. Import and use:
```tsx
import { TextWithGlitchEffect, AnnouncementBar } from '@/components/ui';
```

## 📱 Responsive Design

All components are built with mobile-first responsive design:
- **Mobile**: Single column layouts, touch-friendly interactions
- **Tablet**: Optimized for medium screens with appropriate spacing
- **Desktop**: Full multi-column layouts with hover effects

## ♿ Accessibility

Components follow WCAG guidelines:
- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- Color contrast compliance

## 🚀 Performance

- Optimized animations with `will-change` and `transform`
- Lazy loading for images and heavy components
- Efficient re-renders with proper React patterns
- Minimal bundle impact with tree-shaking support

## 🧪 Testing

Visit `/components-demo` to see all components in action with:
- Interactive examples
- Different configurations
- Responsive behavior
- Animation demonstrations

## 📄 License

Built for Apothecary Extracts - Premium Cannabis Components Library
