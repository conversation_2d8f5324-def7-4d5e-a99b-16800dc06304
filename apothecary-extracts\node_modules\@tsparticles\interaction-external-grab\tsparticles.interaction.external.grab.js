/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Grabber.js":
/*!*********************************!*\
  !*** ./dist/browser/Grabber.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Grabber: () => (/* binding */ Grabber)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Grab_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Grab.js */ \"./dist/browser/Options/Classes/Grab.js\");\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\n\nconst grabMode = \"grab\",\n  minDistance = 0,\n  minOpacity = 0;\nclass Grabber extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n  constructor(container, engine) {\n    super(container);\n    this._engine = engine;\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      grab = container.actualOptions.interactivity.modes.grab;\n    if (!grab) {\n      return;\n    }\n    container.retina.grabModeDistance = grab.distance * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions,\n      interactivity = options.interactivity;\n    if (!interactivity.modes.grab || !interactivity.events.onHover.enable || container.interactivity.status !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.mouseMoveEvent) {\n      return;\n    }\n    const mousePos = container.interactivity.mouse.position;\n    if (!mousePos) {\n      return;\n    }\n    const distance = container.retina.grabModeDistance;\n    if (!distance || distance < minDistance) {\n      return;\n    }\n    const query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n    for (const particle of query) {\n      const pos = particle.getPosition(),\n        pointDistance = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(pos, mousePos);\n      if (pointDistance > distance) {\n        continue;\n      }\n      const grabLineOptions = interactivity.modes.grab.links,\n        lineOpacity = grabLineOptions.opacity,\n        opacityLine = lineOpacity - pointDistance * lineOpacity / distance;\n      if (opacityLine <= minOpacity) {\n        continue;\n      }\n      const optColor = grabLineOptions.color ?? particle.options.links?.color;\n      if (!container.particles.grabLineColor && optColor) {\n        const linksOptions = interactivity.modes.grab.links;\n        container.particles.grabLineColor = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLinkRandomColor)(this._engine, optColor, linksOptions.blink, linksOptions.consent);\n      }\n      const colorLine = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLinkColor)(particle, undefined, container.particles.grabLineColor);\n      if (!colorLine) {\n        continue;\n      }\n      (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.drawGrab)(container, particle, colorLine, opacityLine, mousePos);\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    return events.onHover.enable && !!mouse.position && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(grabMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.grab) {\n      options.grab = new _Options_Classes_Grab_js__WEBPACK_IMPORTED_MODULE_2__.Grab();\n    }\n    for (const source of sources) {\n      options.grab.load(source?.grab);\n    }\n  }\n  reset() {}\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-grab/./dist/browser/Grabber.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Grab.js":
/*!**********************************************!*\
  !*** ./dist/browser/Options/Classes/Grab.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Grab: () => (/* binding */ Grab)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _GrabLinks_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GrabLinks.js */ \"./dist/browser/Options/Classes/GrabLinks.js\");\n\n\nclass Grab {\n  constructor() {\n    this.distance = 100;\n    this.links = new _GrabLinks_js__WEBPACK_IMPORTED_MODULE_1__.GrabLinks();\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    this.links.load(data.links);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-grab/./dist/browser/Options/Classes/Grab.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/GrabLinks.js":
/*!***************************************************!*\
  !*** ./dist/browser/Options/Classes/GrabLinks.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GrabLinks: () => (/* binding */ GrabLinks)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass GrabLinks {\n  constructor() {\n    this.blink = false;\n    this.consent = false;\n    this.opacity = 1;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.blink !== undefined) {\n      this.blink = data.blink;\n    }\n    if (data.color !== undefined) {\n      this.color = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n    }\n    if (data.consent !== undefined) {\n      this.consent = data.consent;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-grab/./dist/browser/Options/Classes/GrabLinks.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawGrab: () => (/* binding */ drawGrab),\n/* harmony export */   drawGrabLine: () => (/* binding */ drawGrabLine)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst defaultWidth = 0;\nfunction drawGrabLine(context, width, begin, end, colorLine, opacity) {\n  (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.drawLine)(context, begin, end);\n  context.strokeStyle = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromRgb)(colorLine, opacity);\n  context.lineWidth = width;\n  context.stroke();\n}\nfunction drawGrab(container, particle, lineColor, opacity, mousePos) {\n  container.canvas.draw(ctx => {\n    const beginPos = particle.getPosition();\n    drawGrabLine(ctx, particle.retina.linksWidth ?? defaultWidth, beginPos, mousePos, lineColor, opacity);\n  });\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-grab/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Grab: () => (/* reexport safe */ _Options_Classes_Grab_js__WEBPACK_IMPORTED_MODULE_1__.Grab),\n/* harmony export */   GrabLinks: () => (/* reexport safe */ _Options_Classes_GrabLinks_js__WEBPACK_IMPORTED_MODULE_2__.GrabLinks),\n/* harmony export */   loadExternalGrabInteraction: () => (/* binding */ loadExternalGrabInteraction)\n/* harmony export */ });\n/* harmony import */ var _Grabber_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Grabber.js */ \"./dist/browser/Grabber.js\");\n/* harmony import */ var _Options_Classes_Grab_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Grab.js */ \"./dist/browser/Options/Classes/Grab.js\");\n/* harmony import */ var _Options_Classes_GrabLinks_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/GrabLinks.js */ \"./dist/browser/Options/Classes/GrabLinks.js\");\n\nasync function loadExternalGrabInteraction(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addInteractor(\"externalGrab\", container => {\n    return Promise.resolve(new _Grabber_js__WEBPACK_IMPORTED_MODULE_0__.Grabber(container, engine));\n  }, refresh);\n}\n\n\n\n\n\n//# sourceURL=webpack://@tsparticles/interaction-external-grab/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});