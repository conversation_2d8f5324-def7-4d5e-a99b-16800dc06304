'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';
import { SplitMediaLayoutProps } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * SplitMediaLayout Component
 * 
 * Build split-screen layouts combining media and content for product education.
 * Features responsive design, animation triggers, and flexible media/content positioning.
 * 
 * @example
 * ```tsx
 * <SplitMediaLayout
 *   media={{
 *     type: 'image',
 *     src: '/images/extraction-process.jpg',
 *     alt: 'Cannabis extraction process'
 *   }}
 *   content={{
 *     title: 'Our Extraction Process',
 *     description: 'We use state-of-the-art CO2 extraction methods to preserve the full spectrum of cannabinoids and terpenes.',
 *     features: ['CO2 Extraction', 'Full Spectrum', 'Lab Tested', 'Solvent-Free'],
 *     cta: {
 *       text: 'Learn More',
 *       action: () => router.push('/process')
 *     }
 *   }}
 *   mediaPosition="left"
 *   splitRatio="50-50"
 *   verticalAlign="center"
 * />
 * ```
 */
const SplitMediaLayout: React.FC<SplitMediaLayoutProps> = ({
  media,
  content,
  mediaPosition = 'left',
  splitRatio = '50-50',
  verticalAlign = 'center',
  className = '',
  style,
}) => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Get split ratio classes
  const getSplitRatioClasses = () => {
    switch (splitRatio) {
      case '60-40':
        return mediaPosition === 'left' 
          ? { media: 'lg:w-3/5', content: 'lg:w-2/5' }
          : { media: 'lg:w-2/5', content: 'lg:w-3/5' };
      case '40-60':
        return mediaPosition === 'left'
          ? { media: 'lg:w-2/5', content: 'lg:w-3/5' }
          : { media: 'lg:w-3/5', content: 'lg:w-2/5' };
      case '50-50':
      default:
        return { media: 'lg:w-1/2', content: 'lg:w-1/2' };
    }
  };

  const ratioClasses = getSplitRatioClasses();

  // Get vertical alignment classes
  const getVerticalAlignClasses = () => {
    switch (verticalAlign) {
      case 'top':
        return 'items-start';
      case 'bottom':
        return 'items-end';
      case 'center':
      default:
        return 'items-center';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const mediaVariants = {
    hidden: { 
      opacity: 0, 
      x: mediaPosition === 'left' ? -50 : 50,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      x: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: brandAnimations.easing.smooth
      }
    }
  };

  const contentVariants = {
    hidden: { 
      opacity: 0, 
      x: mediaPosition === 'left' ? 50 : -50,
      y: 20
    },
    visible: { 
      opacity: 1, 
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: brandAnimations.easing.smooth,
        delay: 0.2
      }
    }
  };

  return (
    <motion.section
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={inView ? 'visible' : 'hidden'}
      className={`split-media-layout py-16 ${className}`}
      style={style}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex flex-col lg:flex-row ${getVerticalAlignClasses()} gap-8 lg:gap-12`}>
          {/* Media Section */}
          <motion.div
            variants={mediaVariants}
            className={`${ratioClasses.media} ${
              mediaPosition === 'right' ? 'lg:order-2' : 'lg:order-1'
            }`}
          >
            <div className="relative">
              {media.type === 'image' ? (
                <div 
                  className="relative aspect-[4/3] rounded-xl overflow-hidden"
                  style={{ boxShadow: brandShadows.large }}
                >
                  <Image
                    src={media.src}
                    alt={media.alt || ''}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  
                  {/* Overlay gradient */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
                </div>
              ) : (
                <div 
                  className="relative aspect-video rounded-xl overflow-hidden"
                  style={{ boxShadow: brandShadows.large }}
                >
                  <video
                    src={media.src}
                    controls
                    className="w-full h-full object-cover"
                    poster={media.alt} // Using alt as poster for video
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
              )}

              {/* Decorative elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-8 h-8 rounded-full"
                style={{ backgroundColor: brandColors.apothecary }}
                initial={{ scale: 0, opacity: 0 }}
                animate={inView ? { scale: 1, opacity: 0.6 } : { scale: 0, opacity: 0 }}
                transition={{ delay: 0.8, duration: 0.4 }}
              />
              <motion.div
                className="absolute -bottom-4 -left-4 w-6 h-6 rounded-full"
                style={{ backgroundColor: brandColors.gold[400] }}
                initial={{ scale: 0, opacity: 0 }}
                animate={inView ? { scale: 1, opacity: 0.4 } : { scale: 0, opacity: 0 }}
                transition={{ delay: 1, duration: 0.4 }}
              />
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            variants={contentVariants}
            className={`${ratioClasses.content} ${
              mediaPosition === 'right' ? 'lg:order-1' : 'lg:order-2'
            } flex flex-col justify-center`}
          >
            <div className="space-y-6">
              {/* Title */}
              <motion.h2
                className="text-3xl lg:text-4xl font-serif font-bold text-charcoal-800 leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                {content.title}
              </motion.h2>

              {/* Description */}
              <motion.p
                className="text-lg text-charcoal-600 leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                {content.description}
              </motion.p>

              {/* Features List */}
              {content.features && content.features.length > 0 && (
                <motion.div
                  className="space-y-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                >
                  {content.features.map((feature, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3"
                      initial={{ opacity: 0, x: -20 }}
                      animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                      transition={{ delay: 0.7 + index * 0.1, duration: 0.4 }}
                    >
                      <div 
                        className="w-2 h-2 rounded-full flex-shrink-0"
                        style={{ backgroundColor: brandColors.apothecary }}
                      />
                      <span className="text-charcoal-700 font-medium">
                        {feature}
                      </span>
                    </motion.div>
                  ))}
                </motion.div>
              )}

              {/* CTA Button */}
              {content.cta && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                >
                  <motion.button
                    onClick={content.cta.action}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-6 py-3 rounded-lg font-semibold text-cream-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: brandColors.primary[600],
                      focusRingColor: brandColors.primary[400],
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = brandColors.primary[700];
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = brandColors.primary[600];
                    }}
                  >
                    {content.cta.text}
                    <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </motion.button>
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default SplitMediaLayout;
