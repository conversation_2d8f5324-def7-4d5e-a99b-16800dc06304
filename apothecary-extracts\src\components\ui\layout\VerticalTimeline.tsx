'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';
import { VerticalTimelineProps, TimelineItem } from '../../../types/ui';
import { brandColors, brandShadows, brandAnimations } from '../../../styles/brand';

/**
 * VerticalTimeline Component
 * 
 * Create a vertical timeline for showing cultivation process, company history, or product journey.
 * Features alternating layout, scroll animations, and themed styling.
 * 
 * @example
 * ```tsx
 * <VerticalTimeline
 *   items={timelineItems}
 *   theme="cultivation"
 *   alternating={true}
 *   showLine={true}
 *   animateOnScroll={true}
 * />
 * ```
 */
const VerticalTimeline: React.FC<VerticalTimelineProps> = ({
  items,
  theme = 'cultivation',
  alternating = true,
  showLine = true,
  animateOnScroll = true,
  className = '',
  style,
}) => {
  // Get theme colors
  const getThemeColors = () => {
    switch (theme) {
      case 'cultivation':
        return {
          primary: brandColors.primary[600],
          secondary: brandColors.sage[400],
          accent: brandColors.gold[400],
        };
      case 'company':
        return {
          primary: brandColors.charcoal[700],
          secondary: brandColors.cream[300],
          accent: brandColors.apothecary,
        };
      case 'product':
        return {
          primary: brandColors.gold[600],
          secondary: brandColors.primary[300],
          accent: brandColors.sage[500],
        };
      default:
        return {
          primary: brandColors.primary[600],
          secondary: brandColors.sage[400],
          accent: brandColors.gold[400],
        };
    }
  };

  const themeColors = getThemeColors();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  return (
    <div className={`vertical-timeline relative ${className}`} style={style}>
      {/* Timeline Line */}
      {showLine && (
        <div 
          className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full"
          style={{ backgroundColor: themeColors.secondary }}
        />
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: '-100px' }}
        className="space-y-12"
      >
        {items.map((item, index) => (
          <TimelineItemComponent
            key={item.id}
            item={item}
            index={index}
            isAlternating={alternating}
            themeColors={themeColors}
            animateOnScroll={animateOnScroll}
          />
        ))}
      </motion.div>
    </div>
  );
};

// Individual Timeline Item Component
interface TimelineItemComponentProps {
  item: TimelineItem;
  index: number;
  isAlternating: boolean;
  themeColors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  animateOnScroll: boolean;
}

const TimelineItemComponent: React.FC<TimelineItemComponentProps> = ({
  item,
  index,
  isAlternating,
  themeColors,
  animateOnScroll,
}) => {
  const [ref, inView] = useInView({
    threshold: 0.3,
    triggerOnce: animateOnScroll,
  });

  const isLeft = isAlternating ? index % 2 === 0 : false;

  const itemVariants = {
    hidden: {
      opacity: 0,
      x: isLeft ? -50 : 50,
      y: 30,
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        ease: brandAnimations.easing.smooth,
      },
    },
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.5,
        ease: brandAnimations.easing.bounce,
        delay: 0.2,
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      variants={itemVariants}
      initial="hidden"
      animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}
      className={`relative flex items-center ${
        isAlternating
          ? isLeft
            ? 'flex-row-reverse'
            : 'flex-row'
          : 'flex-row'
      }`}
    >
      {/* Content */}
      <div 
        className={`w-full ${
          isAlternating ? 'md:w-5/12' : 'md:w-10/12 md:ml-16'
        } ${isLeft ? 'md:pr-8' : 'md:pl-8'}`}
      >
        <motion.div
          className="bg-cream-50 rounded-xl p-6 relative"
          style={{ boxShadow: brandShadows.soft }}
          whileHover={{ y: -4 }}
          transition={{ duration: 0.2 }}
        >
          {/* Arrow */}
          {isAlternating && (
            <div
              className={`absolute top-6 w-0 h-0 ${
                isLeft
                  ? 'right-0 border-l-8 border-l-cream-50 border-y-8 border-y-transparent'
                  : 'left-0 border-r-8 border-r-cream-50 border-y-8 border-y-transparent'
              }`}
            />
          )}

          {/* Date */}
          <div className="mb-3">
            <span 
              className="inline-block px-3 py-1 rounded-full text-sm font-medium text-cream-50"
              style={{ backgroundColor: themeColors.accent }}
            >
              {item.date}
            </span>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-charcoal-800 mb-3">
            {item.title}
          </h3>

          {/* Description */}
          <p className="text-charcoal-600 leading-relaxed mb-4">
            {item.description}
          </p>

          {/* Image */}
          {item.image && (
            <div className="relative h-48 rounded-lg overflow-hidden mb-4">
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="object-cover"
              />
            </div>
          )}
        </motion.div>
      </div>

      {/* Timeline Icon */}
      <motion.div
        variants={iconVariants}
        initial="hidden"
        animate={animateOnScroll ? (inView ? 'visible' : 'hidden') : 'visible'}
        className={`absolute ${
          isAlternating ? 'left-1/2 transform -translate-x-1/2' : 'left-6'
        } z-10`}
      >
        <div
          className="w-12 h-12 rounded-full flex items-center justify-center border-4 border-cream-50"
          style={{ backgroundColor: themeColors.primary }}
        >
          {item.icon ? (
            <div className="text-cream-50">
              {item.icon}
            </div>
          ) : (
            <div className="w-3 h-3 bg-cream-50 rounded-full" />
          )}
        </div>
      </motion.div>

      {/* Spacer for alternating layout */}
      {isAlternating && (
        <div className="w-5/12 hidden md:block" />
      )}
    </motion.div>
  );
};

export default VerticalTimeline;
