{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Navigation() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigationItems = [\n    { name: 'Home', href: '/' },\n    { name: 'Products', href: '/products' },\n    { name: 'Cultivars', href: '/cultivars' },\n    { name: 'Deals', href: '/deals' },\n    { name: 'Locations', href: '/locations' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <nav className=\"bg-cream-50 shadow-soft sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-2xl font-serif font-bold text-primary-800\">\n                Apothecary Extracts\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-charcoal-700 hover:text-primary-800 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-charcoal-700 hover:text-primary-800 hover:bg-cream-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-600\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                </svg>\n              ) : (\n                <svg\n                  className=\"block h-6 w-6\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                  aria-hidden=\"true\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 border-t border-cream-200\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-charcoal-700 hover:text-primary-800 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;sCAOpE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;6DAIN,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUf,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE5B,KAAK,IAAI;2BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAa9B", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/AgeVerification.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AgeVerificationProps {\n  onVerified: () => void;\n}\n\nexport default function AgeVerification({ onVerified }: AgeVerificationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Check if user has already verified their age\n    const hasVerified = localStorage.getItem('ageVerified');\n    if (!hasVerified) {\n      setIsVisible(true);\n    } else {\n      onVerified();\n    }\n  }, [onVerified]);\n\n  const handleVerification = (isOfAge: boolean) => {\n    if (isOfAge) {\n      localStorage.setItem('ageVerified', 'true');\n      setIsVisible(false);\n      onVerified();\n    } else {\n      // Redirect to educational resources or show message\n      window.location.href = 'https://www.samhsa.gov/marijuana';\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-primary-800 bg-opacity-95 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-cream-50 rounded-xl p-8 max-w-md w-full text-center shadow-medium\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-serif font-bold text-primary-800 mb-2\">\n            Age Verification Required\n          </h2>\n          <p className=\"text-charcoal-700 text-sm leading-relaxed\">\n            You must be 21 years of age or older to view this website and purchase cannabis products.\n            Please verify your age to continue.\n          </p>\n        </div>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={() => handleVerification(true)}\n            className=\"w-full bg-primary-800 text-cream-50 py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2\"\n          >\n            Yes, I&apos;m 21 or older\n          </button>\n          \n          <button\n            onClick={() => handleVerification(false)}\n            className=\"w-full bg-transparent text-charcoal-700 py-3 px-6 rounded-lg font-medium border border-charcoal-300 hover:bg-charcoal-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-charcoal-400 focus:ring-offset-2\"\n          >\n            No, I&apos;m under 21\n          </button>\n        </div>\n        \n        <div className=\"mt-6 pt-4 border-t border-charcoal-200\">\n          <p className=\"text-xs text-charcoal-500 leading-relaxed\">\n            By entering this website, you certify that you are of legal age to purchase cannabis products \n            in your jurisdiction. Cannabis products have not been evaluated by the FDA and are not intended \n            to diagnose, treat, cure, or prevent any disease.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQe,SAAS,gBAAgB,EAAE,UAAU,EAAwB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,CAAC,aAAa;YAChB,aAAa;QACf,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,eAAe;YACpC,aAAa;YACb;QACF,OAAO;YACL,oDAAoD;YACpD,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;;;;;;;8BAM3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/components/ComplianceDisclaimer.tsx"], "sourcesContent": ["export default function ComplianceDisclaimer() {\n  return (\n    <div className=\"bg-gold-100 border border-gold-400 rounded-lg p-4 mb-6\">\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-gold-600 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3\">\n          <h3 className=\"text-sm font-semibold text-gold-800 mb-2\">\n            Important Legal Information\n          </h3>\n          <div className=\"text-sm text-gold-700 space-y-2\">\n            <p>\n              <strong>Colorado State Compliance:</strong> This establishment is licensed by the State of Colorado \n              to sell cannabis products to adults 21 years of age and older. License #: [LICENSE_NUMBER]\n            </p>\n            <p>\n              <strong>Health & Safety:</strong> Cannabis products have not been evaluated by the FDA and are not \n              intended to diagnose, treat, cure, or prevent any disease. Keep out of reach of children and pets.\n            </p>\n            <p>\n              <strong>Consumption Guidelines:</strong> Do not operate vehicles or machinery after use. \n              Effects may be delayed with edible products. Start with low doses and wait before consuming more.\n            </p>\n            <p>\n              <strong>Legal Restrictions:</strong> Cannabis products may not be transported across state lines. \n              Consumption is prohibited in public places and federal properties.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAA+B,MAAK;wBAAe,SAAQ;kCACxE,cAAA,8OAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoN,UAAS;;;;;;;;;;;;;;;;8BAG5P,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAAmC;;;;;;;8CAG7C,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAGnC,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAAgC;;;;;;;8CAG1C,8OAAC;;sDACC,8OAAC;sDAAO;;;;;;wCAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/content/site-config.ts"], "sourcesContent": ["// Site-wide content configuration\n// This file centralizes content management for easier updates and consistency\n\nexport const siteConfig = {\n  brand: {\n    name: \"Apothecary Extracts\",\n    tagline: \"Premium Cannabis Excellence\",\n    description: \"Colorado's premier cannabis dispensary, committed to providing the highest quality products and exceptional customer service.\",\n    founded: \"2015\",\n    locations: [\"Colorado Springs\", \"Denver Metro\"]\n  },\n\n  meta: {\n    industry: \"Cannabis\",\n    services: [\n      \"Premium Cannabis Retail\",\n      \"Medical Cannabis\",\n      \"Cannabis Education\",\n      \"Expert Consultation\"\n    ],\n    compliance: {\n      license: \"Licensed Cannabis Retailer\",\n      ageRequirement: \"Must be 21+ to purchase\",\n      medicalCard: \"Valid medical card required for medical products\"\n    }\n  },\n\n  pages: {\n    home: {\n      seo: {\n        title: \"Apothecary Extracts | Premium Cannabis in Colorado\",\n        description: \"Discover Colorado's finest cannabis products at Apothecary Extracts. Premium flower, concentrates, edibles, and topicals with expert guidance and exceptional quality.\"\n      },\n      hero: {\n        headline: \"Premium Cannabis Excellence\",\n        subheadline: \"Discover Colorado's finest selection of cannabis products, expertly curated for quality, potency, and purity.\",\n        ctas: [\"Shop Products\", \"Find Locations\"]\n      }\n    },\n\n    deals: {\n      seo: {\n        title: \"Daily Deals & Specials | Apothecary Extracts\",\n        description: \"Save on premium cannabis products with our daily deals and weekly specials. New offers every day at Apothecary Extracts.\"\n      },\n      hero: {\n        headline: \"Daily Deals & Specials\",\n        subheadline: \"Discover exceptional value on premium cannabis products. New deals every day of the week.\"\n      }\n    },\n\n    cultivars: {\n      seo: {\n        title: \"Premium Cannabis Cultivars | Apothecary Extracts\",\n        description: \"Explore our curated selection of premium cannabis strains. Sativa, Indica, and Hybrid varieties with detailed genetics and effects information.\"\n      },\n      hero: {\n        headline: \"Premium Cultivars\",\n        subheadline: \"Explore our carefully curated selection of premium cannabis genetics, each strain selected for quality, potency, and unique characteristics.\"\n      }\n    },\n\n    products: {\n      seo: {\n        title: \"Cannabis Products | Apothecary Extracts\",\n        description: \"Browse our complete selection of premium cannabis products including flower, concentrates, edibles, and topicals. Lab-tested for quality and potency.\"\n      }\n    },\n\n    locations: {\n      seo: {\n        title: \"Dispensary Locations | Apothecary Extracts\",\n        description: \"Visit our Colorado cannabis dispensary locations. Find hours, directions, and contact information for Apothecary Extracts dispensaries.\"\n      }\n    },\n\n    about: {\n      seo: {\n        title: \"About Us | Apothecary Extracts\",\n        description: \"Learn about Apothecary Extracts' commitment to premium cannabis, quality cultivation, and exceptional customer service in Colorado.\"\n      }\n    }\n  },\n\n  contact: {\n    email: \"<EMAIL>\",\n    phone: \"1-800-555-HERB\",\n    social: {\n      instagram: \"@apothecaryextracts\",\n      facebook: \"ApothecaryExtracts\",\n      twitter: \"@ApothecaryExt\"\n    }\n  },\n\n  legal: {\n    compliance: [\n      \"Licensed Cannabis Retailer in Colorado\",\n      \"All products lab-tested for quality and potency\",\n      \"Must be 21+ with valid ID to purchase\",\n      \"Medical patients require valid medical card\",\n      \"Consumption prohibited in public areas\",\n      \"Keep out of reach of children and pets\"\n    ],\n    disclaimers: {\n      fda: \"These statements have not been evaluated by the FDA. This product is not intended to diagnose, treat, cure, or prevent any disease.\",\n      colorado: \"For use only by adults 21 years of age and older. Keep out of reach of children.\",\n      medical: \"Medical marijuana patients must have a valid medical marijuana card and ID.\"\n    }\n  },\n\n  navigation: {\n    primary: [\n      { name: 'Home', href: '/' },\n      { name: 'Products', href: '/products' },\n      { name: 'Cultivars', href: '/cultivars' },\n      { name: 'Deals', href: '/deals' },\n      { name: 'Locations', href: '/locations' },\n      { name: 'About', href: '/about' }\n    ],\n    footer: {\n      quickLinks: [\n        { name: 'Products', href: '/products' },\n        { name: 'Cultivars', href: '/cultivars' },\n        { name: 'Deals', href: '/deals' },\n        { name: 'Locations', href: '/locations' },\n        { name: 'About Us', href: '/about' }\n      ],\n      legal: [\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Compliance', href: '/compliance' }\n      ]\n    }\n  }\n};\n\n// Product categories configuration\nexport const productCategories = [\n  {\n    name: 'Premium Flower',\n    slug: 'flower',\n    description: 'Hand-selected, top-shelf cannabis flower with exceptional quality and potency.',\n    features: ['Lab Tested', 'Organic Grown', 'Various Strains'],\n    color: 'from-primary-600 to-primary-700'\n  },\n  {\n    name: 'Concentrates & Extracts',\n    slug: 'concentrates',\n    description: 'Pure, potent concentrates including wax, shatter, and live resin.',\n    features: ['High Potency', 'Pure Extraction', 'Multiple Forms'],\n    color: 'from-gold-500 to-gold-600'\n  },\n  {\n    name: 'Edibles',\n    slug: 'edibles',\n    description: 'Delicious, precisely dosed edibles for a controlled cannabis experience.',\n    features: ['Precise Dosing', 'Great Taste', 'Long Lasting'],\n    color: 'from-sage-400 to-sage-500'\n  },\n  {\n    name: 'Topicals',\n    slug: 'topicals',\n    description: 'Therapeutic cannabis topicals for localized relief and wellness.',\n    features: ['Non-Psychoactive', 'Therapeutic', 'Natural Relief'],\n    color: 'from-cream-400 to-cream-500'\n  }\n];\n\n// Weekly deals configuration\nexport const weeklyDeals = [\n  {\n    day: \"Monday\",\n    title: \"Medical Monday\",\n    discount: \"20% off all edibles\",\n    description: \"Start your week with premium edibles at exceptional value. Perfect for consistent, controlled dosing.\",\n    icon: \"🍃\",\n    color: \"from-primary-600 to-primary-700\"\n  },\n  {\n    day: \"Tuesday\",\n    title: \"Topical Tuesday\", \n    discount: \"15% off topicals & wellness\",\n    description: \"Therapeutic cannabis topicals for localized relief and wellness without psychoactive effects.\",\n    icon: \"💆\",\n    color: \"from-sage-400 to-sage-500\"\n  },\n  {\n    day: \"Wednesday\",\n    title: \"Concentrate Wednesday\",\n    discount: \"15% off all concentrates\",\n    description: \"Premium extracts including live resin, rosin, and shatter. High potency for experienced users.\",\n    icon: \"💎\",\n    color: \"from-gold-500 to-gold-600\"\n  },\n  {\n    day: \"Thursday\",\n    title: \"Flower Thursday\",\n    discount: \"10% off premium flower\",\n    description: \"Hand-selected, top-shelf cannabis flower with exceptional quality and diverse strain selection.\",\n    icon: \"🌸\",\n    color: \"from-primary-500 to-primary-600\"\n  },\n  {\n    day: \"Friday\",\n    title: \"First-Timer Friday\",\n    discount: \"Free consultation + gift\",\n    description: \"New to cannabis? Get expert guidance and a complimentary pre-roll or edible with your first purchase.\",\n    icon: \"🎁\",\n    color: \"from-cream-400 to-cream-500\"\n  },\n  {\n    day: \"Weekend\",\n    title: \"Weekend Warriors\",\n    discount: \"Bundle deals available\",\n    description: \"Mix and match products for weekend adventures. Ask our budtenders about current bundle options.\",\n    icon: \"🏔️\",\n    color: \"from-charcoal-400 to-charcoal-500\"\n  }\n];\n\nexport default siteConfig;\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,8EAA8E;;;;;;;AAEvE,MAAM,aAAa;IACxB,OAAO;QACL,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;YAAC;YAAoB;SAAe;IACjD;IAEA,MAAM;QACJ,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,YAAY;YACV,SAAS;YACT,gBAAgB;YAChB,aAAa;QACf;IACF;IAEA,OAAO;QACL,MAAM;YACJ,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;YACA,MAAM;gBACJ,UAAU;gBACV,aAAa;gBACb,MAAM;oBAAC;oBAAiB;iBAAiB;YAC3C;QACF;QAEA,OAAO;YACL,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;YAC<PERSON>,MAAM;gBACJ,UAAU;gBACV,aAAa;YACf;QACF;QAEA,WAAW;YACT,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;YACA,MAAM;gBACJ,UAAU;gBACV,aAAa;YACf;QACF;QAEA,UAAU;YACR,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;QACF;QAEA,WAAW;YACT,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;QACF;QAEA,OAAO;YACL,KAAK;gBACH,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,SAAS;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YACN,WAAW;YACX,UAAU;YACV,SAAS;QACX;IACF;IAEA,OAAO;QACL,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX,KAAK;YACL,UAAU;YACV,SAAS;QACX;IACF;IAEA,YAAY;QACV,SAAS;YACP;gBAAE,MAAM;gBAAQ,MAAM;YAAI;YAC1B;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAS,MAAM;YAAS;SACjC;QACD,QAAQ;YACN,YAAY;gBACV;oBAAE,MAAM;oBAAY,MAAM;gBAAY;gBACtC;oBAAE,MAAM;oBAAa,MAAM;gBAAa;gBACxC;oBAAE,MAAM;oBAAS,MAAM;gBAAS;gBAChC;oBAAE,MAAM;oBAAa,MAAM;gBAAa;gBACxC;oBAAE,MAAM;oBAAY,MAAM;gBAAS;aACpC;YACD,OAAO;gBACL;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAc,MAAM;gBAAc;aAC3C;QACH;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAc;YAAiB;SAAkB;QAC5D,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAgB;YAAmB;SAAiB;QAC/D,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAkB;YAAe;SAAe;QAC3D,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YAAC;YAAoB;YAAe;SAAiB;QAC/D,OAAO;IACT;CACD;AAGM,MAAM,cAAc;IACzB;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///O:/VSCODE%20PROJECTS/2025%20NEW/Apothecary/apothecary-extracts/src/app/cultivars/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport Head from 'next/head';\nimport Navigation from '@/components/Navigation';\nimport AgeVerification from '@/components/AgeVerification';\nimport ComplianceDisclaimer from '@/components/ComplianceDisclaimer';\nimport { siteConfig } from '@/content/site-config';\n\nconst strainCategories = [\n  {\n    type: \"Sativa\",\n    description: \"Energizing and uplifting effects, ideal for daytime use\",\n    color: \"from-gold-500 to-gold-600\",\n    icon: \"☀️\"\n  },\n  {\n    type: \"Indica\", \n    description: \"Relaxing and sedating effects, perfect for evening use\",\n    color: \"from-primary-600 to-primary-700\",\n    icon: \"🌙\"\n  },\n  {\n    type: \"Hybrid\",\n    description: \"Balanced effects combining the best of both worlds\",\n    color: \"from-sage-400 to-sage-500\", \n    icon: \"⚖️\"\n  }\n];\n\nconst featuredStrains = [\n  {\n    name: \"Papaya Cake\",\n    type: \"Hybrid\",\n    thc: \"22-26%\",\n    cbd: \"<1%\",\n    effects: [\"Relaxed\", \"Euphoric\", \"Creative\"],\n    flavors: [\"Tropical\", \"Sweet\", \"Creamy\"],\n    description: \"A delightful hybrid that combines tropical sweetness with relaxing effects. Perfect for creative endeavors and social situations.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"Papaya × Wedding Cake\"\n  },\n  {\n    name: \"GMO Crasher\",\n    type: \"Indica\",\n    thc: \"24-28%\", \n    cbd: \"<1%\",\n    effects: [\"Sedating\", \"Relaxed\", \"Sleepy\"],\n    flavors: [\"Garlic\", \"Diesel\", \"Earthy\"],\n    description: \"A potent indica-dominant strain known for its unique savory profile and powerful relaxing effects. Ideal for evening use.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"GMO × Wedding Crasher\"\n  },\n  {\n    name: \"Pineapple Habanero\",\n    type: \"Sativa\",\n    thc: \"20-24%\",\n    cbd: \"<1%\", \n    effects: [\"Energetic\", \"Creative\", \"Focused\"],\n    flavors: [\"Pineapple\", \"Spicy\", \"Citrus\"],\n    description: \"An invigorating sativa with a unique sweet and spicy flavor profile. Excellent for daytime productivity and creative projects.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"Pineapple Express × Habanero Haze\"\n  },\n  {\n    name: \"Purple Sunset\",\n    type: \"Hybrid\",\n    thc: \"18-22%\",\n    cbd: \"1-2%\",\n    effects: [\"Balanced\", \"Calm\", \"Happy\"],\n    flavors: [\"Berry\", \"Grape\", \"Floral\"],\n    description: \"A beautifully balanced hybrid with stunning purple hues and a complex berry flavor profile. Perfect for any time of day.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"Purple Punch × Sunset Sherbet\"\n  },\n  {\n    name: \"Colorado Kush\",\n    type: \"Indica\",\n    thc: \"19-23%\",\n    cbd: \"<1%\",\n    effects: [\"Relaxed\", \"Sleepy\", \"Pain Relief\"],\n    flavors: [\"Pine\", \"Earthy\", \"Woody\"],\n    description: \"A classic Colorado strain with traditional kush characteristics. Known for its therapeutic benefits and classic cannabis flavor.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"Local Colorado Genetics\"\n  },\n  {\n    name: \"Citrus Burst\",\n    type: \"Sativa\",\n    thc: \"21-25%\",\n    cbd: \"<1%\",\n    effects: [\"Uplifting\", \"Energetic\", \"Social\"],\n    flavors: [\"Citrus\", \"Lemon\", \"Orange\"],\n    description: \"A bright and energizing sativa with an explosive citrus flavor. Perfect for social gatherings and outdoor activities.\",\n    image: \"/api/placeholder/300/200\",\n    genetics: \"Tangie × Super Lemon Haze\"\n  }\n];\n\nexport default function CultivarsPage() {\n  const [isAgeVerified, setIsAgeVerified] = useState(false);\n  const [selectedCategory, setSelectedCategory] = useState(\"All\");\n  const { cultivars: pageConfig } = siteConfig.pages;\n\n  const filteredStrains = selectedCategory === \"All\" \n    ? featuredStrains \n    : featuredStrains.filter(strain => strain.type === selectedCategory);\n\n  return (\n    <>\n      <Head>\n        <title>{pageConfig.seo.title}</title>\n        <meta name=\"description\" content={pageConfig.seo.description} />\n        <meta property=\"og:title\" content={pageConfig.seo.title} />\n        <meta property=\"og:description\" content={pageConfig.seo.description} />\n        <meta name=\"age-restriction\" content=\"21+\" />\n      </Head>\n\n      {!isAgeVerified && (\n        <AgeVerification onVerified={() => setIsAgeVerified(true)} />\n      )}\n\n      {isAgeVerified && (\n        <div className=\"min-h-screen bg-cream-100\">\n          <Navigation />\n\n          <main>\n            {/* Hero Section */}\n            <section className=\"relative bg-gradient-to-br from-primary-800 via-primary-700 to-primary-600 text-cream-50 overflow-hidden py-24 lg:py-32\">\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute inset-0 bg-primary-900\"></div>\n              </div>\n              \n              <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n                <motion.h1 \n                  className=\"text-4xl md:text-6xl font-serif font-bold leading-tight mb-6\"\n                  initial={{ opacity: 0, y: -20 }} \n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.8 }}\n                >\n                  {pageConfig.hero.headline}\n                </motion.h1>\n\n                <motion.p\n                  className=\"text-xl md:text-2xl text-cream-200 mb-8 leading-relaxed max-w-4xl mx-auto\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.8, delay: 0.2 }}\n                >\n                  {pageConfig.hero.subheadline}\n                </motion.p>\n              </div>\n            </section>\n\n            {/* Compliance Disclaimer */}\n            <section className=\"py-8 bg-cream-100\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <ComplianceDisclaimer />\n              </div>\n            </section>\n\n            {/* Strain Categories */}\n            <section className=\"py-16 bg-cream-100\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <motion.div \n                  className=\"text-center mb-12\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6 }}\n                  viewport={{ once: true }}\n                >\n                  <h2 className=\"text-3xl font-serif font-bold text-primary-800 mb-4\">\n                    Cannabis Categories\n                  </h2>\n                  <p className=\"text-lg text-charcoal-600 max-w-3xl mx-auto\">\n                    Understanding the differences between Sativa, Indica, and Hybrid strains helps you choose the perfect cannabis for your needs.\n                  </p>\n                </motion.div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n                  {strainCategories.map((category, index) => (\n                    <motion.div\n                      key={category.type}\n                      className=\"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300\"\n                      initial={{ opacity: 0, y: 20 }}\n                      whileInView={{ opacity: 1, y: 0 }}\n                      transition={{ \n                        duration: 0.3,\n                        delay: index * 0.1 \n                      }}\n                      viewport={{ once: true }}\n                    >\n                      <div className={`h-20 bg-gradient-to-br ${category.color} flex items-center justify-center`}>\n                        <div className=\"text-center text-cream-50\">\n                          <div className=\"text-2xl mb-1\">{category.icon}</div>\n                          <div className=\"font-semibold\">{category.type}</div>\n                        </div>\n                      </div>\n                      <div className=\"p-6\">\n                        <p className=\"text-charcoal-600 text-sm leading-relaxed\">\n                          {category.description}\n                        </p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n\n                {/* Filter Buttons */}\n                <motion.div \n                  className=\"flex flex-wrap justify-center gap-4 mb-12\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.3 }}\n                  viewport={{ once: true }}\n                >\n                  {[\"All\", \"Sativa\", \"Indica\", \"Hybrid\"].map((category) => (\n                    <button\n                      key={category}\n                      onClick={() => setSelectedCategory(category)}\n                      className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${\n                        selectedCategory === category\n                          ? \"bg-primary-800 text-cream-50\"\n                          : \"bg-cream-50 text-primary-800 hover:bg-primary-100\"\n                      }`}\n                    >\n                      {category}\n                    </button>\n                  ))}\n                </motion.div>\n              </div>\n            </section>\n\n            {/* Featured Strains */}\n            <section className=\"py-20 bg-cream-200\">\n              <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                <motion.div \n                  className=\"text-center mb-16\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6 }}\n                  viewport={{ once: true }}\n                >\n                  <h2 className=\"text-4xl font-serif font-bold text-primary-800 mb-4\">\n                    Featured Strains\n                  </h2>\n                  <p className=\"text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed\">\n                    Discover our current selection of premium cannabis strains, each with unique characteristics and carefully documented genetics.\n                  </p>\n                </motion.div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                  {filteredStrains.map((strain, index) => (\n                    <motion.div\n                      key={strain.name}\n                      className=\"bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300\"\n                      initial={{ opacity: 0, y: 20 }}\n                      whileInView={{ opacity: 1, y: 0 }}\n                      whileHover={{ scale: 1.02, y: -4 }}\n                      transition={{ \n                        duration: 0.3,\n                        delay: index * 0.1 \n                      }}\n                      viewport={{ once: true }}\n                    >\n                      <div className=\"h-48 bg-gradient-to-br from-primary-200 to-primary-300 relative overflow-hidden\">\n                        <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                        <div className=\"absolute top-4 left-4\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                            strain.type === 'Sativa' ? 'bg-gold-500 text-cream-50' :\n                            strain.type === 'Indica' ? 'bg-primary-700 text-cream-50' :\n                            'bg-sage-500 text-cream-50'\n                          }`}>\n                            {strain.type}\n                          </span>\n                        </div>\n                      </div>\n\n                      <div className=\"p-6\">\n                        <h3 className=\"text-xl font-semibold text-primary-800 mb-2\">\n                          {strain.name}\n                        </h3>\n                        <p className=\"text-sm text-charcoal-500 mb-3\">\n                          {strain.genetics}\n                        </p>\n                        <p className=\"text-charcoal-600 text-sm mb-4 leading-relaxed\">\n                          {strain.description}\n                        </p>\n\n                        <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm\">\n                          <div>\n                            <span className=\"font-medium text-primary-800\">THC:</span>\n                            <span className=\"ml-1 text-charcoal-600\">{strain.thc}</span>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-primary-800\">CBD:</span>\n                            <span className=\"ml-1 text-charcoal-600\">{strain.cbd}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"mb-4\">\n                          <div className=\"text-sm font-medium text-primary-800 mb-2\">Effects:</div>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {strain.effects.map((effect) => (\n                              <span\n                                key={effect}\n                                className=\"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full\"\n                              >\n                                {effect}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n\n                        <div>\n                          <div className=\"text-sm font-medium text-primary-800 mb-2\">Flavors:</div>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {strain.flavors.map((flavor) => (\n                              <span\n                                key={flavor}\n                                className=\"px-2 py-1 bg-gold-100 text-gold-700 text-xs rounded-full\"\n                              >\n                                {flavor}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </section>\n\n            {/* CTA Section */}\n            <section className=\"py-20 bg-primary-800\">\n              <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6 }}\n                  viewport={{ once: true }}\n                >\n                  <h2 className=\"text-3xl font-serif font-bold text-cream-50 mb-4\">\n                    Find Your Perfect Strain\n                  </h2>\n                  <p className=\"text-xl text-cream-200 mb-8\">\n                    Visit our dispensary to explore these strains in person and get expert recommendations from our knowledgeable budtenders.\n                  </p>\n                  <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                    <a\n                      href=\"/locations\"\n                      className=\"inline-flex items-center px-8 py-4 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200\"\n                    >\n                      Visit Our Dispensary\n                    </a>\n                    <a\n                      href=\"/products\"\n                      className=\"inline-flex items-center px-8 py-4 bg-transparent text-cream-50 font-semibold rounded-lg border-2 border-cream-50 hover:bg-cream-50 hover:text-primary-800 transition-colors duration-200\"\n                    >\n                      Browse All Products\n                    </a>\n                  </div>\n                </motion.div>\n              </div>\n            </section>\n          </main>\n\n          {/* Footer */}\n          <footer className=\"bg-charcoal-800 text-cream-100 py-8\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n              <p className=\"text-cream-400 text-sm\">\n                © 2025 Apothecary Extracts. All rights reserved. | Licensed Cannabis Retailer | Must be 21+ to purchase\n              </p>\n            </div>\n          </footer>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAW;YAAY;SAAW;QAC5C,SAAS;YAAC;YAAY;YAAS;SAAS;QACxC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAY;YAAW;SAAS;QAC1C,SAAS;YAAC;YAAU;YAAU;SAAS;QACvC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAa;YAAY;SAAU;QAC7C,SAAS;YAAC;YAAa;YAAS;SAAS;QACzC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAY;YAAQ;SAAQ;QACtC,SAAS;YAAC;YAAS;YAAS;SAAS;QACrC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAW;YAAU;SAAc;QAC7C,SAAS;YAAC;YAAQ;YAAU;SAAQ;QACpC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,SAAS;YAAC;YAAa;YAAa;SAAS;QAC7C,SAAS;YAAC;YAAU;YAAS;SAAS;QACtC,aAAa;QACb,OAAO;QACP,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,WAAW,UAAU,EAAE,GAAG,gIAAA,CAAA,aAAU,CAAC,KAAK;IAElD,MAAM,kBAAkB,qBAAqB,QACzC,kBACA,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;IAErD,qBACE;;0BACE,8OAAC,oKAAA,CAAA,UAAI;;kCACH,8OAAC;kCAAO,WAAW,GAAG,CAAC,KAAK;;;;;;kCAC5B,8OAAC;wBAAK,MAAK;wBAAc,SAAS,WAAW,GAAG,CAAC,WAAW;;;;;;kCAC5D,8OAAC;wBAAK,UAAS;wBAAW,SAAS,WAAW,GAAG,CAAC,KAAK;;;;;;kCACvD,8OAAC;wBAAK,UAAS;wBAAiB,SAAS,WAAW,GAAG,CAAC,WAAW;;;;;;kCACnE,8OAAC;wBAAK,MAAK;wBAAkB,SAAQ;;;;;;;;;;;;YAGtC,CAAC,+BACA,8OAAC,qIAAA,CAAA,UAAe;gBAAC,YAAY,IAAM,iBAAiB;;;;;;YAGrD,+BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,UAAU;;;;;kCAEX,8OAAC;;0CAEC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;0DAE3B,WAAW,IAAI,CAAC,QAAQ;;;;;;0DAG3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DAEvC,WAAW,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;;;;;;0CAKzB,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;;8DAEvB,8OAAC;oDAAG,WAAU;8DAAsD;;;;;;8DAGpE,8OAAC;oDAAE,WAAU;8DAA8C;;;;;;;;;;;;sDAK7D,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDACV,UAAU;wDACV,OAAO,QAAQ;oDACjB;oDACA,UAAU;wDAAE,MAAM;oDAAK;;sEAEvB,8OAAC;4DAAI,WAAW,CAAC,uBAAuB,EAAE,SAAS,KAAK,CAAC,iCAAiC,CAAC;sEACzF,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiB,SAAS,IAAI;;;;;;kFAC7C,8OAAC;wEAAI,WAAU;kFAAiB,SAAS,IAAI;;;;;;;;;;;;;;;;;sEAGjD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;;;;;;;mDAlBpB,SAAS,IAAI;;;;;;;;;;sDA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;sDAEtB;gDAAC;gDAAO;gDAAU;gDAAU;6CAAS,CAAC,GAAG,CAAC,CAAC,yBAC1C,8OAAC;oDAEC,SAAS,IAAM,oBAAoB;oDACnC,WAAW,CAAC,gEAAgE,EAC1E,qBAAqB,WACjB,iCACA,qDACJ;8DAED;mDARI;;;;;;;;;;;;;;;;;;;;;0CAgBf,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;;8DAEvB,8OAAC;oDAAG,WAAU;8DAAsD;;;;;;8DAGpE,8OAAC;oDAAE,WAAU;8DAA8D;;;;;;;;;;;;sDAK7E,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,YAAY;wDACV,UAAU;wDACV,OAAO,QAAQ;oDACjB;oDACA,UAAU;wDAAE,MAAM;oDAAK;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,IAAI,KAAK,WAAW,8BAC3B,OAAO,IAAI,KAAK,WAAW,iCAC3B,6BACA;kFACC,OAAO,IAAI;;;;;;;;;;;;;;;;;sEAKlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,OAAO,IAAI;;;;;;8EAEd,8OAAC;oEAAE,WAAU;8EACV,OAAO,QAAQ;;;;;;8EAElB,8OAAC;oEAAE,WAAU;8EACV,OAAO,WAAW;;;;;;8EAGrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAA+B;;;;;;8FAC/C,8OAAC;oFAAK,WAAU;8FAA0B,OAAO,GAAG;;;;;;;;;;;;sFAEtD,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAA+B;;;;;;8FAC/C,8OAAC;oFAAK,WAAU;8FAA0B,OAAO,GAAG;;;;;;;;;;;;;;;;;;8EAIxD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA4C;;;;;;sFAC3D,8OAAC;4EAAI,WAAU;sFACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,8OAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;;;;;;8EASb,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAA4C;;;;;;sFAC3D,8OAAC;4EAAI,WAAU;sFACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,8OAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;;;;;;;;;;;;;mDAjEV,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;0CAiF1B,8OAAC;gCAAQ,WAAU;0CACjB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;;0DAEvB,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}]}