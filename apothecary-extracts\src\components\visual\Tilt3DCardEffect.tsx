'use client';

import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Tilt3DCardEffectProps } from '../../types/visual';
import { brandShadows, brandAnimations } from '../../styles/brand';

/**
 * Tilt3DCardEffect Component
 * 
 * Increases interactivity and realism for cards or strain showcases.
 * Features 3D tilt effects based on mouse position with dynamic shadows.
 * 
 * @example
 * ```tsx
 * <Tilt3DCardEffect maxTilt={15} addDynamicShadow={true}>
 *   <div className="strain-card">
 *     <h3>Blue Dream</h3>
 *     <p>Premium Sativa Hybrid</p>
 *   </div>
 * </Tilt3DCardEffect>
 * ```
 */
const Tilt3DCardEffect: React.FC<Tilt3DCardEffectProps> = ({
  children,
  className = '',
  style,
  maxTilt = 10,
  perspective = 1000,
  scale = 1.02,
  speed = 300,
  addDynamicShadow = true,
  reset = true,
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [tiltValues, setTiltValues] = useState({ rotateX: 0, rotateY: 0 });
  const [isHovered, setIsHovered] = useState(false);

  // Calculate tilt based on mouse position
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Calculate mouse position relative to card center
    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;

    // Calculate tilt angles (inverted for natural feel)
    const rotateY = (mouseX / (rect.width / 2)) * maxTilt;
    const rotateX = -(mouseY / (rect.height / 2)) * maxTilt;

    setTiltValues({ rotateX, rotateY });
  };

  // Reset tilt on mouse leave
  const handleMouseLeave = () => {
    setIsHovered(false);
    if (reset) {
      setTiltValues({ rotateX: 0, rotateY: 0 });
    }
  };

  // Set hover state on mouse enter
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  // Get dynamic shadow based on tilt direction
  const getDynamicShadow = () => {
    if (!addDynamicShadow || !isHovered) return brandShadows.medium;

    const { rotateX, rotateY } = tiltValues;
    
    // Calculate shadow offset based on tilt
    const shadowX = rotateY * 0.5;
    const shadowY = rotateX * 0.5;
    const shadowBlur = Math.abs(rotateX) + Math.abs(rotateY) + 10;
    
    return `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, 0.2)`;
  };

  return (
    <motion.div
      ref={cardRef}
      className={`tilt-3d-card ${className}`}
      style={{
        perspective: `${perspective}px`,
        transformStyle: 'preserve-3d',
        ...style,
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      animate={{
        rotateX: tiltValues.rotateX,
        rotateY: tiltValues.rotateY,
        scale: isHovered ? scale : 1,
        boxShadow: getDynamicShadow(),
      }}
      transition={{
        duration: speed / 1000,
        ease: brandAnimations.easing.smooth,
      }}
    >
      {children}
    </motion.div>
  );
};

export default Tilt3DCardEffect;
