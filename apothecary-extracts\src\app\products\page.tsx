'use client';

import { useState } from 'react';
import Navigation from '@/components/Navigation';

// Sample product data
const products = [
  {
    id: 1,
    name: 'Blue Dream',
    category: 'flower',
    type: 'Hybrid',
    thc: '18-22%',
    cbd: '0.1-0.3%',
    price: 45,
    priceUnit: '1/8 oz',
    image: '/api/placeholder/300/300',
    description: 'A balanced hybrid with sweet berry aroma and cerebral, full-body effects.',
    effects: ['Creative', 'Euphoric', 'Relaxed'],
    flavors: ['Berry', 'Sweet', 'Vanilla'],
    inStock: true,
    featured: true
  },
  {
    id: 2,
    name: 'Live Resin Cart - OG Kush',
    category: 'concentrates',
    type: 'Indica',
    thc: '85-90%',
    cbd: '0.1%',
    price: 65,
    priceUnit: '0.5g',
    image: '/api/placeholder/300/300',
    description: 'Premium live resin cartridge with authentic OG Kush flavor profile.',
    effects: ['Relaxed', 'Sleepy', 'Happy'],
    flavors: ['Earthy', 'Pine', 'Lemon'],
    inStock: true,
    featured: false
  },
  {
    id: 3,
    name: 'Gummy Bears - Mixed Berry',
    category: 'edibles',
    type: 'Hybrid',
    thc: '10mg each',
    cbd: '0mg',
    price: 25,
    priceUnit: '10-pack',
    image: '/api/placeholder/300/300',
    description: 'Delicious mixed berry gummies with precise 10mg THC dosing.',
    effects: ['Happy', 'Relaxed', 'Euphoric'],
    flavors: ['Berry', 'Sweet', 'Fruity'],
    inStock: true,
    featured: true
  },
  {
    id: 4,
    name: 'Pain Relief Balm',
    category: 'topicals',
    type: 'CBD',
    thc: '0mg',
    cbd: '200mg',
    price: 35,
    priceUnit: '2oz jar',
    image: '/api/placeholder/300/300',
    description: 'Non-psychoactive topical balm for localized pain and inflammation relief.',
    effects: ['Pain Relief', 'Anti-inflammatory', 'Soothing'],
    flavors: ['Eucalyptus', 'Menthol', 'Lavender'],
    inStock: true,
    featured: false
  }
];

const categories = [
  { id: 'all', name: 'All Products', count: products.length },
  { id: 'flower', name: 'Flower', count: products.filter(p => p.category === 'flower').length },
  { id: 'concentrates', name: 'Concentrates', count: products.filter(p => p.category === 'concentrates').length },
  { id: 'edibles', name: 'Edibles', count: products.filter(p => p.category === 'edibles').length },
  { id: 'topicals', name: 'Topicals', count: products.filter(p => p.category === 'topicals').length }
];

export default function ProductsPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredProducts = products
    .filter(product => 
      (selectedCategory === 'all' || product.category === selectedCategory) &&
      (searchTerm === '' || product.name.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'thc':
          return parseFloat(b.thc) - parseFloat(a.thc);
        default:
          return a.name.localeCompare(b.name);
      }
    });

  return (
    <div className="min-h-screen bg-cream-100">
      <Navigation />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-serif font-bold text-primary-800 mb-4">
            Our Products
          </h1>
          <p className="text-xl text-charcoal-600 max-w-3xl">
            Explore our carefully curated selection of premium cannabis products, 
            all lab-tested for quality and potency.
          </p>
        </div>

        {/* Filters and Search */}
        <div className="bg-cream-50 rounded-xl p-6 mb-8 shadow-soft">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Categories */}
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-primary-800 mb-3">Categories</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      selectedCategory === category.id
                        ? 'bg-primary-800 text-cream-50'
                        : 'bg-cream-200 text-charcoal-700 hover:bg-primary-100'
                    }`}
                  >
                    {category.name} ({category.count})
                  </button>
                ))}
              </div>
            </div>

            {/* Search and Sort */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div>
                <label htmlFor="search" className="block text-sm font-medium text-charcoal-700 mb-1">
                  Search Products
                </label>
                <input
                  type="text"
                  id="search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by name..."
                  className="px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="sort" className="block text-sm font-medium text-charcoal-700 mb-1">
                  Sort By
                </label>
                <select
                  id="sort"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-charcoal-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-600 focus:border-transparent"
                >
                  <option value="name">Name A-Z</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="thc">THC Content</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map(product => (
            <div
              key={product.id}
              className="bg-cream-50 rounded-xl overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Product Image */}
              <div className="relative h-48 bg-gradient-to-br from-primary-100 to-primary-200">
                {product.featured && (
                  <div className="absolute top-3 left-3 bg-gold-500 text-primary-800 px-2 py-1 rounded-full text-xs font-semibold">
                    Featured
                  </div>
                )}
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    product.inStock 
                      ? 'bg-sage-200 text-sage-800' 
                      : 'bg-charcoal-200 text-charcoal-700'
                  }`}>
                    {product.inStock ? 'In Stock' : 'Out of Stock'}
                  </span>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-6">
                <div className="mb-2">
                  <span className="text-xs font-medium text-primary-600 uppercase tracking-wide">
                    {product.category}
                  </span>
                </div>
                
                <h3 className="text-lg font-semibold text-primary-800 mb-2">
                  {product.name}
                </h3>
                
                <p className="text-sm text-charcoal-600 mb-4 line-clamp-2">
                  {product.description}
                </p>

                {/* THC/CBD Info */}
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm">
                    <span className="text-charcoal-500">THC:</span>
                    <span className="font-medium text-charcoal-800 ml-1">{product.thc}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-charcoal-500">CBD:</span>
                    <span className="font-medium text-charcoal-800 ml-1">{product.cbd}</span>
                  </div>
                </div>

                {/* Effects */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {product.effects.slice(0, 3).map(effect => (
                      <span
                        key={effect}
                        className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                      >
                        {effect}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Price and CTA */}
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-2xl font-bold text-primary-800">${product.price}</span>
                    <span className="text-sm text-charcoal-500 ml-1">/{product.priceUnit}</span>
                  </div>
                  <button
                    disabled={!product.inStock}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      product.inStock
                        ? 'bg-primary-800 text-cream-50 hover:bg-primary-700'
                        : 'bg-charcoal-300 text-charcoal-500 cursor-not-allowed'
                    }`}
                  >
                    {product.inStock ? 'View Details' : 'Out of Stock'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl text-charcoal-300 mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-charcoal-700 mb-2">No products found</h3>
            <p className="text-charcoal-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </main>
    </div>
  );
}
